# APP前端升级功能集成指南

## 概述

本指南详细说明如何将在线升级功能集成到您的APP中，实现自动检查更新、下载、解压和安装功能。

## 集成步骤

### 1. 添加升级检查模块

#### JavaScript/Electron 应用

```javascript
class AppUpdater {
    constructor(options = {}) {
        this.apiUrl = options.apiUrl || 'https://your-domain.com/app_update_standalone.php';
        this.currentVersion = options.currentVersion || '1.0.0';
        this.platform = options.platform || this.detectPlatform();
        this.autoCheckInterval = options.autoCheckInterval || 3600000; // 1小时
        this.checkTimer = null;
    }

    // 检测平台
    detectPlatform() {
        const platform = process.platform;
        if (platform === 'win32') return 'windows';
        if (platform === 'darwin') return 'macos';
        if (platform === 'linux') return 'linux';
        return 'all';
    }

    // 启动自动检查
    startAutoCheck() {
        this.checkForUpdates(); // 立即检查一次
        this.checkTimer = setInterval(() => {
            this.checkForUpdates();
        }, this.autoCheckInterval);
    }

    // 停止自动检查
    stopAutoCheck() {
        if (this.checkTimer) {
            clearInterval(this.checkTimer);
            this.checkTimer = null;
        }
    }

    // 检查更新
    async checkForUpdates() {
        try {
            const url = `${this.apiUrl}?action=check&version=${this.currentVersion}&platform=${this.platform}`;
            const response = await fetch(url);
            const result = await response.json();

            if (result.success && result.data.has_update) {
                this.showUpdateDialog(result.data.update_info);
                return result.data.update_info;
            }
            
            return null;
        } catch (error) {
            console.error('检查更新失败:', error);
            return null;
        }
    }

    // 显示更新对话框
    showUpdateDialog(updateInfo) {
        // 创建更新对话框
        const dialog = this.createUpdateDialog(updateInfo);
        document.body.appendChild(dialog);
    }

    // 创建更新对话框
    createUpdateDialog(updateInfo) {
        const overlay = document.createElement('div');
        overlay.className = 'update-overlay';
        overlay.innerHTML = `
            <div class="update-dialog">
                <div class="update-icon">
                    <i class="fas fa-arrow-down"></i>
                </div>
                <div class="update-title">${updateInfo.title}</div>
                <div class="update-version">
                    <span class="version-badge">新版本可用</span>
                </div>
                <div class="update-description">${updateInfo.description}</div>
                <div class="update-size">
                    <small><i class="fas fa-hdd"></i> 预计下载大小: ~25.6 MB</small>
                </div>
                <div class="update-actions">
                    <button class="update-btn update-now" onclick="appUpdater.startDownload('${updateInfo.id}')">
                        <i class="fas fa-download"></i> 立即更新
                    </button>
                    <button class="update-btn update-later" onclick="appUpdater.closeUpdateDialog()">
                        <i class="fas fa-clock"></i> 稍后提醒
                    </button>
                </div>
                <div class="update-progress" style="display: none;">
                    <div class="progress-info">
                        <span class="progress-label">正在下载更新...</span>
                        <span class="progress-speed"></span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                    <div class="progress-text">0% - 0 MB / 25.6 MB</div>
                </div>
            </div>
        `;

        // 添加样式
        this.addUpdateStyles();
        
        return overlay;
    }

    // 开始下载
    async startDownload(updateId) {
        const dialog = document.querySelector('.update-dialog');
        const actions = dialog.querySelector('.update-actions');
        const progress = dialog.querySelector('.update-progress');
        
        actions.style.display = 'none';
        progress.style.display = 'block';

        try {
            // 获取下载链接
            const downloadUrl = `${this.apiUrl}?action=download&id=${updateId}&platform=${this.platform}`;
            
            // 开始下载（这里需要根据具体平台实现）
            await this.downloadAndInstall(downloadUrl);
            
        } catch (error) {
            console.error('下载失败:', error);
            alert('下载失败: ' + error.message);
        }
    }

    // 下载并安装（需要根据具体平台实现）
    async downloadAndInstall(downloadUrl) {
        // Electron 示例
        if (typeof require !== 'undefined') {
            const { ipcRenderer } = require('electron');
            
            // 发送下载请求到主进程
            ipcRenderer.send('start-download', downloadUrl);
            
            // 监听下载进度
            ipcRenderer.on('download-progress', (event, progress) => {
                this.updateProgress(progress);
            });
            
            // 监听下载完成
            ipcRenderer.on('download-complete', (event, filePath) => {
                this.installUpdate(filePath);
            });
        } else {
            // Web 环境下的处理
            this.simulateDownload();
        }
    }

    // 更新进度显示
    updateProgress(progress) {
        const progressFill = document.querySelector('.progress-fill');
        const progressText = document.querySelector('.progress-text');
        const progressSpeed = document.querySelector('.progress-speed');
        
        progressFill.style.width = progress.percent + '%';
        progressText.textContent = `${Math.round(progress.percent)}% - ${progress.transferred} / ${progress.total}`;
        progressSpeed.textContent = progress.speed || '';
    }

    // 安装更新
    installUpdate(filePath) {
        const progressLabel = document.querySelector('.progress-label');
        progressLabel.textContent = '正在安装更新...';
        
        // 根据平台执行安装
        if (this.platform === 'windows') {
            // Windows: 执行 .exe 文件
            const { shell } = require('electron');
            shell.openPath(filePath);
        } else if (this.platform === 'macos') {
            // macOS: 挂载 .dmg 文件
            const { exec } = require('child_process');
            exec(`open "${filePath}"`);
        }
        
        // 关闭应用准备重启
        setTimeout(() => {
            const { app } = require('electron').remote || require('@electron/remote');
            app.quit();
        }, 3000);
    }

    // 模拟下载（用于演示）
    simulateDownload() {
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 10;
            if (progress > 100) progress = 100;
            
            this.updateProgress({
                percent: progress,
                transferred: `${(progress * 0.256).toFixed(1)} MB`,
                total: '25.6 MB',
                speed: '2.1 MB/s'
            });
            
            if (progress >= 100) {
                clearInterval(interval);
                setTimeout(() => {
                    this.closeUpdateDialog();
                    alert('更新完成！应用将重启。');
                }, 1000);
            }
        }, 200);
    }

    // 关闭更新对话框
    closeUpdateDialog() {
        const overlay = document.querySelector('.update-overlay');
        if (overlay) {
            overlay.remove();
        }
    }

    // 添加样式
    addUpdateStyles() {
        if (document.getElementById('update-styles')) return;
        
        const style = document.createElement('style');
        style.id = 'update-styles';
        style.textContent = `
            .update-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
            }
            
            .update-dialog {
                background: white;
                padding: 30px;
                border-radius: 16px;
                text-align: center;
                max-width: 420px;
                width: 90%;
                box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            }
            
            .update-icon {
                width: 80px;
                height: 80px;
                background: linear-gradient(135deg, #007bff, #0056b3);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 25px;
                font-size: 2.5em;
                color: white;
            }
            
            .update-title {
                font-size: 1.4em;
                font-weight: 600;
                margin-bottom: 15px;
                color: #2c3e50;
            }
            
            .version-badge {
                background: linear-gradient(135deg, #28a745, #20c997);
                color: white;
                padding: 6px 16px;
                border-radius: 20px;
                font-size: 0.85em;
                font-weight: 600;
                display: inline-block;
                margin-bottom: 15px;
            }
            
            .update-description {
                font-size: 0.95em;
                line-height: 1.6;
                margin-bottom: 20px;
                color: #6c757d;
                background: #f8f9fa;
                padding: 15px;
                border-radius: 8px;
                border-left: 4px solid #007bff;
            }
            
            .update-size {
                margin-bottom: 25px;
                color: #6c757d;
                font-size: 0.9em;
            }
            
            .update-actions {
                display: flex;
                gap: 12px;
                justify-content: center;
                flex-direction: column;
            }
            
            .update-btn {
                padding: 14px 28px;
                border: none;
                border-radius: 8px;
                font-size: 1em;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
            }
            
            .update-now {
                background: #007bff;
                color: white;
            }
            
            .update-later {
                background: #f8f9fa;
                color: #6c757d;
                border: 1px solid #dee2e6;
            }
            
            .update-progress {
                margin-top: 25px;
            }
            
            .progress-info {
                display: flex;
                justify-content: space-between;
                margin-bottom: 10px;
            }
            
            .progress-bar {
                background: #e9ecef;
                border-radius: 12px;
                height: 12px;
                overflow: hidden;
                margin-bottom: 15px;
            }
            
            .progress-fill {
                background: linear-gradient(90deg, #007bff, #0056b3);
                height: 100%;
                width: 0%;
                transition: width 0.5s ease;
            }
        `;
        
        document.head.appendChild(style);
    }
}

// 使用示例
const appUpdater = new AppUpdater({
    apiUrl: 'https://your-domain.com/app_update_standalone.php',
    currentVersion: '1.0.0',
    platform: 'windows', // 或 'macos'
    autoCheckInterval: 3600000 // 1小时检查一次
});

// 启动自动检查
appUpdater.startAutoCheck();

// 手动检查更新
// appUpdater.checkForUpdates();
```

### 2. Electron 主进程集成

```javascript
// main.js
const { app, BrowserWindow, ipcMain } = require('electron');
const { download } = require('electron-dl');
const path = require('path');

// 处理下载请求
ipcMain.on('start-download', async (event, url) => {
    const win = BrowserWindow.getFocusedWindow();
    
    try {
        const dl = await download(win, url, {
            directory: path.join(app.getPath('downloads'), 'app-updates'),
            onProgress: (progress) => {
                event.reply('download-progress', {
                    percent: progress.percent * 100,
                    transferred: formatBytes(progress.transferredBytes),
                    total: formatBytes(progress.totalBytes),
                    speed: formatBytes(progress.bytesPerSecond) + '/s'
                });
            }
        });
        
        event.reply('download-complete', dl.getSavePath());
    } catch (error) {
        event.reply('download-error', error.message);
    }
});

function formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}
```

### 3. 配置文件示例

```json
{
    "name": "小梅花AI助手",
    "version": "1.0.0",
    "updateConfig": {
        "apiUrl": "https://your-domain.com/app_update_standalone.php",
        "autoCheck": true,
        "checkInterval": 3600000,
        "platform": "auto"
    }
}
```

## 注意事项

1. **安全性**: 确保API使用HTTPS，验证下载文件的完整性
2. **权限**: 安装更新可能需要管理员权限
3. **用户体验**: 提供清晰的进度反馈和错误处理
4. **兼容性**: 考虑不同操作系统的差异
5. **测试**: 在各种网络环境下测试更新功能

## 部署建议

1. 使用CDN加速文件下载
2. 实现断点续传功能
3. 添加更新回滚机制
4. 监控更新成功率
5. 提供离线安装包备选方案
