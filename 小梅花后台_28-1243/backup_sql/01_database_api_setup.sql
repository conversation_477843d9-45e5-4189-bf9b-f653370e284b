-- API配置数据库表结构
-- 版本: 1.0.0
-- 创建时间: 2023-06-01

-- 创建API令牌表
CREATE TABLE IF NOT EXISTS `api_tokens` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `token` varchar(255) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `expires_at` datetime NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `last_used_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `token` (`token`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建API日志表
CREATE TABLE IF NOT EXISTS `api_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `endpoint` varchar(100) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `ip_address` varchar(45) NOT NULL,
  `request_data` text DEFAULT NULL,
  `response_status` varchar(20) NOT NULL,
  `message` text DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `endpoint` (`endpoint`),
  KEY `user_id` (`user_id`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建动态端点表
CREATE TABLE IF NOT EXISTS `dynamic_endpoints` (
  `endpoint_id` int(11) NOT NULL AUTO_INCREMENT,
  `endpoint_name` varchar(50) NOT NULL,
  `key_id` int(11) NOT NULL,
  `handler_function` varchar(50) NOT NULL,
  `require_auth` tinyint(1) DEFAULT 1,
  `is_active` tinyint(1) DEFAULT 1,
  `use_count` int(11) DEFAULT 0,
  `expires_at` datetime NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `last_used_at` datetime DEFAULT NULL,
  PRIMARY KEY (`endpoint_id`),
  UNIQUE KEY `endpoint_name` (`endpoint_name`),
  KEY `key_id` (`key_id`),
  KEY `expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建心跳日志表
CREATE TABLE IF NOT EXISTS `heartbeat_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key_id` int(11) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` varchar(255) DEFAULT NULL,
  `client_info` text DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `key_id` (`key_id`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建卡密使用日志表
CREATE TABLE IF NOT EXISTS `key_usage_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key_id` int(11) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` varchar(255) DEFAULT NULL,
  `shop_name` varchar(100) DEFAULT NULL,
  `shop_url` varchar(255) DEFAULT NULL,
  `client_info` text DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `key_id` (`key_id`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建卡密状态检查日志表
CREATE TABLE IF NOT EXISTS `key_status_checks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key_id` int(11) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` varchar(255) DEFAULT NULL,
  `client_info` text DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `key_id` (`key_id`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 修改license_keys表，添加API相关字段
ALTER TABLE `license_keys` 
  ADD COLUMN `last_used_at` datetime DEFAULT NULL AFTER `use_count`,
  ADD COLUMN `last_checked_at` datetime DEFAULT NULL AFTER `last_used_at`,
  ADD COLUMN `last_heartbeat_at` datetime DEFAULT NULL AFTER `last_checked_at`,
  ADD COLUMN `last_ip` varchar(45) DEFAULT NULL AFTER `last_heartbeat_at`,
  ADD COLUMN `last_user_agent` varchar(255) DEFAULT NULL AFTER `last_ip`,
  ADD COLUMN `store_url` varchar(255) DEFAULT NULL AFTER `store_name`;

-- 创建系统设置表
CREATE TABLE IF NOT EXISTS `system_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(50) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 插入默认API配置
INSERT INTO `system_settings` (`setting_key`, `setting_value`, `description`) VALUES
('api_version', 'v2', 'API版本'),
('api_secret_key', MD5(CONCAT(UUID(), NOW())), 'API密钥，用于签名验证'),
('api_enable_signature_validation', 'true', '是否启用签名验证'),
('api_enable_timestamp_validation', 'true', '是否启用时间戳验证'),
('api_enable_token_validation', 'true', '是否启用令牌验证'),
('api_timestamp_ttl', '300', '时间戳有效期（秒）'),
('api_token_ttl', '3600', '令牌有效期（秒）'),
('api_debug_mode', 'false', '是否启用调试模式'),
('api_log_requests', 'true', '是否记录API请求'),
('api_rate_limit_enabled', 'true', '是否启用速率限制'),
('api_rate_limit_requests', '60', '每分钟最大请求数'),
('api_rate_limit_window', '60', '速率限制窗口（秒）'),
('api_encryption_enabled', 'true', '是否启用加密'),
('api_encryption_algorithm', 'aes-256-gcm', '加密算法'),
('api_dynamic_endpoint_enabled', 'true', '是否启用动态端点')
ON DUPLICATE KEY UPDATE `setting_value` = VALUES(`setting_value`), `description` = VALUES(`description`);

-- 创建API安全配置表
CREATE TABLE IF NOT EXISTS `api_security_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `config_key` varchar(50) NOT NULL,
  `config_value` text NOT NULL,
  `description` text DEFAULT NULL,
  `is_encrypted` tinyint(1) DEFAULT 0,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 插入默认API安全配置
INSERT INTO `api_security_config` (`config_key`, `config_value`, `description`, `is_encrypted`) VALUES
('primary_server', 'https://xiaomeihuakefu.cn', '主服务器地址', 0),
('backup_server', 'https://api.xiaomeihuakefu.cn', '备用服务器地址', 0),
('secure_server', 'https://secure.xiaomeihuakefu.cn', '安全服务器地址', 0),
('encryption_key', MD5(CONCAT(UUID(), NOW())), '数据加密密钥', 1),
('signature_salt', MD5(CONCAT(UUID(), NOW())), '签名盐值', 1),
('token_secret', MD5(CONCAT(UUID(), NOW())), '令牌密钥', 1),
('whitelist_ips', '[]', '白名单IP列表（JSON数组）', 0),
('blacklist_ips', '[]', '黑名单IP列表（JSON数组）', 0),
('max_failed_attempts', '5', '最大失败尝试次数', 0),
('lockout_time', '1800', '锁定时间（秒）', 0),
('secure_headers', '{"X-Frame-Options":"DENY","X-XSS-Protection":"1; mode=block","X-Content-Type-Options":"nosniff"}', '安全HTTP头（JSON对象）', 0)
ON DUPLICATE KEY UPDATE `config_value` = VALUES(`config_value`), `description` = VALUES(`description`), `is_encrypted` = VALUES(`is_encrypted`);

-- 创建API黑名单表
CREATE TABLE IF NOT EXISTS `api_blacklist` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fingerprint` varchar(32) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `reason` enum('BRUTE_FORCE','GEO_ANOMALY','MULTIPLE_FAILURES','ADMIN_BAN') NOT NULL,
  `expires_at` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `fingerprint` (`fingerprint`),
  KEY `ip_address` (`ip_address`),
  KEY `expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建API密钥同步表
CREATE TABLE IF NOT EXISTS `api_key_sync` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key_id` int(11) NOT NULL,
  `sync_status` enum('PENDING','SYNCED','FAILED') NOT NULL DEFAULT 'PENDING',
  `last_sync_at` datetime DEFAULT NULL,
  `sync_attempts` int(11) DEFAULT 0,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `key_id` (`key_id`),
  KEY `sync_status` (`sync_status`),
  KEY `last_sync_at` (`last_sync_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建API密钥缓存表（用于Redis同步）
CREATE TABLE IF NOT EXISTS `api_key_cache` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key_value` varchar(255) NOT NULL,
  `key_data` text NOT NULL,
  `expires_at` datetime NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `key_value` (`key_value`),
  KEY `expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建触发器：当license_keys表插入新记录时，自动添加到同步表
DELIMITER //
CREATE TRIGGER IF NOT EXISTS `tr_license_keys_after_insert` 
AFTER INSERT ON `license_keys`
FOR EACH ROW
BEGIN
    INSERT INTO `api_key_sync` (`key_id`, `sync_status`, `created_at`) 
    VALUES (NEW.id, 'PENDING', NOW());
END//
DELIMITER ;

-- 创建触发器：当license_keys表更新记录时，更新同步状态
DELIMITER //
CREATE TRIGGER IF NOT EXISTS `tr_license_keys_after_update` 
AFTER UPDATE ON `license_keys`
FOR EACH ROW
BEGIN
    IF NEW.status != OLD.status OR NEW.expiry_date != OLD.expiry_date OR NEW.has_customer_service != OLD.has_customer_service OR NEW.has_product_listing != OLD.has_product_listing THEN
        INSERT INTO `api_key_sync` (`key_id`, `sync_status`, `created_at`) 
        VALUES (NEW.id, 'PENDING', NOW())
        ON DUPLICATE KEY UPDATE `sync_status` = 'PENDING', `updated_at` = NOW();
    END IF;
END//
DELIMITER ;

-- 创建存储过程：同步卡密到缓存
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS `sync_keys_to_cache`()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE key_id INT;
    DECLARE cur CURSOR FOR 
        SELECT aks.key_id 
        FROM api_key_sync aks
        WHERE aks.sync_status = 'PENDING'
        ORDER BY aks.created_at ASC
        LIMIT 100;
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN cur;
    
    read_loop: LOOP
        FETCH cur INTO key_id;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- 更新同步尝试次数
        UPDATE api_key_sync 
        SET sync_attempts = sync_attempts + 1 
        WHERE key_id = key_id;
        
        -- 同步卡密到缓存
        INSERT INTO api_key_cache (key_value, key_data, expires_at)
        SELECT 
            lk.key_value,
            JSON_OBJECT(
                'id', lk.id,
                'key_value', lk.key_value,
                'type', lk.type,
                'script_id', lk.script_id,
                'status', lk.status,
                'expiry_date', lk.expiry_date,
                'has_customer_service', lk.has_customer_service,
                'has_product_listing', lk.has_product_listing
            ),
            lk.expiry_date
        FROM license_keys lk
        WHERE lk.id = key_id
        ON DUPLICATE KEY UPDATE 
            key_data = VALUES(key_data),
            expires_at = VALUES(expires_at),
            updated_at = NOW();
        
        -- 更新同步状态
        UPDATE api_key_sync 
        SET sync_status = 'SYNCED', last_sync_at = NOW() 
        WHERE key_id = key_id;
    END LOOP;
    
    CLOSE cur;
END//
DELIMITER ;

-- 创建事件：定期运行同步存储过程
DELIMITER //
CREATE EVENT IF NOT EXISTS `ev_sync_keys_to_cache`
ON SCHEDULE EVERY 5 MINUTE
DO
BEGIN
    CALL sync_keys_to_cache();
END//
DELIMITER ;

-- 确保事件调度器已启用
SET GLOBAL event_scheduler = ON; 