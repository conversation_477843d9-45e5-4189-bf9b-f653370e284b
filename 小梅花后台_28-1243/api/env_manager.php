<?php
/**
 * 环境变量管理类
 * 用于管理系统环境变量和配置
 * 
 * @version 1.1.0
 */

// 加载安全配置类
require_once __DIR__ . '/secure_config.php';

class EnvManager {
    private static $instance = null;
    private $envFile;
    private $configFile;
    private $isLoaded = false;
    private $env = [];
    private $dbConfig = [];
    private $secureConfig;

    /**
     * 构造函数
     */
    private function __construct() {
        $this->envFile = dirname(__DIR__) . '/.env';
        $this->configFile = dirname(__DIR__) . '/config/secure_config.json';
        $this->secureConfig = SecureConfig::getInstance();
    }

    /**
     * 获取实例（单例模式）
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * 加载环境变量
     */
    public function load() {
        if ($this->isLoaded) {
            return;
        }

        // 优先从安全配置文件加载
        $this->loadSecureConfig();
        
        // 如果安全配置不存在，则尝试从.env文件加载
        if (empty($this->env)) {
            $this->loadEnvFile();
            
            // 如果从.env加载成功，则将其转换为安全配置
            if (!empty($this->env)) {
                $this->migrateToSecureConfig();
            }
        }

        // 从环境变量加载数据库配置
        $this->loadDatabaseConfig();

        $this->isLoaded = true;
    }

    /**
     * 从安全配置文件加载
     */
    private function loadSecureConfig() {
        // 确保配置目录存在
        $configDir = dirname($this->configFile);
        if (!file_exists($configDir)) {
            mkdir($configDir, 0755, true);
        }
        
        // 加载安全配置
        $config = $this->secureConfig->loadConfig($this->configFile);
        if (!empty($config)) {
            $this->env = $config;
        }
    }

    /**
     * 将.env文件迁移到安全配置
     */
    private function migrateToSecureConfig() {
        if (!empty($this->env) && file_exists($this->envFile)) {
            // 保存到安全配置
            $this->secureConfig->saveConfig($this->env, $this->configFile);
            
            // 备份原始.env文件
            $backupFile = $this->envFile . '.bak';
            copy($this->envFile, $backupFile);
            
            // 创建一个新的.env文件，指向安全配置
            $envContent = "# 安全配置\n# 敏感信息已加密存储在 config/secure_config.json\n# 此文件仅作为兼容性保留\n";
            file_put_contents($this->envFile, $envContent);
        }
    }

    /**
     * 加载.env文件
     */
    private function loadEnvFile() {
        if (file_exists($this->envFile)) {
            $lines = file($this->envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            foreach ($lines as $line) {
                // 忽略注释
                if (strpos(trim($line), '#') === 0) {
                    continue;
                }

                // 解析键值对
                if (strpos($line, '=') !== false) {
                    list($name, $value) = explode('=', $line, 2);
                    $name = trim($name);
                    $value = trim($value);

                    // 去除引号
                    if (strpos($value, '"') === 0 && strrpos($value, '"') === strlen($value) - 1) {
                        $value = substr($value, 1, -1);
                    } elseif (strpos($value, "'") === 0 && strrpos($value, "'") === strlen($value) - 1) {
                        $value = substr($value, 1, -1);
                    }

                    // 存储环境变量到内部数组，不使用putenv
                    $this->env[$name] = $value;
                    $_ENV[$name] = $value;
                    $_SERVER[$name] = $value;
                }
            }
        }
    }

    /**
     * 从环境变量加载数据库配置
     */
    private function loadDatabaseConfig() {
        $this->dbConfig = [
            'host' => $this->get('DB_HOST') ?: 'localhost',
            'port' => $this->get('DB_PORT') ?: '3306',
            'dbname' => $this->get('DB_NAME') ?: 'xiaomeihua',
            'user' => $this->get('DB_USER') ?: 'root',
            'pass' => $this->get('DB_PASS') ?: ''
        ];
    }

    /**
     * 获取环境变量
     */
    public function get($key, $default = null) {
        if (isset($this->env[$key])) {
            return $this->env[$key];
        }
        
        if (isset($_ENV[$key])) {
            return $_ENV[$key];
        }
        
        if (isset($_SERVER[$key])) {
            return $_SERVER[$key];
        }
        
        return $default;
    }

    /**
     * 设置环境变量
     */
    public function set($key, $value) {
        $this->env[$key] = $value;
        $_ENV[$key] = $value;
        $_SERVER[$key] = $value;
        return $this;
    }

    /**
     * 获取数据库配置
     */
    public function getDatabaseConfig() {
        return $this->dbConfig;
    }

    /**
     * 保存数据库配置
     */
    public function saveDatabaseConfig($config) {
        // 更新内存中的配置
        foreach ($config as $key => $value) {
            if (array_key_exists($key, $this->dbConfig)) {
                $this->dbConfig[$key] = $value;
            }
        }

        // 更新环境变量
        $this->set('DB_HOST', $this->dbConfig['host']);
        $this->set('DB_PORT', $this->dbConfig['port']);
        $this->set('DB_NAME', $this->dbConfig['dbname']);
        $this->set('DB_USER', $this->dbConfig['user']);
        $this->set('DB_PASS', $this->dbConfig['pass']);

        // 保存到安全配置
        return $this->secureConfig->saveConfig($this->env, $this->configFile);
    }

    /**
     * 检查是否存在环境变量
     */
    public function has($key) {
        return isset($this->env[$key]) || isset($_ENV[$key]) || isset($_SERVER[$key]);
    }

    /**
     * 删除环境变量
     */
    public function remove($key) {
        unset($this->env[$key]);
        unset($_ENV[$key]);
        unset($_SERVER[$key]);
        return $this;
    }
}
?> 