<?php
/**
 * API密钥自动更新脚本
 * 
 * 此脚本检查并自动更新过期的API密钥
 * 可以通过cron作业定期执行
 */

// 严格的错误控制
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('log_errors_max_len', 0);
ini_set('error_log', __DIR__ . '/api_key_auto_update.log');

// 设置执行时间限制
set_time_limit(30);

// 辅助函数
function getIntervalText($seconds) {
    if ($seconds < 3600) {
        return floor($seconds / 60) . '分钟';
    } elseif ($seconds < 86400) {
        return floor($seconds / 3600) . '小时';
    } elseif ($seconds < 2592000) {
        return floor($seconds / 86400) . '天';
    } else {
        return floor($seconds / 2592000) . '个月';
    }
}

// 获取优化的数据库连接
function getOptimizedDB() {
    static $pdo = null;
    
    if ($pdo === null) {
        try {
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_SILENT,
                PDO::ATTR_TIMEOUT => 3,
                PDO::ATTR_PERSISTENT => false
            ];
            
            // 智能路径检测
            $possible_paths = [
                __DIR__ . '/../database.db',
                dirname(__DIR__) . '/database.db',
                $_SERVER['DOCUMENT_ROOT'] . '/database.db',
                'database.db',
                '/www/wwwroot/www.xiaomeihuakefu.cn/database.db'
            ];
            
            $db_path = null;
            foreach ($possible_paths as $path) {
                if (file_exists($path) && is_readable($path)) {
                    $db_path = $path;
                    break;
                }
            }
            
            if ($db_path) {
                $pdo = new PDO('sqlite:' . $db_path, null, null, $options);
                $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
            } else {
                error_log("无法找到数据库文件");
                $pdo = false;
            }
        } catch (Exception $e) {
            error_log("数据库连接失败: " . $e->getMessage());
            $pdo = false;
        }
    }
    
    return $pdo;
}

// 生成唯一令牌
function generateUniqueToken($type = 'api_key') {
    if ($type === 'api_key') {
        return bin2hex(random_bytes(32)); // 64字符长度
    } else {
        return bin2hex(random_bytes(64)); // 128字符长度
    }
}

// 自动更新过期或即将过期的API密钥
function autoUpdateExpiredKeys() {
    $pdo = getOptimizedDB();
    if (!$pdo) {
        error_log("自动更新API密钥失败: 数据库连接错误");
        return false;
    }
    
    try {
        // 检查token_management表是否存在
        $tableExists = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='token_management'")->fetch();
        
        if (!$tableExists) {
            error_log("自动更新API密钥失败: token_management表不存在");
            return false;
        }
        
        // 获取所有过期或即将过期（1小时内）且启用了自动刷新的令牌
        $stmt = $pdo->prepare("
            SELECT id, token_type, refresh_interval 
            FROM token_management 
            WHERE is_active = 1 
            AND auto_refresh = 1 
            AND expires_at <= datetime('now', '+1 hour')
        ");
        $stmt->execute();
        $tokens = $stmt->fetchAll();
        
        $updated = 0;
        $errors = 0;
        
        foreach ($tokens as $token) {
            try {
                // 生成新的令牌值
                $newTokenValue = generateUniqueToken($token['token_type']);
                
                // 计算新的过期时间
                $refreshInterval = (int)$token['refresh_interval'];
                $newExpiresAt = date('Y-m-d H:i:s', time() + $refreshInterval);
                
                // 更新令牌
                $updateStmt = $pdo->prepare("
                    UPDATE token_management 
                    SET token_value = ?, 
                        expires_at = ?, 
                        updated_at = datetime('now') 
                    WHERE id = ?
                ");
                
                $result = $updateStmt->execute([$newTokenValue, $newExpiresAt, $token['id']]);
                
                if ($result) {
                    $updated++;
                    error_log("成功更新令牌 ID: {$token['id']}, 类型: {$token['token_type']}, 新过期时间: {$newExpiresAt}");
                } else {
                    $errors++;
                    error_log("更新令牌失败 ID: {$token['id']}, 类型: {$token['token_type']}");
                }
            } catch (Exception $e) {
                $errors++;
                error_log("处理令牌时出错 ID: {$token['id']}: " . $e->getMessage());
            }
        }
        
        error_log("自动更新完成: 更新了 {$updated} 个令牌, {$errors} 个错误");
        return true;
    } catch (Exception $e) {
        error_log("自动更新API密钥时发生错误: " . $e->getMessage());
        return false;
    }
}

// 执行自动更新
$result = autoUpdateExpiredKeys();

// 如果是通过命令行运行，输出结果
if (php_sapi_name() === 'cli') {
    echo $result ? "自动更新API密钥成功完成\n" : "自动更新API密钥失败\n";
}

// 如果是通过HTTP请求运行，返回JSON响应
if (!empty($_SERVER['HTTP_ACCEPT']) && strpos($_SERVER['HTTP_ACCEPT'], 'application/json') !== false) {
    header('Content-Type: application/json');
    echo json_encode(['success' => $result]);
    exit;
}

// 如果是通过HTTP请求运行但不需要JSON响应，返回简单文本
if (!empty($_SERVER['REQUEST_METHOD'])) {
    header('Content-Type: text/plain');
    echo $result ? "自动更新API密钥成功完成" : "自动更新API密钥失败";
} 