<?php
/**
 * APP设置API接口 - 快速修复版本
 * 处理APP弹窗、协议、更新等功能的API请求
 * 使用SQLite内存数据库作为备用方案
 */

// 定义API访问常量
define('API_ACCESS', true);

// 设置响应头
header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 设置超时
set_time_limit(30);

/**
 * APP设置API处理类 - 修复版
 */
class AppSettingsAPI {
    private $db;
    private $method;
    private $endpoint;
    private $params;
    private $database_available = false;
    
    public function __construct() {
        $this->method = $_SERVER['REQUEST_METHOD'];
        $this->parseUrl();
        $this->initializeDatabase();
    }
    
    /**
     * 初始化数据库连接
     */
    private function initializeDatabase() {
        try {
            // 尝试SQLite内存数据库
            $this->db = new PDO('sqlite::memory:', null, null, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_TIMEOUT => 5
            ]);
            
            $this->initializeTables();
            $this->insertSampleData();
            $this->database_available = true;
            
            error_log("APP Settings API: 使用SQLite内存数据库");
            
        } catch (Exception $e) {
            error_log("APP Settings API: 数据库初始化失败 - " . $e->getMessage());
            $this->database_available = false;
        }
    }
    
    /**
     * 解析URL获取endpoint和参数
     */
    private function parseUrl() {
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        
        // 移除多种可能的路径前缀
        $patterns = [
            '/api/app_settings_fix.php/',
            '/api/app_settings.php/',
            '/api/app_settings/',
            '/app_settings_fix.php/',
            '/app_settings.php/',
            '/app_settings/'
        ];
        
        foreach ($patterns as $pattern) {
            if (strpos($path, $pattern) !== false) {
                $path = str_replace($pattern, '', $path);
                break;
            }
        }
        
        // 如果路径仍然包含文件名，移除它
        $path = str_replace(['app_settings_fix.php', 'app_settings.php'], '', $path);
        $path = trim($path, '/');
        
        $pathParts = $path ? explode('/', $path) : [];
        
        $this->endpoint = $pathParts[0] ?? '';
        $this->params = array_slice($pathParts, 1);
    }
    
    /**
     * 初始化数据库表
     */
    private function initializeTables() {
        // 创建弹窗表
        $sql = "CREATE TABLE IF NOT EXISTS app_popups (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            content TEXT NOT NULL,
            type TEXT NOT NULL,
            custom_days INTEGER DEFAULT NULL,
            status TEXT DEFAULT 'active',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )";
        $this->db->exec($sql);
        
        // 创建协议表
        $sql = "CREATE TABLE IF NOT EXISTS app_agreements (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            content TEXT NOT NULL,
            status TEXT DEFAULT 'draft',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )";
        $this->db->exec($sql);
        
        // 创建APP更新表
        $sql = "CREATE TABLE IF NOT EXISTS app_updates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            version TEXT DEFAULT '1.0.0',
            title TEXT DEFAULT '新版本更新',
            description TEXT DEFAULT '本次更新包含性能优化和bug修复',
            exe_file TEXT,
            dmg_file TEXT,
            force_update INTEGER DEFAULT 1,
            status TEXT DEFAULT 'draft',
            download_count INTEGER DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )";
        $this->db->exec($sql);
    }
    
    /**
     * 插入示例数据
     */
    private function insertSampleData() {
        // 插入示例弹窗
        $stmt = $this->db->prepare("INSERT INTO app_popups (title, content, type) VALUES (?, ?, ?)");
        $stmt->execute(['欢迎使用小梅花AI', '欢迎使用小梅花AI客服系统！本系统将为您提供智能化的客服解决方案。', '["once"]']);
        
        // 插入示例协议
        $stmt = $this->db->prepare("INSERT INTO app_agreements (title, content, status) VALUES (?, ?, ?)");
        $stmt->execute(['用户服务协议', '这是用户服务协议的内容...', 'published']);
        
        // 插入示例更新
        $stmt = $this->db->prepare("INSERT INTO app_updates (version, title, description, status) VALUES (?, ?, ?, ?)");
        $stmt->execute(['1.0.1', '新版本发布', '修复了一些已知问题，提升了系统稳定性。', 'published']);
    }
    
    /**
     * 处理API请求
     */
    public function handleRequest() {
        try {
            if (!$this->database_available) {
                return $this->sendError('Database not available', 500);
            }
            
            switch ($this->endpoint) {
                case 'popup':
                    return $this->handlePopup();
                case 'agreement':
                    return $this->handleAgreement();
                case 'update':
                    return $this->handleUpdate();
                case 'test':
                    return $this->handleTest();
                default:
                    return $this->sendError('Invalid endpoint. Available: popup, agreement, update, test', 404);
            }
        } catch (Exception $e) {
            error_log('APP Settings API Error: ' . $e->getMessage());
            return $this->sendError('Internal server error: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 处理测试请求
     */
    private function handleTest() {
        $testData = [
            'api_status' => 'working',
            'database_type' => 'SQLite Memory',
            'endpoints' => ['popup', 'agreement', 'update'],
            'sample_requests' => [
                'GET /popup/list - 获取弹窗列表',
                'GET /agreement/list - 获取协议列表', 
                'GET /update/list - 获取更新列表'
            ],
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        return $this->sendSuccess($testData, 'API test successful');
    }
    
    /**
     * 处理弹窗相关请求
     */
    private function handlePopup() {
        switch ($this->method) {
            case 'GET':
                if (isset($this->params[0])) {
                    if ($this->params[0] === 'list') {
                        return $this->getPopupList();
                    } else {
                        return $this->getPopup($this->params[0]);
                    }
                }
                return $this->sendError('Invalid request. Use /popup/list to get all popups', 400);
                
            case 'POST':
                return $this->createPopup();
                
            case 'PUT':
                if (isset($this->params[0])) {
                    return $this->updatePopup($this->params[0]);
                }
                return $this->sendError('ID required', 400);
                
            case 'DELETE':
                if (isset($this->params[0])) {
                    if ($this->params[0] === 'all') {
                        return $this->deleteAllPopups();
                    } else {
                        return $this->deletePopup($this->params[0]);
                    }
                }
                return $this->sendError('ID required', 400);
                
            default:
                return $this->sendError('Method not allowed', 405);
        }
    }
    
    /**
     * 获取弹窗列表
     */
    private function getPopupList() {
        try {
            $stmt = $this->db->query("SELECT * FROM app_popups ORDER BY created_at DESC");
            $popups = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // 处理type字段
            foreach ($popups as &$popup) {
                if (isset($popup['type'])) {
                    $decoded = json_decode($popup['type'], true);
                    $popup['type'] = is_array($decoded) ? $decoded : [$popup['type']];
                }
            }

            return $this->sendSuccess(['popups' => $popups]);
        } catch (Exception $e) {
            error_log('获取弹窗列表失败: ' . $e->getMessage());
            return $this->sendError('Failed to get popup list', 500);
        }
    }
    
    /**
     * 获取单个弹窗
     */
    private function getPopup($id) {
        try {
            $stmt = $this->db->prepare("SELECT * FROM app_popups WHERE id = ?");
            $stmt->execute([$id]);
            $popup = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$popup) {
                return $this->sendError('Popup not found', 404);
            }

            // 处理type字段
            if (isset($popup['type'])) {
                $decoded = json_decode($popup['type'], true);
                $popup['type'] = is_array($decoded) ? $decoded : [$popup['type']];
            }

            return $this->sendSuccess(['popup' => $popup]);
        } catch (Exception $e) {
            error_log('获取弹窗失败: ' . $e->getMessage());
            return $this->sendError('Failed to get popup', 500);
        }
    }
    
    /**
     * 创建弹窗
     */
    private function createPopup() {
        try {
            $data = $this->getRequestData();

            if (empty($data['title']) || empty($data['content'])) {
                return $this->sendError('Missing required fields: title, content', 400);
            }

            // 处理type数组
            $types = $data['type'] ?? ['once'];
            if (!is_array($types)) {
                $types = [$types];
            }

            // 验证类型
            $validTypes = ['once', 'daily_7', 'weekly', 'always', 'custom'];
            foreach ($types as $type) {
                if (!in_array($type, $validTypes)) {
                    return $this->sendError('Invalid type: ' . $type, 400);
                }
            }

            // 处理自定义天数
            $customDays = null;
            if (in_array('custom', $types) && isset($data['custom_days'])) {
                $customDays = intval($data['custom_days']);
                if ($customDays < 1 || $customDays > 365) {
                    return $this->sendError('Invalid custom days (1-365)', 400);
                }
            }

            $stmt = $this->db->prepare("INSERT INTO app_popups (title, content, type, custom_days) VALUES (?, ?, ?, ?)");
            $success = $stmt->execute([
                $data['title'],
                $data['content'],
                json_encode($types),
                $customDays
            ]);

            if ($success) {
                return $this->sendSuccess(['id' => $this->db->lastInsertId()], 'Popup created successfully');
            } else {
                return $this->sendError('Failed to create popup', 500);
            }
        } catch (Exception $e) {
            error_log('创建弹窗失败: ' . $e->getMessage());
            return $this->sendError('Failed to create popup: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 处理协议相关请求
     */
    private function handleAgreement() {
        switch ($this->method) {
            case 'GET':
                if (isset($this->params[0])) {
                    if ($this->params[0] === 'list') {
                        return $this->getAgreementList();
                    } else {
                        return $this->getAgreement($this->params[0]);
                    }
                }
                return $this->sendError('Invalid request. Use /agreement/list to get all agreements', 400);
                
            case 'POST':
                return $this->createAgreement();
                
            default:
                return $this->sendError('Method not allowed', 405);
        }
    }
    
    /**
     * 获取协议列表
     */
    private function getAgreementList() {
        try {
            $stmt = $this->db->query("SELECT * FROM app_agreements ORDER BY created_at DESC");
            $agreements = $stmt->fetchAll(PDO::FETCH_ASSOC);

            return $this->sendSuccess(['agreements' => $agreements]);
        } catch (Exception $e) {
            error_log('获取协议列表失败: ' . $e->getMessage());
            return $this->sendError('Failed to get agreement list', 500);
        }
    }
    
    /**
     * 获取单个协议
     */
    private function getAgreement($id) {
        try {
            $stmt = $this->db->prepare("SELECT * FROM app_agreements WHERE id = ?");
            $stmt->execute([$id]);
            $agreement = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$agreement) {
                return $this->sendError('Agreement not found', 404);
            }
            
            return $this->sendSuccess(['agreement' => $agreement]);
        } catch (Exception $e) {
            return $this->sendError('Failed to get agreement', 500);
        }
    }
    
    /**
     * 创建协议
     */
    private function createAgreement() {
        try {
            $data = $this->getRequestData();

            if (empty($data['title']) || empty($data['content'])) {
                return $this->sendError('Missing required fields: title, content', 400);
            }

            $stmt = $this->db->prepare("INSERT INTO app_agreements (title, content) VALUES (?, ?)");
            $success = $stmt->execute([$data['title'], $data['content']]);

            if ($success) {
                return $this->sendSuccess(['id' => $this->db->lastInsertId()], 'Agreement created successfully');
            } else {
                return $this->sendError('Failed to create agreement', 500);
            }
        } catch (Exception $e) {
            error_log('创建协议失败: ' . $e->getMessage());
            return $this->sendError('Failed to create agreement: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 处理更新相关请求
     */
    private function handleUpdate() {
        switch ($this->method) {
            case 'GET':
                if (isset($this->params[0])) {
                    if ($this->params[0] === 'list') {
                        return $this->getUpdateList();
                    } else {
                        return $this->getUpdate($this->params[0]);
                    }
                }
                return $this->sendError('Invalid request. Use /update/list to get all updates', 400);
                
            case 'POST':
                return $this->createUpdate();
                
            default:
                return $this->sendError('Method not allowed', 405);
        }
    }
    
    /**
     * 获取更新列表
     */
    private function getUpdateList() {
        try {
            $stmt = $this->db->query("SELECT * FROM app_updates ORDER BY created_at DESC");
            $versions = $stmt->fetchAll(PDO::FETCH_ASSOC);

            return $this->sendSuccess(['versions' => $versions]);
        } catch (Exception $e) {
            error_log('获取更新列表失败: ' . $e->getMessage());
            return $this->sendError('Failed to get update list', 500);
        }
    }
    
    /**
     * 获取单个更新
     */
    private function getUpdate($id) {
        try {
            $stmt = $this->db->prepare("SELECT * FROM app_updates WHERE id = ?");
            $stmt->execute([$id]);
            $update = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$update) {
                return $this->sendError('Update not found', 404);
            }
            
            return $this->sendSuccess(['update' => $update]);
        } catch (Exception $e) {
            return $this->sendError('Failed to get update', 500);
        }
    }
    
    /**
     * 创建更新
     */
    private function createUpdate() {
        try {
            $data = $this->getRequestData();

            // 设置默认值
            $version = $data['version'] ?? '1.0.0';
            $title = $data['title'] ?? '新版本更新';
            $description = $data['description'] ?? '本次更新包含性能优化和bug修复';
            $forceUpdate = 1;

            $stmt = $this->db->prepare("INSERT INTO app_updates (version, title, description, force_update) VALUES (?, ?, ?, ?)");
            $success = $stmt->execute([$version, $title, $description, $forceUpdate]);

            if ($success) {
                return $this->sendSuccess(['id' => $this->db->lastInsertId()], 'Update created successfully');
            } else {
                return $this->sendError('Failed to create update', 500);
            }
        } catch (Exception $e) {
            error_log('创建更新失败: ' . $e->getMessage());
            return $this->sendError('Failed to create update: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 删除弹窗
     */
    private function deletePopup($id) {
        try {
            $stmt = $this->db->prepare("DELETE FROM app_popups WHERE id = ?");
            $success = $stmt->execute([$id]);

            if ($success) {
                return $this->sendSuccess(null, 'Popup deleted successfully');
            } else {
                return $this->sendError('Failed to delete popup', 500);
            }
        } catch (Exception $e) {
            error_log('删除弹窗失败: ' . $e->getMessage());
            return $this->sendError('Failed to delete popup: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 删除所有弹窗
     */
    private function deleteAllPopups() {
        try {
            $stmt = $this->db->prepare("DELETE FROM app_popups");
            $success = $stmt->execute();

            if ($success) {
                $deletedCount = $stmt->rowCount();
                return $this->sendSuccess(['deleted_count' => $deletedCount], "Successfully deleted {$deletedCount} popups");
            } else {
                return $this->sendError('Failed to delete all popups', 500);
            }
        } catch (Exception $e) {
            error_log('删除所有弹窗失败: ' . $e->getMessage());
            return $this->sendError('Failed to delete all popups: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 更新弹窗
     */
    private function updatePopup($id) {
        try {
            $data = $this->getRequestData();

            if (empty($data['title']) || empty($data['content'])) {
                return $this->sendError('Missing required fields: title, content', 400);
            }

            // 处理type数组
            $types = $data['type'] ?? ['once'];
            if (!is_array($types)) {
                $types = [$types];
            }

            // 验证类型
            $validTypes = ['once', 'daily_7', 'weekly', 'always', 'custom'];
            foreach ($types as $type) {
                if (!in_array($type, $validTypes)) {
                    return $this->sendError('Invalid type: ' . $type, 400);
                }
            }

            // 处理自定义天数
            $customDays = null;
            if (in_array('custom', $types) && isset($data['custom_days'])) {
                $customDays = intval($data['custom_days']);
                if ($customDays < 1 || $customDays > 365) {
                    return $this->sendError('Invalid custom days (1-365)', 400);
                }
            }

            $stmt = $this->db->prepare("UPDATE app_popups SET title = ?, content = ?, type = ?, custom_days = ? WHERE id = ?");
            $success = $stmt->execute([
                $data['title'],
                $data['content'],
                json_encode($types),
                $customDays,
                $id
            ]);

            if ($success) {
                return $this->sendSuccess(null, 'Popup updated successfully');
            } else {
                return $this->sendError('Failed to update popup', 500);
            }
        } catch (Exception $e) {
            error_log('更新弹窗失败: ' . $e->getMessage());
            return $this->sendError('Failed to update popup: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取请求数据
     */
    private function getRequestData() {
        if ($this->method === 'GET') {
            return $_GET;
        } else {
            $input = file_get_contents('php://input');
            $data = json_decode($input, true);
            return $data ?: $_POST;
        }
    }
    
    /**
     * 发送成功响应
     */
    private function sendSuccess($data = null, $message = 'Success') {
        $response = ['success' => true, 'message' => $message];
        if ($data) {
            $response = array_merge($response, $data);
        }
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        return true;
    }
    
    /**
     * 发送错误响应
     */
    private function sendError($message, $code = 400) {
        http_response_code($code);
        echo json_encode(['success' => false, 'message' => $message], JSON_UNESCAPED_UNICODE);
        return false;
    }
}

// 处理请求
try {
    $api = new AppSettingsAPI();
    $api->handleRequest();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error: ' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
    error_log('APP Settings API Fatal Error: ' . $e->getMessage());
}
?>