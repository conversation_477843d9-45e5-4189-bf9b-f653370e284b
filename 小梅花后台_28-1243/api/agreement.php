<?php
/**
 * 独立协议API接口 - 重构版本
 * 处理APP协议相关请求（隐私政策、服务条款等）
 */

// 引入统一数据库配置
require_once __DIR__ . '/database_config.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, User-Agent');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 错误处理
function sendError($message, $code = 500) {
    http_response_code($code);
    echo json_encode([
        'success' => false,
        'message' => $message,
        'agreement' => null,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    exit;
}

// 成功响应
function sendSuccess($data, $message = 'Success') {
    echo json_encode([
        'success' => true,
        'message' => $message,
        'agreement' => $data,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    exit;
}

// 初始化协议表
function initAgreementTable($db) {
    try {
        $pdo = $db->getConnection();
        if (!$pdo) return;

        // 使用与后台管理一致的表结构
        $sql = "CREATE TABLE IF NOT EXISTS app_agreements (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            content LONGTEXT NOT NULL,
            type VARCHAR(50) DEFAULT 'privacy' COMMENT '协议类型: privacy, terms, etc',
            version VARCHAR(20) DEFAULT '1.0' COMMENT '协议版本',
            status ENUM('draft', 'published') DEFAULT 'draft' COMMENT '协议状态',
            sort_order INT DEFAULT 0 COMMENT '排序顺序，数字越小越靠前',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_type (type),
            INDEX idx_status (status),
            INDEX idx_created_at (created_at),
            INDEX idx_sort_order (sort_order)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

        $pdo->exec($sql);

        // 检查是否有默认数据
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM app_agreements WHERE status = 'published'");
        $stmt->execute();
        $result = $stmt->fetch();

        if ($result['count'] == 0) {
            // 插入app中的协议内容
            $appAgreementContent = '
<div style="font-family: \'Microsoft YaHei\', sans-serif; line-height: 1.6; color: #333;">
<h1 style="color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px;">小梅花智能AI客服软件服务协议与免责声明</h1>

<p style="color: #7f8c8d; font-size: 14px; margin: 20px 0;"><strong>最后更新日期：2025年7月17日</strong></p>

<h2 style="color: #2980b9; margin-top: 30px;">一、服务定义与范围</h2>
<p><strong>1.1</strong> 本软件（以下简称"小梅花"或"本服务"）是由技术开发者团队运营的人工智能客服系统，为用户提供自动化在线咨询应答、常见问题处理等技术支持服务。</p>
<p><strong>1.2</strong> 用户需通过购买有效卡密激活服务权限，卡密类型包括但不限于：单次体验卡、月度服务卡、年度服务卡等（具体以官方购买页面说明为准）。</p>

<h2 style="color: #2980b9; margin-top: 30px;">二、用户使用规范</h2>
<h3 style="color: #34495e;">2.1 合法使用承诺</h3>
<p>用户在使用本服务时须严格遵守以下规定：</p>
<ul style="margin-left: 20px;">
  <li>(a) 不得利用本服务从事任何违反所在国家/地区法律的行为；</li>
  <li>(b) 禁止用于欺诈、钓鱼、传播恶意信息、侵犯他人权利等非法活动；</li>
  <li>(c) 不得对微信小店、视频号等第三方平台进行违规操作；</li>
  <li>(d) 严格禁止对软件系统实施压力测试、漏洞扫描、反向工程等破坏性行为。</li>
</ul>

<h3 style="color: #34495e;">2.2 卡密管理规则</h3>
<ul style="margin-left: 20px;">
  <li>(a) 所有卡密购买后即时生效，视为数字服务已交付；</li>
  <li>(b) 基于数字商品特性，卡密激活后不接受任何形式退款（包括但不限于未使用、误购、服务中断等情形）；</li>
  <li>(c) 用户需自行承担卡密保管责任，因转借、泄露导致的损失不予补偿。</li>
</ul>

<h2 style="color: #2980b9; margin-top: 30px;">三、数据与隐私保护</h2>
<h3 style="color: #34495e;">3.1 数据收集声明</h3>
<p>本软件严格遵循"无痕服务"原则：</p>
<ul style="margin-left: 20px;">
  <li>■ 不采集用户身份信息（包括微信号、手机号、社交关系链）</li>
  <li>■ 不上传店铺运营数据（含客户资料、订单信息、财务记录）</li>
  <li>■ 不存储对话内容及文件（用户退出软件后会话数据自动销毁）</li>
  <li>■ 不获取视频号内容及用户私有媒体资源</li>
</ul>

</div>
            ';

            $stmt = $pdo->prepare("INSERT INTO app_agreements (title, content, type, version, status) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute([
                '小梅花智能AI客服软件服务协议与免责声明',
                $appAgreementContent,
                'privacy',
                '1.0',
                'published'
            ]);
        }
    } catch (Exception $e) {
        // 忽略表创建错误，继续执行
        error_log("初始化协议表失败: " . $e->getMessage());
    }
}

// 获取备用协议数据
function getFallbackAgreement($type = 'privacy') {
    // 返回app中的协议内容作为备用
    $appAgreementContent = '
<div style="font-family: \'Microsoft YaHei\', sans-serif; line-height: 1.6; color: #333;">
<h1 style="color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px;">小梅花智能AI客服软件服务协议与免责声明</h1>

<p style="color: #7f8c8d; font-size: 14px; margin: 20px 0;"><strong>最后更新日期：2025年7月17日</strong></p>

<h2 style="color: #2980b9; margin-top: 30px;">一、服务定义与范围</h2>
<p><strong>1.1</strong> 本软件（以下简称"小梅花"或"本服务"）是由技术开发者团队运营的人工智能客服系统，为用户提供自动化在线咨询应答、常见问题处理等技术支持服务。</p>
<p><strong>1.2</strong> 用户需通过购买有效卡密激活服务权限，卡密类型包括但不限于：单次体验卡、月度服务卡、年度服务卡等（具体以官方购买页面说明为准）。</p>

<h2 style="color: #2980b9; margin-top: 30px;">二、用户使用规范</h2>
<h3 style="color: #34495e;">2.1 合法使用承诺</h3>
<p>用户在使用本服务时须严格遵守以下规定：</p>
<ul style="margin-left: 20px;">
  <li>(a) 不得利用本服务从事任何违反所在国家/地区法律的行为；</li>
  <li>(b) 禁止用于欺诈、钓鱼、传播恶意信息、侵犯他人权利等非法活动；</li>
  <li>(c) 不得对微信小店、视频号等第三方平台进行违规操作；</li>
  <li>(d) 严格禁止对软件系统实施压力测试、漏洞扫描、反向工程等破坏性行为。</li>
</ul>

<div style="margin-top: 40px; padding: 20px; background-color: #ecf0f1; border-radius: 8px;">
<p style="text-align: center; color: #2c3e50; font-weight: bold;">
感谢您选择小梅花智能AI客服！<br>
使用本软件即表示您已阅读、理解并同意遵守以上所有条款。
</p>
</div>
</div>
    ';

    return [
        'id' => 1,
        'type' => 'privacy',
        'title' => '小梅花智能AI客服软件服务协议与免责声明',
        'content' => $appAgreementContent,
        'version' => '1.0',
        'status' => 'published',
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ];
}

// 获取已发布协议
function getPublishedAgreement($type = 'privacy') {
    try {
        $db = getDatabase();
        $pdo = $db->getConnection();

        // 如果数据库连接失败，使用备用数据
        if (!$pdo) {
            return getFallbackAgreement($type);
        }
    } catch (Exception $e) {
        // 数据库连接异常，使用备用数据
        error_log("数据库连接失败: " . $e->getMessage());
        return getFallbackAgreement($type);
    }

    try {
        // 初始化协议表
        initAgreementTable($db);

        // 查找已发布的协议，优先返回指定类型，按排序和创建时间排序
        $stmt = $pdo->prepare("
            SELECT * FROM app_agreements
            WHERE status = 'published' AND type = ?
            ORDER BY sort_order ASC, created_at ASC
            LIMIT 1
        ");
        $stmt->execute([$type]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        // 如果指定类型没有，查找任何已发布的协议
        if (!$result) {
            $stmt = $pdo->prepare("
                SELECT * FROM app_agreements
                WHERE status = 'published'
                ORDER BY sort_order ASC, created_at ASC
                LIMIT 1
            ");
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
        }

        // 如果数据库中没有数据，使用备用数据
        return $result ?: getFallbackAgreement($type);
    } catch (Exception $e) {
        // 查询失败时使用备用数据
        error_log("获取协议失败: " . $e->getMessage());
        return getFallbackAgreement($type);
    }
}

// 主处理逻辑
try {
    // 支持GET请求和命令行调用
    $isCommandLine = php_sapi_name() === 'cli';
    if (!$isCommandLine && $_SERVER['REQUEST_METHOD'] !== 'GET') {
        sendError('只支持GET请求', 405);
    }

    // 获取协议类型参数（支持命令行和Web请求）
    if ($isCommandLine) {
        // 命令行模式：从环境变量或命令行参数获取
        parse_str($_SERVER['QUERY_STRING'] ?? '', $params);
        $type = $params['type'] ?? 'privacy';
    } else {
        // Web模式：从GET参数获取
        $type = $_GET['type'] ?? 'privacy';
    }
    
    // 验证协议类型
    $validTypes = ['privacy', 'terms'];
    if (!in_array($type, $validTypes)) {
        sendError('无效的协议类型: ' . $type, 400);
    }
    
    // 获取已发布协议
    $agreement = getPublishedAgreement($type);

    if ($agreement) {
        sendSuccess($agreement, 'Agreement found');
    } else {
        sendError('协议不存在: ' . $type, 404);
    }
    
} catch (Exception $e) {
    sendError('服务器内部错误: ' . $e->getMessage());
}
?>
