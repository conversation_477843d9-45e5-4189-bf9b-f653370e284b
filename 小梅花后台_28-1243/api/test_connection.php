<?php
/**
 * 测试API连接和数据库连接
 */

// 设置响应头
header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // 引入数据库连接
    require_once __DIR__ . '/../includes/db.php';
    
    // 测试数据库连接
    global $pdo;
    if ($pdo) {
        $stmt = $pdo->query("SELECT 1 as test");
        $result = $stmt->fetch();
        
        if ($result) {
            echo json_encode([
                'success' => true,
                'message' => 'API和数据库连接正常',
                'timestamp' => date('Y-m-d H:i:s'),
                'database_test' => $result['test']
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => '数据库查询失败'
            ]);
        }
    } else {
        echo json_encode([
            'success' => false,
            'message' => '数据库连接对象不存在'
        ]);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => '连接测试失败: ' . $e->getMessage(),
        'error_trace' => $e->getTraceAsString()
    ]);
}
?>