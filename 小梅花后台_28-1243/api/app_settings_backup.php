<?php
/**
 * APP设置API接口
 * 处理APP弹窗、协议、更新等功能的API请求
 * 
 * @version 1.0.0
 */

// 定义API访问常量
define('API_ACCESS', true);

// 设置响应头
header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 引入必要的文件
require_once __DIR__ . '/../includes/functions.php';

// 数据库连接
try {
    require_once __DIR__ . '/../includes/db.php';
    $database_available = is_database_available();
} catch (Exception $e) {
    $database_available = false;
    error_log('数据库连接失败: ' . $e->getMessage());
}

/**
 * APP设置API处理类
 */
class AppSettingsAPI {
    private $db;
    private $method;
    private $endpoint;
    private $params;
    
    public function __construct() {
        global $pdo, $database_available;

        $this->db = $pdo;
        $this->method = $_SERVER['REQUEST_METHOD'];
        $this->parseUrl();

        // 如果数据库可用，初始化表
        if ($database_available && $this->db) {
            $this->initializeTables();
        }
    }
    
    /**
     * 解析URL获取endpoint和参数
     */
    private function parseUrl() {
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        // 移除多种可能的路径前缀
        $patterns = [
            '/api/app_settings.php/',
            '/api/app_settings/',
            '/app_settings.php/',
            '/app_settings/'
        ];
        
        foreach ($patterns as $pattern) {
            if (strpos($path, $pattern) !== false) {
                $path = str_replace($pattern, '', $path);
                break;
            }
        }
        
        // 如果路径仍然包含文件名，移除它
        $path = str_replace('app_settings.php', '', $path);
        $path = trim($path, '/');
        
        $pathParts = $path ? explode('/', $path) : [];
        
        $this->endpoint = $pathParts[0] ?? '';
        $this->params = array_slice($pathParts, 1);
    }
    
    /**
     * 初始化数据库表
     */
    private function initializeTables() {
        // 创建弹窗表
        $sql = "CREATE TABLE IF NOT EXISTS app_popups (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            content TEXT NOT NULL,
            type JSON NOT NULL,
            custom_days INT DEFAULT NULL,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        $this->db->exec($sql);

        // 检查并更新现有表结构
        try {
            // 检查type字段是否为ENUM类型，如果是则需要更新
            $stmt = $this->db->query("SHOW COLUMNS FROM app_popups LIKE 'type'");
            $column = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($column && strpos($column['Type'], 'enum') !== false) {
                // 更新type字段为JSON类型
                $this->db->exec("ALTER TABLE app_popups MODIFY COLUMN type JSON NOT NULL");

                // 添加custom_days字段（如果不存在）
                $stmt = $this->db->query("SHOW COLUMNS FROM app_popups LIKE 'custom_days'");
                if (!$stmt->fetch()) {
                    $this->db->exec("ALTER TABLE app_popups ADD COLUMN custom_days INT DEFAULT NULL AFTER type");
                }

                // 更新现有数据
                $this->db->exec("UPDATE app_popups SET type = JSON_ARRAY(type) WHERE JSON_VALID(type) = 0");
            }
        } catch (Exception $e) {
            error_log("更新弹窗表结构失败: " . $e->getMessage());
        }
        
        // 创建协议表
        $sql = "CREATE TABLE IF NOT EXISTS app_agreements (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            content LONGTEXT NOT NULL,
            status ENUM('draft', 'published') DEFAULT 'draft',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        $this->db->exec($sql);
        
        // 创建APP更新表
        $sql = "CREATE TABLE IF NOT EXISTS app_updates (
            id INT AUTO_INCREMENT PRIMARY KEY,
            version VARCHAR(50) NULL DEFAULT '1.0.0',
            title VARCHAR(255) NULL DEFAULT '新版本更新',
            description TEXT NULL DEFAULT '本次更新包含性能优化和bug修复',
            exe_file VARCHAR(255) NULL,
            dmg_file VARCHAR(255) NULL,
            force_update BOOLEAN DEFAULT TRUE,
            status ENUM('draft', 'published') DEFAULT 'draft',
            download_count INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        $this->db->exec($sql);

        // 更新现有表结构以支持新的默认值
        try {
            // 修改现有字段为可选并设置默认值
            $this->db->exec("ALTER TABLE app_updates
                MODIFY COLUMN version VARCHAR(50) NULL DEFAULT '1.0.0',
                MODIFY COLUMN title VARCHAR(255) NULL DEFAULT '新版本更新',
                MODIFY COLUMN description TEXT NULL DEFAULT '本次更新包含性能优化和bug修复',
                MODIFY COLUMN force_update BOOLEAN DEFAULT TRUE");
        } catch (Exception $e) {
            error_log("更新app_updates表结构失败: " . $e->getMessage());
        }
    }
    
    /**
     * 处理API请求
     */
    public function handleRequest() {
        try {
            switch ($this->endpoint) {
                case 'popup':
                    return $this->handlePopup();
                case 'agreement':
                    return $this->handleAgreement();
                case 'update':
                    return $this->handleUpdate();
                default:
                    return $this->sendError('Invalid endpoint', 404);
            }
        } catch (Exception $e) {
            error_log('APP Settings API Error: ' . $e->getMessage());
            return $this->sendError('Internal server error', 500);
        }
    }
    
    /**
     * 处理弹窗相关请求
     */
    private function handlePopup() {
        switch ($this->method) {
            case 'GET':
                if (isset($this->params[0])) {
                    if ($this->params[0] === 'list') {
                        return $this->getPopupList();
                    } else {
                        return $this->getPopup($this->params[0]);
                    }
                }
                return $this->sendError('Invalid request', 400);
                
            case 'POST':
                return $this->createPopup();
                
            case 'PUT':
                if (isset($this->params[0])) {
                    return $this->updatePopup($this->params[0]);
                }
                return $this->sendError('ID required', 400);
                
            case 'DELETE':
                if (isset($this->params[0])) {
                    if ($this->params[0] === 'all') {
                        return $this->deleteAllPopups();
                    } else {
                        return $this->deletePopup($this->params[0]);
                    }
                }
                return $this->sendError('ID required', 400);
                
            default:
                return $this->sendError('Method not allowed', 405);
        }
    }
    
    /**
     * 处理协议相关请求
     */
    private function handleAgreement() {
        switch ($this->method) {
            case 'GET':
                if (isset($this->params[0])) {
                    if ($this->params[0] === 'list') {
                        return $this->getAgreementList();
                    } else {
                        return $this->getAgreement($this->params[0]);
                    }
                }
                return $this->sendError('Invalid request', 400);
                
            case 'POST':
                if (isset($this->params[0])) {
                    if ($this->params[1] === 'publish') {
                        return $this->publishAgreement($this->params[0]);
                    } elseif ($this->params[1] === 'unpublish') {
                        return $this->unpublishAgreement($this->params[0]);
                    }
                }
                return $this->createAgreement();
                
            case 'PUT':
                if (isset($this->params[0])) {
                    return $this->updateAgreement($this->params[0]);
                }
                return $this->sendError('ID required', 400);
                
            case 'DELETE':
                if (isset($this->params[0])) {
                    return $this->deleteAgreement($this->params[0]);
                }
                return $this->sendError('ID required', 400);
                
            default:
                return $this->sendError('Method not allowed', 405);
        }
    }
    
    /**
     * 处理更新相关请求
     */
    private function handleUpdate() {
        switch ($this->method) {
            case 'GET':
                if (isset($this->params[0])) {
                    if ($this->params[0] === 'list') {
                        return $this->getUpdateList();
                    } else {
                        return $this->getUpdate($this->params[0]);
                    }
                }
                return $this->sendError('Invalid request', 400);
                
            case 'POST':
                if (isset($this->params[0])) {
                    if ($this->params[1] === 'publish') {
                        return $this->publishUpdate($this->params[0]);
                    } elseif ($this->params[1] === 'unpublish') {
                        return $this->unpublishUpdate($this->params[0]);
                    }
                }
                return $this->createUpdate();
                
            case 'DELETE':
                if (isset($this->params[0])) {
                    if ($this->params[0] === 'all') {
                        return $this->deleteAllUpdates();
                    } else {
                        return $this->deleteUpdate($this->params[0]);
                    }
                }
                return $this->sendError('ID required', 400);
                
            default:
                return $this->sendError('Method not allowed', 405);
        }
    }
    
    // ===================弹窗相关方法===================
    
    /**
     * 获取弹窗列表
     */
    private function getPopupList() {
        try {
            if (!$this->db) {
                return $this->sendError('Database not available', 500);
            }

            $stmt = $this->db->query("SELECT * FROM app_popups ORDER BY created_at DESC");
            $popups = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // 处理type字段，确保返回正确格式
            foreach ($popups as &$popup) {
                if (isset($popup['type'])) {
                    $popup['type'] = json_decode($popup['type'], true);
                    if (!is_array($popup['type'])) {
                        $popup['type'] = [$popup['type']];
                    }
                }
            }

            return $this->sendSuccess(['popups' => $popups]);
        } catch (Exception $e) {
            error_log('获取弹窗列表失败: ' . $e->getMessage());
            return $this->sendError('Failed to get popup list', 500);
        }
    }
    
    /**
     * 获取单个弹窗
     */
    private function getPopup($id) {
        try {
            if (!$this->db) {
                return $this->sendError('Database not available', 500);
            }

            $stmt = $this->db->prepare("SELECT * FROM app_popups WHERE id = ?");
            $stmt->execute([$id]);
            $popup = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$popup) {
                return $this->sendError('Popup not found', 404);
            }

            // 处理type字段，确保返回正确格式
            if (isset($popup['type'])) {
                $popup['type'] = json_decode($popup['type'], true);
                if (!is_array($popup['type'])) {
                    $popup['type'] = [$popup['type']];
                }
            }

            return $this->sendSuccess(['popup' => $popup]);
        } catch (Exception $e) {
            error_log('获取弹窗失败: ' . $e->getMessage());
            return $this->sendError('Failed to get popup', 500);
        }
    }
    
    /**
     * 创建弹窗
     */
    private function createPopup() {
        try {
            if (!$this->db) {
                return $this->sendError('Database not available', 500);
            }

            $data = $this->getRequestData();

            if (empty($data['title']) || empty($data['content'])) {
                return $this->sendError('Missing required fields', 400);
            }

            // 处理type数组
            $types = [];
            if (isset($data['type']) && is_array($data['type'])) {
                $types = $data['type'];
            } else {
                return $this->sendError('Invalid type format', 400);
            }

            // 验证类型
            $validTypes = ['once', 'weekly', 'always', 'custom'];
            foreach ($types as $type) {
                if (!in_array($type, $validTypes)) {
                    return $this->sendError('Invalid type: ' . $type, 400);
                }
            }

            // 处理自定义天数
            $customDays = null;
            if (in_array('custom', $types) && isset($data['custom_days'])) {
                $customDays = intval($data['custom_days']);
                if ($customDays < 1 || $customDays > 365) {
                    return $this->sendError('Invalid custom days', 400);
                }
            }

            $stmt = $this->db->prepare("INSERT INTO app_popups (title, content, type, custom_days) VALUES (?, ?, ?, ?)");
            $success = $stmt->execute([
                $data['title'],
                $data['content'],
                json_encode($types),
                $customDays
            ]);

            if ($success) {
                return $this->sendSuccess(['id' => $this->db->lastInsertId()], 'Popup created successfully');
            } else {
                return $this->sendError('Failed to create popup', 500);
            }
        } catch (Exception $e) {
            error_log('创建弹窗失败: ' . $e->getMessage());
            return $this->sendError('Failed to create popup: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 更新弹窗
     */
    private function updatePopup($id) {
        try {
            $data = $this->getRequestData();

            if (empty($data['title']) || empty($data['content'])) {
                return $this->sendError('Missing required fields', 400);
            }

            // 处理type数组
            $types = [];
            if (isset($data['type']) && is_array($data['type'])) {
                $types = $data['type'];
            } else {
                return $this->sendError('Invalid type format', 400);
            }

            // 验证类型
            $validTypes = ['once', 'daily_7', 'weekly', 'always', 'custom'];
            foreach ($types as $type) {
                if (!in_array($type, $validTypes)) {
                    return $this->sendError('Invalid type: ' . $type, 400);
                }
            }

            // 处理自定义天数
            $customDays = null;
            if (in_array('custom', $types) && isset($data['custom_days'])) {
                $customDays = intval($data['custom_days']);
                if ($customDays < 1 || $customDays > 365) {
                    return $this->sendError('Invalid custom days', 400);
                }
            }

            $stmt = $this->db->prepare("UPDATE app_popups SET title = ?, content = ?, type = ?, custom_days = ? WHERE id = ?");
            $success = $stmt->execute([
                $data['title'],
                $data['content'],
                json_encode($types),
                $customDays,
                $id
            ]);

            if ($success) {
                return $this->sendSuccess(null, 'Popup updated successfully');
            } else {
                return $this->sendError('Failed to update popup', 500);
            }
        } catch (Exception $e) {
            error_log('更新弹窗失败: ' . $e->getMessage());
            return $this->sendError('Failed to update popup: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 删除弹窗
     */
    private function deletePopup($id) {
        try {
            $stmt = $this->db->prepare("DELETE FROM app_popups WHERE id = ?");
            $success = $stmt->execute([$id]);

            if ($success) {
                return $this->sendSuccess(null, 'Popup deleted successfully');
            } else {
                return $this->sendError('Failed to delete popup', 500);
            }
        } catch (Exception $e) {
            error_log('删除弹窗失败: ' . $e->getMessage());
            return $this->sendError('Failed to delete popup: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 删除所有弹窗
     */
    private function deleteAllPopups() {
        try {
            if (!$this->db) {
                return $this->sendError('Database not available', 500);
            }

            $stmt = $this->db->prepare("DELETE FROM app_popups");
            $success = $stmt->execute();

            if ($success) {
                $deletedCount = $stmt->rowCount();
                return $this->sendSuccess(['deleted_count' => $deletedCount], "Successfully deleted {$deletedCount} popups");
            } else {
                return $this->sendError('Failed to delete all popups', 500);
            }
        } catch (Exception $e) {
            error_log('删除所有弹窗失败: ' . $e->getMessage());
            return $this->sendError('Failed to delete all popups: ' . $e->getMessage(), 500);
        }
    }
    
    // ===================协议相关方法===================
    
    /**
     * 获取协议列表
     */
    private function getAgreementList() {
        try {
            if (!$this->db) {
                return $this->sendError('Database not available', 500);
            }

            $stmt = $this->db->query("SELECT * FROM app_agreements ORDER BY created_at DESC");
            $agreements = $stmt->fetchAll(PDO::FETCH_ASSOC);

            return $this->sendSuccess(['agreements' => $agreements]);
        } catch (Exception $e) {
            error_log('获取协议列表失败: ' . $e->getMessage());
            return $this->sendError('Failed to get agreement list', 500);
        }
    }
    
    /**
     * 获取单个协议
     */
    private function getAgreement($id) {
        $stmt = $this->db->prepare("SELECT * FROM app_agreements WHERE id = ?");
        $stmt->execute([$id]);
        $agreement = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$agreement) {
            return $this->sendError('Agreement not found', 404);
        }
        
        return $this->sendSuccess(['agreement' => $agreement]);
    }
    
    /**
     * 创建协议
     */
    private function createAgreement() {
        try {
            if (!$this->db) {
                return $this->sendError('Database not available', 500);
            }

            $data = $this->getRequestData();

            if (empty($data['title']) || empty($data['content'])) {
                return $this->sendError('Missing required fields', 400);
            }

            $stmt = $this->db->prepare("INSERT INTO app_agreements (title, content) VALUES (?, ?)");
            $success = $stmt->execute([$data['title'], $data['content']]);

            if ($success) {
                return $this->sendSuccess(['id' => $this->db->lastInsertId()], 'Agreement created successfully');
            } else {
                return $this->sendError('Failed to create agreement', 500);
            }
        } catch (Exception $e) {
            error_log('创建协议失败: ' . $e->getMessage());
            return $this->sendError('Failed to create agreement: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 更新协议
     */
    private function updateAgreement($id) {
        $data = $this->getRequestData();
        
        $stmt = $this->db->prepare("UPDATE app_agreements SET title = ?, content = ? WHERE id = ?");
        $success = $stmt->execute([$data['title'], $data['content'], $id]);
        
        if ($success) {
            return $this->sendSuccess(null, 'Agreement updated successfully');
        } else {
            return $this->sendError('Failed to update agreement', 500);
        }
    }
    
    /**
     * 发布协议
     */
    private function publishAgreement($id) {
        $stmt = $this->db->prepare("UPDATE app_agreements SET status = 'published' WHERE id = ?");
        $success = $stmt->execute([$id]);
        
        if ($success) {
            return $this->sendSuccess(null, 'Agreement published successfully');
        } else {
            return $this->sendError('Failed to publish agreement', 500);
        }
    }
    
    /**
     * 撤回协议
     */
    private function unpublishAgreement($id) {
        $stmt = $this->db->prepare("UPDATE app_agreements SET status = 'draft' WHERE id = ?");
        $success = $stmt->execute([$id]);
        
        if ($success) {
            return $this->sendSuccess(null, 'Agreement unpublished successfully');
        } else {
            return $this->sendError('Failed to unpublish agreement', 500);
        }
    }
    
    /**
     * 删除协议
     */
    private function deleteAgreement($id) {
        $stmt = $this->db->prepare("DELETE FROM app_agreements WHERE id = ?");
        $success = $stmt->execute([$id]);
        
        if ($success) {
            return $this->sendSuccess(null, 'Agreement deleted successfully');
        } else {
            return $this->sendError('Failed to delete agreement', 500);
        }
    }
    
    // ===================更新相关方法===================
    
    /**
     * 获取更新列表
     */
    private function getUpdateList() {
        try {
            if (!$this->db) {
                return $this->sendError('Database not available', 500);
            }

            $stmt = $this->db->query("SELECT * FROM app_updates ORDER BY created_at DESC");
            $versions = $stmt->fetchAll(PDO::FETCH_ASSOC);

            return $this->sendSuccess(['versions' => $versions]);
        } catch (Exception $e) {
            error_log('获取更新列表失败: ' . $e->getMessage());
            return $this->sendError('Failed to get update list', 500);
        }
    }
    
    /**
     * 获取单个更新
     */
    private function getUpdate($id) {
        $stmt = $this->db->prepare("SELECT * FROM app_updates WHERE id = ?");
        $stmt->execute([$id]);
        $update = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$update) {
            return $this->sendError('Update not found', 404);
        }
        
        return $this->sendSuccess(['update' => $update]);
    }
    
    /**
     * 创建更新
     */
    private function createUpdate() {
        try {
            if (!$this->db) {
                return $this->sendError('Database not available', 500);
            }

            $data = $this->getRequestData();

            // 设置默认值，所有字段都变为可选
            $version = !empty($data['version']) ? $data['version'] : '1.0.0';
            $title = !empty($data['title']) ? $data['title'] : '新版本更新';
            $description = !empty($data['description']) ? $data['description'] : '本次更新包含性能优化和bug修复';

            // 处理文件上传
            $exeFile = null;
            $dmgFile = null;

            if (isset($_FILES['exe_file']) && $_FILES['exe_file']['error'] === UPLOAD_ERR_OK) {
                $exeFile = $this->handleFileUpload($_FILES['exe_file'], 'exe');
                if (!$exeFile) {
                    return $this->sendError('Failed to upload exe file', 500);
                }
            }

            if (isset($_FILES['dmg_file']) && $_FILES['dmg_file']['error'] === UPLOAD_ERR_OK) {
                $dmgFile = $this->handleFileUpload($_FILES['dmg_file'], 'dmg');
                if (!$dmgFile) {
                    return $this->sendError('Failed to upload dmg file', 500);
                }
            }

            // 强制更新默认为true，不再从表单获取
            $forceUpdate = 1;

            $stmt = $this->db->prepare("INSERT INTO app_updates (version, title, description, exe_file, dmg_file, force_update) VALUES (?, ?, ?, ?, ?, ?)");
            $success = $stmt->execute([$version, $title, $description, $exeFile, $dmgFile, $forceUpdate]);

            if ($success) {
                return $this->sendSuccess(['id' => $this->db->lastInsertId()], 'Update created successfully');
            } else {
                return $this->sendError('Failed to create update', 500);
            }
        } catch (Exception $e) {
            error_log('创建更新失败: ' . $e->getMessage());
            return $this->sendError('Failed to create update: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 发布更新
     */
    private function publishUpdate($id) {
        $stmt = $this->db->prepare("UPDATE app_updates SET status = 'published' WHERE id = ?");
        $success = $stmt->execute([$id]);
        
        if ($success) {
            return $this->sendSuccess(null, 'Update published successfully');
        } else {
            return $this->sendError('Failed to publish update', 500);
        }
    }
    
    /**
     * 撤回更新
     */
    private function unpublishUpdate($id) {
        $stmt = $this->db->prepare("UPDATE app_updates SET status = 'draft' WHERE id = ?");
        $success = $stmt->execute([$id]);
        
        if ($success) {
            return $this->sendSuccess(null, 'Update unpublished successfully');
        } else {
            return $this->sendError('Failed to unpublish update', 500);
        }
    }
    
    /**
     * 删除更新
     */
    private function deleteUpdate($id) {
        // 先获取文件信息以便删除文件
        $stmt = $this->db->prepare("SELECT exe_file, dmg_file FROM app_updates WHERE id = ?");
        $stmt->execute([$id]);
        $update = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($update) {
            // 删除相关文件
            if ($update['exe_file'] && file_exists(__DIR__ . '/../uploads/' . $update['exe_file'])) {
                unlink(__DIR__ . '/../uploads/' . $update['exe_file']);
            }
            if ($update['dmg_file'] && file_exists(__DIR__ . '/../uploads/' . $update['dmg_file'])) {
                unlink(__DIR__ . '/../uploads/' . $update['dmg_file']);
            }
        }
        
        $stmt = $this->db->prepare("DELETE FROM app_updates WHERE id = ?");
        $success = $stmt->execute([$id]);
        
        if ($success) {
            return $this->sendSuccess(null, 'Update deleted successfully');
        } else {
            return $this->sendError('Failed to delete update', 500);
        }
    }

    /**
     * 删除所有更新版本
     */
    private function deleteAllUpdates() {
        try {
            if (!$this->db) {
                return $this->sendError('Database not available', 500);
            }

            // 先获取所有文件信息以便删除文件
            $stmt = $this->db->query("SELECT exe_file, dmg_file FROM app_updates");
            $updates = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $deletedFiles = 0;
            foreach ($updates as $update) {
                // 删除相关文件
                if ($update['exe_file'] && file_exists(__DIR__ . '/../uploads/' . $update['exe_file'])) {
                    if (unlink(__DIR__ . '/../uploads/' . $update['exe_file'])) {
                        $deletedFiles++;
                    }
                }
                if ($update['dmg_file'] && file_exists(__DIR__ . '/../uploads/' . $update['dmg_file'])) {
                    if (unlink(__DIR__ . '/../uploads/' . $update['dmg_file'])) {
                        $deletedFiles++;
                    }
                }
            }

            // 删除数据库记录
            $stmt = $this->db->prepare("DELETE FROM app_updates");
            $success = $stmt->execute();

            if ($success) {
                $deletedCount = $stmt->rowCount();
                return $this->sendSuccess([
                    'deleted_count' => $deletedCount,
                    'deleted_files' => $deletedFiles
                ], "Successfully deleted {$deletedCount} versions and {$deletedFiles} files");
            } else {
                return $this->sendError('Failed to delete all updates', 500);
            }
        } catch (Exception $e) {
            error_log('删除所有更新版本失败: ' . $e->getMessage());
            return $this->sendError('Failed to delete all updates: ' . $e->getMessage(), 500);
        }
    }
    
    // ===================工具方法===================
    
    /**
     * 处理文件上传
     */
    private function handleFileUpload($file, $type) {
        $uploadDir = __DIR__ . '/../uploads/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        $allowedTypes = [
            'exe' => ['application/octet-stream', 'application/x-msdownload', 'application/x-msdos-program'],
            'dmg' => ['application/octet-stream', 'application/x-apple-diskimage']
        ];
        
        $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (($type === 'exe' && $fileExtension !== 'exe') || ($type === 'dmg' && $fileExtension !== 'dmg')) {
            return false;
        }
        
        $fileName = uniqid() . '_' . time() . '.' . $fileExtension;
        $filePath = $uploadDir . $fileName;
        
        if (move_uploaded_file($file['tmp_name'], $filePath)) {
            return $fileName;
        }
        
        return false;
    }
    
    /**
     * 获取请求数据
     */
    private function getRequestData() {
        if ($this->method === 'GET') {
            return $_GET;
        } else {
            $input = file_get_contents('php://input');
            $data = json_decode($input, true);
            return $data ?: $_POST;
        }
    }
    
    /**
     * 发送成功响应
     */
    private function sendSuccess($data = null, $message = 'Success') {
        $response = ['success' => true, 'message' => $message];
        if ($data) {
            $response = array_merge($response, $data);
        }
        echo json_encode($response);
        return true;
    }
    
    /**
     * 发送错误响应
     */
    private function sendError($message, $code = 400) {
        http_response_code($code);
        echo json_encode(['success' => false, 'message' => $message]);
        return false;
    }
}

// 处理请求
try {
    $api = new AppSettingsAPI();
    $api->handleRequest();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
    error_log('APP Settings API Fatal Error: ' . $e->getMessage());
}
?>