<?php
/**
 * 检查PHP文件上传配置
 */

header('Content-Type: application/json; charset=utf-8');

function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

function parseSize($size) {
    $unit = preg_replace('/[^bkmgtpezy]/i', '', $size);
    $size = preg_replace('/[^0-9\.]/', '', $size);
    
    if ($unit) {
        return round($size * pow(1024, stripos('bkmgtpezy', $unit[0])));
    } else {
        return round($size);
    }
}

$config = [
    'file_uploads' => ini_get('file_uploads'),
    'upload_max_filesize' => ini_get('upload_max_filesize'),
    'upload_max_filesize_bytes' => parseSize(ini_get('upload_max_filesize')),
    'post_max_size' => ini_get('post_max_size'),
    'post_max_size_bytes' => parseSize(ini_get('post_max_size')),
    'max_file_uploads' => ini_get('max_file_uploads'),
    'max_execution_time' => ini_get('max_execution_time'),
    'max_input_time' => ini_get('max_input_time'),
    'memory_limit' => ini_get('memory_limit'),
    'memory_limit_bytes' => parseSize(ini_get('memory_limit')),
    'upload_tmp_dir' => ini_get('upload_tmp_dir') ?: sys_get_temp_dir(),
];

// 检查上传目录
$uploadDir = __DIR__ . '/uploads/upgrade_packages/';
$uploadDirExists = file_exists($uploadDir);
$uploadDirWritable = is_writable($uploadDir);

// 建议的最小配置
$recommendations = [];

if (!$config['file_uploads']) {
    $recommendations[] = 'file_uploads 必须设置为 On';
}

if ($config['upload_max_filesize_bytes'] < 100 * 1024 * 1024) { // 100MB
    $recommendations[] = 'upload_max_filesize 建议至少设置为 100M';
}

if ($config['post_max_size_bytes'] < 100 * 1024 * 1024) { // 100MB
    $recommendations[] = 'post_max_size 建议至少设置为 100M';
}

if ($config['max_execution_time'] < 300 && $config['max_execution_time'] != 0) { // 5分钟
    $recommendations[] = 'max_execution_time 建议至少设置为 300 秒';
}

if ($config['memory_limit_bytes'] < 128 * 1024 * 1024) { // 128MB
    $recommendations[] = 'memory_limit 建议至少设置为 128M';
}

if (!$uploadDirExists) {
    $recommendations[] = '上传目录不存在: ' . $uploadDir;
}

if ($uploadDirExists && !$uploadDirWritable) {
    $recommendations[] = '上传目录不可写: ' . $uploadDir;
}

$result = [
    'success' => true,
    'message' => '配置检查完成',
    'data' => [
        'php_config' => $config,
        'upload_directory' => [
            'path' => $uploadDir,
            'exists' => $uploadDirExists,
            'writable' => $uploadDirWritable,
        ],
        'formatted_sizes' => [
            'upload_max_filesize' => formatBytes($config['upload_max_filesize_bytes']),
            'post_max_size' => formatBytes($config['post_max_size_bytes']),
            'memory_limit' => formatBytes($config['memory_limit_bytes']),
        ],
        'recommendations' => $recommendations,
        'ready_for_upload' => empty($recommendations),
    ]
];

echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>
