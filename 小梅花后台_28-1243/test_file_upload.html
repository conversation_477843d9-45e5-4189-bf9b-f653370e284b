<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件上传测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
        }
        .form-group input, .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            box-sizing: border-box;
        }
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        .file-upload {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .file-upload:hover {
            border-color: #007bff;
            background: #f8f9fa;
        }
        .file-upload.dragover {
            border-color: #007bff;
            background: #e3f2fd;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-wrap;
        }
        .result.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .result.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .file-info {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>文件上传测试</h1>
        <p>测试APP升级系统的文件上传功能</p>

        <form id="upload-form" enctype="multipart/form-data">
            <div class="form-group">
                <label>版本号:</label>
                <input type="text" name="version" value="1.0.1" required>
            </div>

            <div class="form-group">
                <label>版本标题:</label>
                <input type="text" name="title" value="测试版本 v1.0.1" required>
            </div>

            <div class="form-group">
                <label>更新说明:</label>
                <textarea name="description" required>这是一个测试版本，用于验证文件上传功能。</textarea>
            </div>

            <div class="form-group">
                <label>Windows安装包 (.exe):</label>
                <div class="file-upload" onclick="document.getElementById('exe-file').click()">
                    <input type="file" id="exe-file" name="exe_file" accept=".exe" style="display: none;" onchange="showFileInfo(this, 'exe-info')">
                    <p>点击选择 .exe 文件</p>
                    <div id="exe-info" class="file-info" style="display: none;"></div>
                </div>
            </div>

            <div class="form-group">
                <label>macOS安装包 (.dmg):</label>
                <div class="file-upload" onclick="document.getElementById('dmg-file').click()">
                    <input type="file" id="dmg-file" name="dmg_file" accept=".dmg" style="display: none;" onchange="showFileInfo(this, 'dmg-info')">
                    <p>点击选择 .dmg 文件</p>
                    <div id="dmg-info" class="file-info" style="display: none;"></div>
                </div>
            </div>

            <div class="form-group">
                <label>
                    <input type="checkbox" name="force_update" value="1" checked>
                    强制更新
                </label>
            </div>

            <button type="submit" class="btn btn-primary">上传并创建版本</button>
            <button type="button" class="btn btn-success" onclick="testWithoutFiles()">测试无文件创建</button>
        </form>

        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        // 显示文件信息
        function showFileInfo(input, infoId) {
            const infoDiv = document.getElementById(infoId);
            if (input.files && input.files[0]) {
                const file = input.files[0];
                const size = (file.size / 1024 / 1024).toFixed(2);
                infoDiv.innerHTML = `
                    <strong>文件名:</strong> ${file.name}<br>
                    <strong>大小:</strong> ${size} MB<br>
                    <strong>类型:</strong> ${file.type}
                `;
                infoDiv.style.display = 'block';
            } else {
                infoDiv.style.display = 'none';
            }
        }

        // 显示结果
        function showResult(success, message, data = null) {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result ' + (success ? 'success' : 'error');
            
            let content = message;
            if (data) {
                content += '\n\n详细信息:\n' + JSON.stringify(data, null, 2);
            }
            resultDiv.textContent = content;
        }

        // 表单提交
        document.getElementById('upload-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            formData.append('action', 'create');
            
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = '上传中...';
            submitBtn.disabled = true;
            
            try {
                const response = await fetch('app_update_standalone.php', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                showResult(result.success, result.message, result.data);
                
                if (result.success) {
                    this.reset();
                    document.getElementById('exe-info').style.display = 'none';
                    document.getElementById('dmg-info').style.display = 'none';
                }
            } catch (error) {
                showResult(false, '上传失败: ' + error.message);
            } finally {
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }
        });

        // 测试无文件创建（使用URL）
        async function testWithoutFiles() {
            const formData = new FormData();
            formData.append('action', 'create');
            formData.append('version', '1.0.2');
            formData.append('title', '测试版本 v1.0.2 (URL方式)');
            formData.append('description', '这是一个使用URL方式的测试版本');
            formData.append('exe_download_url', 'https://example.com/test.exe');
            formData.append('dmg_download_url', 'https://example.com/test.dmg');
            formData.append('force_update', '1');
            
            try {
                const response = await fetch('app_update_standalone.php', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                showResult(result.success, result.message, result.data);
            } catch (error) {
                showResult(false, '创建失败: ' + error.message);
            }
        }

        // 拖拽上传支持
        document.querySelectorAll('.file-upload').forEach(area => {
            area.addEventListener('dragover', function(e) {
                e.preventDefault();
                this.classList.add('dragover');
            });
            
            area.addEventListener('dragleave', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
            });
            
            area.addEventListener('drop', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    const input = this.querySelector('input[type="file"]');
                    input.files = files;
                    input.dispatchEvent(new Event('change'));
                }
            });
        });
    </script>
</body>
</html>
