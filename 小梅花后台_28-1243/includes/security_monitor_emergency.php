<?php
/**
 * 简化的安全监控模块 - 紧急修复版本
 * 移除可能导致502/500错误的复杂逻辑
 */

class EmergencySecurityMonitor {
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    /**
     * 简化的安全检查 - 总是返回true，允许访问
     */
    public function checkPageSecurity($user_id, $page) {
        // 紧急修复：暂时允许所有访问，避免阻塞
        error_log("🔧 紧急模式：允许所有页面访问 - 用户ID: $user_id, 页面: $page");
        return true;
    }
    
    /**
     * 简化的设备信任检查
     */
    public function isTrustedDevice($user_id, $device_fingerprint) {
        // 紧急修复：暂时信任所有设备
        return true;
    }
    
    /**
     * 空操作的日志记录
     */
    public function logPageAccess($user_id, $page, $is_trusted_device, $device_info = []) {
        // 简化日志记录，避免数据库操作导致的阻塞
        error_log("访问日志: 用户{$user_id} 访问页面{$page}");
        return true;
    }
    
    /**
     * 简化的登录通知
     */
    public function sendLoginNotification($user_email, $login_info) {
        // 暂时跳过邮件发送，避免网络请求导致的阻塞
        return true;
    }
    
    /**
     * 简化的安全警告
     */
    public function sendUntrustedAccessAlert($user_email, $access_info) {
        // 暂时跳过邮件发送
        return true;
    }
    
    /**
     * 简化的入侵者警告
     */
    public function sendIntruderAlert($user_email, $access_info) {
        // 暂时跳过邮件发送
        return true;
    }
    
    /**
     * 获取信任设备列表 - 简化版
     */
    public function getTrustedDevices($user_id) {
        return [];
    }
    
    /**
     * 获取页面访问记录 - 简化版
     */
    public function getRecentPageAccess($user_id, $limit = 50) {
        return [];
    }
}

// 全局安全监控实例 - 紧急版本
function getSecurityMonitor() {
    global $pdo;
    static $emergency_monitor = null;
    
    if ($emergency_monitor === null) {
        $emergency_monitor = new EmergencySecurityMonitor($pdo);
    }
    
    return $emergency_monitor;
}

// 简化的设备指纹函数
function getDeviceFingerprint() {
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $ip = $_SERVER['REMOTE_ADDR'] ?? '';
    return md5($user_agent . $ip);
}

function getDeviceName() {
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    if (strpos($user_agent, 'Windows') !== false) {
        return 'Windows设备';
    } elseif (strpos($user_agent, 'Mac') !== false) {
        return 'Mac设备';
    } elseif (strpos($user_agent, 'Linux') !== false) {
        return 'Linux设备';
    } elseif (strpos($user_agent, 'Android') !== false) {
        return 'Android设备';
    } elseif (strpos($user_agent, 'iOS') !== false) {
        return 'iOS设备';
    } else {
        return '未知设备';
    }
}

?>