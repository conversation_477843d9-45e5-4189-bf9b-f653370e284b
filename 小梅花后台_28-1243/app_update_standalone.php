<?php
/**
 * 独立的APP更新API - 完全自包含，不依赖任何外部文件
 * 直接连接MySQL数据库，确保100%可用
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 错误处理函数
function sendError($message, $code = 400) {
    http_response_code($code);
    echo json_encode([
        'success' => false,
        'message' => $message,
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 成功响应函数
function sendSuccess($data = null, $message = 'Success') {
    echo json_encode([
        'success' => true,
        'message' => $message,
        'data' => $data,
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 直接连接MySQL数据库
function getDatabase() {
    try {
        $pdo = new PDO(
            "mysql:host=127.0.0.1;port=3306;dbname=xiaomeihuakefu_c;charset=utf8mb4",
            "xiaomeihuakefu_c",
            "7Da5F1Xx995cxYz8",
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci",
                PDO::ATTR_TIMEOUT => 10
            ]
        );
        
        // 设置时区
        $pdo->exec("SET time_zone = '+08:00'");
        
        return $pdo;
    } catch (Exception $e) {
        sendError('数据库连接失败: ' . $e->getMessage(), 500);
    }
}

// 初始化数据库表
function initializeTables($pdo) {
    try {
        // 检查表是否存在
        $stmt = $pdo->query("SHOW TABLES LIKE 'app_updates'");
        if ($stmt->rowCount() == 0) {
            // 创建表
            $createTableSQL = "
            CREATE TABLE `app_updates` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `version` varchar(50) NOT NULL COMMENT '版本号，如：1.0.1',
              `title` varchar(255) NOT NULL COMMENT '版本标题',
              `description` longtext NOT NULL COMMENT '更新说明（支持HTML）',
              `exe_download_url` varchar(500) DEFAULT NULL COMMENT 'Windows安装包下载链接（可选）',
              `dmg_download_url` varchar(500) DEFAULT NULL COMMENT 'macOS安装包下载链接（可选）',
              `force_update` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否强制更新',
              `status` enum('draft','published') NOT NULL DEFAULT 'draft' COMMENT '版本状态',
              `download_count` int(11) NOT NULL DEFAULT 0 COMMENT '下载次数统计',
              `platform` enum('all','windows','macos','linux') NOT NULL DEFAULT 'all' COMMENT '支持平台',
              `min_version` varchar(50) DEFAULT NULL COMMENT '最低兼容版本',
              `release_notes` text DEFAULT NULL COMMENT '发布说明',
              `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
              `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
              PRIMARY KEY (`id`),
              UNIQUE KEY `unique_version` (`version`),
              KEY `idx_status` (`status`),
              KEY `idx_platform` (`platform`),
              KEY `idx_created_at` (`created_at`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='APP版本更新表'
            ";
            
            $pdo->exec($createTableSQL);
            
            // 插入测试数据
            $insertSQL = "
            INSERT INTO `app_updates` (
              `version`, `title`, `description`, `exe_download_url`, `dmg_download_url`,
              `force_update`, `status`, `platform`
            ) VALUES (
              '1.0.1',
              '小梅花AI助手 v1.0.1 更新',
              '<p>这是一个测试版本，用于验证APP更新功能。</p>',
              'https://example.com/xiaomeihua-windows-v1.0.1.exe',
              'https://example.com/xiaomeihua-macos-v1.0.1.dmg',
              1,
              'published',
              'all'
            )
            ";
            
            $pdo->exec($insertSQL);
        }
    } catch (Exception $e) {
        sendError('表初始化失败: ' . $e->getMessage(), 500);
    }
}

// 版本号验证
function validateVersion($version) {
    return preg_match('/^\d+\.\d+\.\d+$/', $version);
}

// URL验证
function validateUrl($url) {
    return filter_var($url, FILTER_VALIDATE_URL) !== false;
}

// 获取数据库连接
$pdo = getDatabase();

// 初始化表
initializeTables($pdo);

// 获取请求方法
$method = $_SERVER['REQUEST_METHOD'];

// API路由
switch ($method) {
    case 'GET':
        $action = $_GET['action'] ?? 'list';
        switch ($action) {
            case 'check':
                handleCheckUpdate($pdo);
                break;
            case 'list':
                handleListVersions($pdo);
                break;
            case 'download':
                handleDownload($pdo);
                break;
            default:
                sendError('未知的操作类型');
        }
        break;
        
    case 'POST':
        $action = $_POST['action'] ?? '';
        switch ($action) {
            case 'create':
                handleCreateVersion($pdo);
                break;
            case 'update':
                handleUpdateVersion($pdo);
                break;
            case 'delete':
                handleDeleteVersion($pdo);
                break;
            case 'publish':
                handlePublishVersion($pdo);
                break;
            case 'unpublish':
                handleUnpublishVersion($pdo);
                break;
            case 'delete_all':
                handleDeleteAllVersions($pdo);
                break;
            default:
                sendError('未知的操作类型');
        }
        break;
        
    default:
        sendError('不支持的请求方法', 405);
}

/**
 * 检查更新
 */
function handleCheckUpdate($pdo) {
    $currentVersion = $_GET['version'] ?? '1.0.0';
    $platform = strtolower($_GET['platform'] ?? 'all');
    
    try {
        // 获取最新发布的版本
        $stmt = $pdo->prepare("
            SELECT * FROM app_updates 
            WHERE status = 'published' AND (platform = ? OR platform = 'all')
            ORDER BY version DESC, created_at DESC 
            LIMIT 1
        ");
        $stmt->execute([$platform]);
        $latestUpdate = $stmt->fetch();
        
        if (!$latestUpdate) {
            sendSuccess([
                'has_update' => false,
                'current_version' => $currentVersion,
                'message' => '暂无可用更新'
            ], '无可用更新');
        }
        
        // 比较版本号
        $hasUpdate = version_compare($latestUpdate['version'], $currentVersion, '>');
        
        $response = [
            'has_update' => $hasUpdate,
            'current_version' => $currentVersion,
            'latest_version' => $latestUpdate['version']
        ];
        
        if ($hasUpdate) {
            $response['update_info'] = [
                'id' => $latestUpdate['id'],
                'title' => $latestUpdate['title'],
                'description' => $latestUpdate['description'],
                'force_update' => (bool)$latestUpdate['force_update'],
                'download_urls' => [
                    'windows' => $latestUpdate['exe_download_url'] ? getFullUrl($latestUpdate['exe_download_url']) : null,
                    'macos' => $latestUpdate['dmg_download_url'] ? getFullUrl($latestUpdate['dmg_download_url']) : null
                ],
                'release_notes' => $latestUpdate['release_notes']
            ];
        }
        
        sendSuccess($response, $hasUpdate ? '发现新版本' : '已是最新版本');
        
    } catch (Exception $e) {
        sendError('检查更新失败: ' . $e->getMessage(), 500);
    }
}

/**
 * 获取版本列表
 */
function handleListVersions($pdo) {
    try {
        $page = max(1, intval($_GET['page'] ?? 1));
        $limit = max(1, min(100, intval($_GET['limit'] ?? 10)));
        $offset = ($page - 1) * $limit;

        // 获取总数
        $stmt = $pdo->query("SELECT COUNT(*) FROM app_updates");
        $total = $stmt->fetchColumn();

        // 获取列表
        $stmt = $pdo->prepare("SELECT * FROM app_updates ORDER BY created_at DESC LIMIT ? OFFSET ?");
        $stmt->execute([$limit, $offset]);
        $versions = $stmt->fetchAll();

        sendSuccess([
            'versions' => $versions,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ceil($total / $limit)
            ]
        ], '获取版本列表成功');

    } catch (Exception $e) {
        sendError('获取版本列表失败: ' . $e->getMessage(), 500);
    }
}

/**
 * 创建新版本
 */
function handleCreateVersion($pdo) {
    // 验证必需参数
    $version = trim($_POST['version'] ?? '');
    $title = trim($_POST['title'] ?? '');
    $description = trim($_POST['description'] ?? '');

    if (empty($title) || empty($description)) {
        sendError('标题和描述不能为空');
    }

    // 如果没有版本号，自动生成
    if (empty($version)) {
        $stmt = $pdo->query("SELECT version FROM app_updates ORDER BY created_at DESC LIMIT 1");
        $lastVersion = $stmt->fetchColumn();
        if ($lastVersion && preg_match('/^(\d+)\.(\d+)\.(\d+)$/', $lastVersion, $matches)) {
            $version = $matches[1] . '.' . $matches[2] . '.' . ($matches[3] + 1);
        } else {
            $version = '1.0.1';
        }
    }

    // 验证版本号格式
    if (!validateVersion($version)) {
        sendError('版本号格式不正确，应为：主版本号.次版本号.修订号');
    }

    // 处理文件上传
    $exeUrl = '';
    $dmgUrl = '';

    // 处理Windows exe文件上传
    if (isset($_FILES['exe_file']) && $_FILES['exe_file']['error'] === UPLOAD_ERR_OK) {
        $exeUrl = handleFileUpload($_FILES['exe_file'], 'exe', $version);
        if (!$exeUrl) {
            sendError('Windows安装包上传失败');
        }
    }

    // 处理macOS dmg文件上传
    if (isset($_FILES['dmg_file']) && $_FILES['dmg_file']['error'] === UPLOAD_ERR_OK) {
        $dmgUrl = handleFileUpload($_FILES['dmg_file'], 'dmg', $version);
        if (!$dmgUrl) {
            sendError('macOS安装包上传失败');
        }
    }

    // 如果没有文件上传，检查是否提供了下载链接（兼容旧版本）
    if (empty($exeUrl) && empty($dmgUrl)) {
        $exeUrl = trim($_POST['exe_download_url'] ?? '');
        $dmgUrl = trim($_POST['dmg_download_url'] ?? '');
    }

    // 至少需要一个文件或下载链接
    if (empty($exeUrl) && empty($dmgUrl)) {
        sendError('至少需要上传一个安装包文件（Windows或macOS）');
    }

    // 验证URL格式（如果是链接）
    if (!empty($exeUrl) && !isUploadedFile($exeUrl) && !validateUrl($exeUrl)) {
        sendError('Windows下载链接格式不正确');
    }

    if (!empty($dmgUrl) && !isUploadedFile($dmgUrl) && !validateUrl($dmgUrl)) {
        sendError('macOS下载链接格式不正确');
    }
    
    try {
        // 检查版本是否已存在
        $stmt = $pdo->prepare("SELECT id FROM app_updates WHERE version = ?");
        $stmt->execute([$version]);
        if ($stmt->fetch()) {
            sendError('版本号已存在，请使用不同的版本号');
        }
        
        // 插入新版本
        $stmt = $pdo->prepare("
            INSERT INTO app_updates 
            (version, title, description, exe_download_url, dmg_download_url, 
             force_update, status, platform) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $success = $stmt->execute([
            $version,
            $title,
            $description,
            $exeUrl ?: null,
            $dmgUrl ?: null,
            isset($_POST['force_update']) ? 1 : 0,
            $_POST['status'] ?? 'published',
            $_POST['platform'] ?? 'all'
        ]);
        
        if ($success) {
            $newId = $pdo->lastInsertId();
            
            // 获取创建的版本信息
            $stmt = $pdo->prepare("SELECT * FROM app_updates WHERE id = ?");
            $stmt->execute([$newId]);
            $newVersion = $stmt->fetch();
            
            sendSuccess($newVersion, '版本创建成功');
        } else {
            sendError('版本创建失败', 500);
        }
        
    } catch (Exception $e) {
        sendError('创建版本失败: ' . $e->getMessage(), 500);
    }
}

/**
 * 删除版本
 */
function handleDeleteVersion($pdo) {
    $id = intval($_POST['id'] ?? 0);
    if ($id <= 0) {
        sendError('无效的版本ID');
    }

    try {
        // 先获取版本信息，以便删除相关文件
        $stmt = $pdo->prepare("SELECT exe_download_url, dmg_download_url FROM app_updates WHERE id = ?");
        $stmt->execute([$id]);
        $version = $stmt->fetch();

        if (!$version) {
            sendError('版本不存在');
        }

        // 删除数据库记录
        $stmt = $pdo->prepare("DELETE FROM app_updates WHERE id = ?");
        $success = $stmt->execute([$id]);

        if ($success && $stmt->rowCount() > 0) {
            // 删除相关的上传文件
            if (!empty($version['exe_download_url'])) {
                deleteUploadedFile($version['exe_download_url']);
            }
            if (!empty($version['dmg_download_url'])) {
                deleteUploadedFile($version['dmg_download_url']);
            }

            sendSuccess(null, '版本删除成功');
        } else {
            sendError('版本删除失败');
        }

    } catch (Exception $e) {
        sendError('删除版本失败: ' . $e->getMessage(), 500);
    }
}

/**
 * 下载处理
 */
function handleDownload($pdo) {
    $id = intval($_GET['id'] ?? 0);
    $platform = strtolower($_GET['platform'] ?? 'windows');
    
    if ($id <= 0) {
        sendError('无效的版本ID');
    }
    
    try {
        $stmt = $pdo->prepare("SELECT * FROM app_updates WHERE id = ? AND status = 'published'");
        $stmt->execute([$id]);
        $version = $stmt->fetch();
        
        if (!$version) {
            sendError('版本不存在或未发布');
        }
        
        $downloadUrl = null;
        if ($platform === 'windows' && $version['exe_download_url']) {
            $downloadUrl = $version['exe_download_url'];
        } elseif ($platform === 'macos' && $version['dmg_download_url']) {
            $downloadUrl = $version['dmg_download_url'];
        }
        
        if (!$downloadUrl) {
            sendError('该平台的下载链接不存在');
        }
        
        // 更新下载次数
        $stmt = $pdo->prepare("UPDATE app_updates SET download_count = download_count + 1 WHERE id = ?");
        $stmt->execute([$id]);
        
        sendSuccess([
            'download_url' => $downloadUrl,
            'version' => $version['version'],
            'title' => $version['title']
        ], '获取下载链接成功');
        
    } catch (Exception $e) {
        sendError('获取下载链接失败: ' . $e->getMessage(), 500);
    }
}

/**
 * 更新版本信息
 */
function handleUpdateVersion($pdo) {
    $id = intval($_POST['id'] ?? 0);
    if ($id <= 0) {
        sendError('无效的版本ID');
    }

    try {
        // 检查版本是否存在
        $stmt = $pdo->prepare("SELECT * FROM app_updates WHERE id = ?");
        $stmt->execute([$id]);
        $version = $stmt->fetch();
        
        if (!$version) {
            sendError('版本不存在');
        }
        
        // 更新字段
        $updateFields = [];
        $updateValues = [];

        if (isset($_POST['version'])) {
            $updateFields[] = 'version = ?';
            $updateValues[] = trim($_POST['version']);
        }

        if (isset($_POST['title'])) {
            $updateFields[] = 'title = ?';
            $updateValues[] = trim($_POST['title']);
        }

        if (isset($_POST['description'])) {
            $updateFields[] = 'description = ?';
            $updateValues[] = trim($_POST['description']);
        }

        if (isset($_POST['exe_download_url'])) {
            $updateFields[] = 'exe_download_url = ?';
            $updateValues[] = trim($_POST['exe_download_url']);
        }

        if (isset($_POST['dmg_download_url'])) {
            $updateFields[] = 'dmg_download_url = ?';
            $updateValues[] = trim($_POST['dmg_download_url']);
        }

        if (isset($_POST['force_update'])) {
            $updateFields[] = 'force_update = ?';
            $updateValues[] = intval($_POST['force_update']);
        }

        if (isset($_POST['status'])) {
            $updateFields[] = 'status = ?';
            $updateValues[] = $_POST['status'];
        }

        // 添加更新时间
        $updateFields[] = 'updated_at = NOW()';

        if (empty($updateFields) || count($updateFields) <= 1) { // 减去updated_at字段
            sendError('没有要更新的字段');
        }
        
        $updateValues[] = $id;
        
        $sql = "UPDATE app_updates SET " . implode(', ', $updateFields) . " WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $success = $stmt->execute($updateValues);
        
        if ($success) {
            // 获取更新后的版本信息
            $stmt = $pdo->prepare("SELECT * FROM app_updates WHERE id = ?");
            $stmt->execute([$id]);
            $updatedVersion = $stmt->fetch();
            
            sendSuccess($updatedVersion, '版本更新成功');
        } else {
            sendError('版本更新失败', 500);
        }
        
    } catch (Exception $e) {
        sendError('更新版本失败: ' . $e->getMessage(), 500);
    }
}

/**
 * 发布版本
 */
function handlePublishVersion($pdo) {
    $id = $_POST['id'] ?? '';

    if (empty($id)) {
        sendError('版本ID不能为空');
    }

    try {
        // 检查版本是否存在
        $stmt = $pdo->prepare("SELECT * FROM app_updates WHERE id = ?");
        $stmt->execute([$id]);
        $version = $stmt->fetch();

        if (!$version) {
            sendError('版本不存在');
        }

        // 更新状态为已发布
        $stmt = $pdo->prepare("UPDATE app_updates SET status = 'published', updated_at = NOW() WHERE id = ?");
        $success = $stmt->execute([$id]);

        if ($success) {
            // 获取更新后的版本信息
            $stmt = $pdo->prepare("SELECT * FROM app_updates WHERE id = ?");
            $stmt->execute([$id]);
            $updatedVersion = $stmt->fetch();

            sendSuccess($updatedVersion, '版本发布成功');
        } else {
            sendError('版本发布失败', 500);
        }

    } catch (Exception $e) {
        sendError('发布版本失败: ' . $e->getMessage(), 500);
    }
}

/**
 * 撤回版本
 */
function handleUnpublishVersion($pdo) {
    $id = $_POST['id'] ?? '';

    if (empty($id)) {
        sendError('版本ID不能为空');
    }

    try {
        // 检查版本是否存在
        $stmt = $pdo->prepare("SELECT * FROM app_updates WHERE id = ?");
        $stmt->execute([$id]);
        $version = $stmt->fetch();

        if (!$version) {
            sendError('版本不存在');
        }

        // 更新状态为草稿
        $stmt = $pdo->prepare("UPDATE app_updates SET status = 'draft', updated_at = NOW() WHERE id = ?");
        $success = $stmt->execute([$id]);

        if ($success) {
            // 获取更新后的版本信息
            $stmt = $pdo->prepare("SELECT * FROM app_updates WHERE id = ?");
            $stmt->execute([$id]);
            $updatedVersion = $stmt->fetch();

            sendSuccess($updatedVersion, '版本撤回成功');
        } else {
            sendError('版本撤回失败', 500);
        }

    } catch (Exception $e) {
        sendError('撤回版本失败: ' . $e->getMessage(), 500);
    }
}

/**
 * 删除所有版本
 */
function handleDeleteAllVersions($pdo) {
    try {
        // 获取所有版本信息
        $stmt = $pdo->query("SELECT exe_download_url, dmg_download_url FROM app_updates");
        $versions = $stmt->fetchAll();
        $count = count($versions);

        if ($count == 0) {
            sendSuccess(['deleted_count' => 0, 'deleted_files' => 0], '没有版本需要删除');
            return;
        }

        // 删除所有版本记录
        $stmt = $pdo->prepare("DELETE FROM app_updates");
        $success = $stmt->execute();

        if ($success) {
            // 删除所有相关的上传文件
            $deletedFiles = 0;
            foreach ($versions as $version) {
                if (!empty($version['exe_download_url']) && deleteUploadedFile($version['exe_download_url'])) {
                    $deletedFiles++;
                }
                if (!empty($version['dmg_download_url']) && deleteUploadedFile($version['dmg_download_url'])) {
                    $deletedFiles++;
                }
            }

            sendSuccess([
                'deleted_count' => $count,
                'deleted_files' => $deletedFiles
            ], "成功删除 {$count} 个版本和 {$deletedFiles} 个文件");
        } else {
            sendError('删除失败', 500);
        }

    } catch (Exception $e) {
        sendError('删除所有版本失败: ' . $e->getMessage(), 500);
    }
}

/**
 * 处理文件上传
 */
function handleFileUpload($file, $type, $version) {
    // 验证文件类型
    $allowedTypes = [
        'exe' => ['application/x-msdownload', 'application/octet-stream', 'application/x-executable'],
        'dmg' => ['application/x-apple-diskimage', 'application/octet-stream']
    ];

    $allowedExtensions = [
        'exe' => 'exe',
        'dmg' => 'dmg'
    ];

    // 检查文件扩展名
    $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if ($fileExtension !== $allowedExtensions[$type]) {
        return false;
    }

    // 创建上传目录
    $uploadDir = __DIR__ . '/uploads/upgrade_packages/';
    if (!file_exists($uploadDir)) {
        if (!mkdir($uploadDir, 0755, true)) {
            return false;
        }
    }

    // 生成文件名
    $fileName = 'app_v' . $version . '_' . $type . '.' . $fileExtension;
    $filePath = $uploadDir . $fileName;

    // 移动上传的文件
    if (move_uploaded_file($file['tmp_name'], $filePath)) {
        // 返回相对URL路径
        return 'uploads/upgrade_packages/' . $fileName;
    }

    return false;
}

/**
 * 检查是否为上传的文件路径
 */
function isUploadedFile($path) {
    return strpos($path, 'uploads/upgrade_packages/') === 0;
}

/**
 * 删除上传的文件
 */
function deleteUploadedFile($filePath) {
    if (isUploadedFile($filePath)) {
        $fullPath = __DIR__ . '/' . $filePath;
        if (file_exists($fullPath)) {
            return unlink($fullPath);
        }
    }
    return true; // 如果文件不存在或不是上传文件，认为删除成功
}

/**
 * 获取完整的URL
 */
function getFullUrl($path) {
    if (empty($path)) {
        return null;
    }

    // 如果已经是完整URL，直接返回
    if (preg_match('/^https?:\/\//', $path)) {
        return $path;
    }

    // 如果是相对路径，转换为完整URL
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    $scriptDir = dirname($_SERVER['SCRIPT_NAME']);

    // 确保路径以/开头
    if (strpos($path, '/') !== 0) {
        $path = '/' . $path;
    }

    return $protocol . '://' . $host . $scriptDir . $path;
}
?>
