<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>APP升级功能测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #495057;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .result.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .result.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }
        .form-group input, .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .form-group textarea {
            height: 80px;
            resize: vertical;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-download"></i> APP升级功能测试</h1>
            <p>测试APP在线升级系统的各项功能</p>
        </div>

        <!-- 配置检查 -->
        <div class="test-section">
            <h3><i class="fas fa-cog"></i> 配置检查</h3>
            <p>检查PHP文件上传配置和目录权限</p>
            <button class="btn btn-info" onclick="testUploadConfig()">
                <i class="fas fa-check-circle"></i> 检查上传配置
            </button>
            <div id="config-result" class="result" style="display: none;"></div>
        </div>

        <!-- API连接测试 -->
        <div class="test-section">
            <h3><i class="fas fa-plug"></i> API连接测试</h3>
            <p>测试与升级API的连接状态</p>
            <button class="btn btn-primary" onclick="testApiConnection()">
                <i class="fas fa-wifi"></i> 测试API连接
            </button>
            <div id="api-result" class="result" style="display: none;"></div>
        </div>

        <!-- 版本列表测试 -->
        <div class="test-section">
            <h3><i class="fas fa-list"></i> 版本列表测试</h3>
            <p>获取当前所有版本列表</p>
            <button class="btn btn-info" onclick="testVersionList()">
                <i class="fas fa-download"></i> 获取版本列表
            </button>
            <div id="version-list-result" class="result" style="display: none;"></div>
        </div>

        <!-- 创建版本测试 -->
        <div class="test-section">
            <h3><i class="fas fa-plus"></i> 创建版本测试</h3>
            <p>测试创建新版本功能</p>
            <div class="form-group">
                <label>版本号:</label>
                <input type="text" id="test-version" placeholder="例如: 1.0.1">
            </div>
            <div class="form-group">
                <label>版本标题:</label>
                <input type="text" id="test-title" placeholder="例如: 小梅花AI助手 v1.0.1 更新">
            </div>
            <div class="form-group">
                <label>更新说明:</label>
                <textarea id="test-description" placeholder="请输入更新说明..."></textarea>
            </div>
            <div class="form-group">
                <label>Windows下载链接:</label>
                <input type="text" id="test-exe-url" placeholder="https://example.com/app.exe">
            </div>
            <div class="form-group">
                <label>macOS下载链接:</label>
                <input type="text" id="test-dmg-url" placeholder="https://example.com/app.dmg">
            </div>
            <button class="btn btn-success" onclick="testCreateVersion()">
                <i class="fas fa-rocket"></i> 创建测试版本
            </button>
            <div id="create-result" class="result" style="display: none;"></div>
        </div>

        <!-- 升级检查测试 -->
        <div class="test-section">
            <h3><i class="fas fa-search"></i> 升级检查测试</h3>
            <p>模拟APP检查更新</p>
            <div class="form-group">
                <label>当前版本:</label>
                <input type="text" id="current-version" placeholder="例如: 1.0.0" value="1.0.0">
            </div>
            <button class="btn btn-warning" onclick="testCheckUpdate()">
                <i class="fas fa-search"></i> 检查更新
            </button>
            <div id="check-result" class="result" style="display: none;"></div>
        </div>

        <!-- 清理测试数据 -->
        <div class="test-section">
            <h3><i class="fas fa-trash"></i> 清理测试数据</h3>
            <p>删除所有测试创建的版本数据</p>
            <button class="btn btn-danger" onclick="testDeleteAll()">
                <i class="fas fa-trash-alt"></i> 删除所有版本
            </button>
            <div id="delete-result" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        const API_URL = 'app_update_standalone.php';

        // 显示结果
        function showResult(elementId, success, message, data = null) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = 'result ' + (success ? 'success' : 'error');

            let content = message;
            if (data) {
                content += '\n\n数据:\n' + JSON.stringify(data, null, 2);
            }
            element.textContent = content;
        }

        // 测试上传配置
        async function testUploadConfig() {
            try {
                const response = await fetch('check_upload_config.php');
                const result = await response.json();
                showResult('config-result', result.data.ready_for_upload, result.message, result.data);
            } catch (error) {
                showResult('config-result', false, '配置检查失败: ' + error.message);
            }
        }

        // 测试API连接
        async function testApiConnection() {
            try {
                const response = await fetch(API_URL + '?action=list');
                const result = await response.json();
                showResult('api-result', true, 'API连接成功！', result);
            } catch (error) {
                showResult('api-result', false, 'API连接失败: ' + error.message);
            }
        }

        // 测试版本列表
        async function testVersionList() {
            try {
                const response = await fetch(API_URL + '?action=list');
                const result = await response.json();
                showResult('version-list-result', result.success, result.message, result.data);
            } catch (error) {
                showResult('version-list-result', false, '获取版本列表失败: ' + error.message);
            }
        }

        // 测试创建版本
        async function testCreateVersion() {
            const version = document.getElementById('test-version').value || '1.0.1';
            const title = document.getElementById('test-title').value || '测试版本 v' + version;
            const description = document.getElementById('test-description').value || '这是一个测试版本';
            const exeUrl = document.getElementById('test-exe-url').value || 'https://example.com/test.exe';
            const dmgUrl = document.getElementById('test-dmg-url').value || 'https://example.com/test.dmg';

            const formData = new FormData();
            formData.append('action', 'create');
            formData.append('version', version);
            formData.append('title', title);
            formData.append('description', description);
            formData.append('exe_download_url', exeUrl);
            formData.append('dmg_download_url', dmgUrl);
            formData.append('force_update', '1');

            try {
                const response = await fetch(API_URL, {
                    method: 'POST',
                    body: formData
                });
                const result = await response.json();
                showResult('create-result', result.success, result.message, result.data);
            } catch (error) {
                showResult('create-result', false, '创建版本失败: ' + error.message);
            }
        }

        // 测试检查更新
        async function testCheckUpdate() {
            const currentVersion = document.getElementById('current-version').value || '1.0.0';
            
            try {
                const response = await fetch(API_URL + '?action=check&current_version=' + encodeURIComponent(currentVersion));
                const result = await response.json();
                showResult('check-result', result.success, result.message, result.data);
            } catch (error) {
                showResult('check-result', false, '检查更新失败: ' + error.message);
            }
        }

        // 测试删除所有版本
        async function testDeleteAll() {
            if (!confirm('确定要删除所有版本吗？此操作不可恢复。')) {
                return;
            }

            const formData = new FormData();
            formData.append('action', 'delete_all');

            try {
                const response = await fetch(API_URL, {
                    method: 'POST',
                    body: formData
                });
                const result = await response.json();
                showResult('delete-result', result.success, result.message, result.data);
            } catch (error) {
                showResult('delete-result', false, '删除失败: ' + error.message);
            }
        }

        // 页面加载时自动测试配置和API连接
        window.addEventListener('load', function() {
            setTimeout(testUploadConfig, 300);
            setTimeout(testApiConnection, 800);
        });
    </script>
</body>
</html>
