<?php
// 修复验证码发送问题的脚本
session_start();
require_once '../includes/db.php';
require_once '../includes/functions.php';

// 检查登录状态
if (!isset($_SESSION['admin_user_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['error' => '请先登录']);
    exit;
}

header('Content-Type: application/json');

// 创建一个修复后的验证码发送函数
function sendVerificationEmailFixed($email, $code) {
    $settings = getSystemSettings();
    
    if (empty($settings['smtp_host']) || empty($settings['smtp_username']) || empty($settings['smtp_password'])) {
        error_log('SMTP配置不完整');
        return false;
    }
    
    // 使用与sendTestEmailWithConfig相同的PHPMailer检测逻辑
    $phpmailer_paths = [
        __DIR__ . '/../includes/PHPMailer/src/PHPMailer.php',
        __DIR__ . '/../includes/PHPMailer/PHPMailer.php'
    ];
    
    $phpmailer_found = false;
    foreach ($phpmailer_paths as $path) {
        if (file_exists($path)) {
            $base_dir = dirname($path);
            if (file_exists($base_dir . '/Exception.php')) {
                require_once $base_dir . '/Exception.php';
            }
            if (file_exists($base_dir . '/PHPMailer.php')) {
                require_once $base_dir . '/PHPMailer.php';
            }
            if (file_exists($base_dir . '/SMTP.php')) {
                require_once $base_dir . '/SMTP.php';
            }
            $phpmailer_found = true;
            break;
        }
    }
    
    if (!$phpmailer_found) {
        error_log('PHPMailer库未找到');
        return false;
    }
    
    // 根据不同版本使用不同的类名
    if (class_exists('PHPMailer\PHPMailer\PHPMailer')) {
        $mail = new PHPMailer\PHPMailer\PHPMailer(true);
        $encryption_starttls = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS;
        $encryption_smtps = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_SMTPS;
    } elseif (class_exists('PHPMailer')) {
        $mail = new PHPMailer(true);
        $encryption_starttls = 'tls';
        $encryption_smtps = 'ssl';
    } else {
        error_log('PHPMailer类未找到');
        return false;
    }
    
    try {
        // 使用与sendTestEmailWithConfig完全相同的配置逻辑
        $mail->isSMTP();
        $mail->Host = $settings['smtp_host'];
        $mail->SMTPAuth = true;
        $mail->Username = $settings['smtp_username'];
        $mail->Password = $settings['smtp_password'];
        
        // 根据端口选择加密方式（与测试邮件函数保持一致）
        $port = intval($settings['smtp_port'] ?? 465);
        $host = strtolower($settings['smtp_host']);
        
        if ($port == 465) {
            $mail->SMTPSecure = $encryption_smtps;
        } elseif ($port == 25) {
            // 25端口通常不使用加密
            $mail->SMTPSecure = false;
            $mail->SMTPAuth = true;
        } elseif (strpos($host, '163.com') !== false && $port == 25) {
            // 163邮箱特殊处理
            $mail->SMTPSecure = false;
            $mail->SMTPAuth = true;
        } else {
            $mail->SMTPSecure = $encryption_starttls;
        }
        
        $mail->Port = $port;
        $mail->CharSet = 'UTF-8';
        $mail->Timeout = 30;
        $mail->SMTPDebug = 0;
        
        $mail->setFrom($settings['smtp_username'], '小梅花AI客服系统');
        $mail->addAddress($email);
        
        $mail->isHTML(true);
        $mail->Subject = '【小梅花AI客服系统】登录验证码';
        
        $mail->Body = '
        <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
                <h1 style="color: white; margin: 0; font-size: 24px;">🌸 小梅花AI客服系统</h1>
                <p style="color: rgba(255,255,255,0.9); margin: 10px 0 0 0;">安全验证</p>
            </div>
            <div style="background: white; padding: 30px; border-radius: 0 0 10px 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                <h2 style="color: #333; margin-bottom: 20px;">您的登录验证码</h2>
                <div style="background: #f8f9fa; border: 2px dashed #667eea; border-radius: 8px; padding: 20px; text-align: center; margin: 20px 0;">
                    <span style="font-size: 32px; font-weight: bold; color: #667eea; letter-spacing: 5px;">' . $code . '</span>
                </div>
                <p style="color: #666; line-height: 1.6; margin-bottom: 15px;">
                    您正在登录小梅花AI客服系统管理后台，验证码有效期为5分钟。
                </p>
                <p style="color: #666; line-height: 1.6;">
                    如果这不是您的操作，请忽略此邮件。
                </p>
                <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; text-align: center;">
                    <p style="color: #999; font-size: 12px; margin: 0;">
                        © 2024 小梅花AI客服系统 | 技术支持：小梅花科技
                    </p>
                </div>
            </div>
        </div>';
        
        $mail->send();
        error_log("修复版验证码邮件发送成功: {$email}");
        return true;
    } catch (Exception $e) {
        error_log('修复版验证码邮件发送失败: ' . $e->getMessage());
        error_log('PHPMailer错误信息: ' . $mail->ErrorInfo);
        return false;
    }
}

try {
    // 获取管理员信息
    $stmt = $pdo->prepare("SELECT id, username, email FROM admin_users WHERE id = ?");
    $stmt->execute([$_SESSION['admin_user_id']]);
    $user = $stmt->fetch();
    
    if (!$user) {
        echo json_encode(['error' => '用户不存在']);
        exit;
    }
    
    if (empty($user['email'])) {
        echo json_encode(['error' => '用户邮箱未设置']);
        exit;
    }
    
    // 获取系统设置
    $settings = getSystemSettings();
    
    // 检查SMTP配置
    $smtp_configured = !empty($settings['smtp_host']) && 
                      !empty($settings['smtp_username']) && 
                      !empty($settings['smtp_password']);
    
    if (!$smtp_configured) {
        echo json_encode(['error' => 'SMTP配置不完整']);
        exit;
    }
    
    $action = $_GET['action'] ?? 'info';
    
    if ($action === 'test_original') {
        // 测试原始验证码发送函数
        $test_code = generateVerificationCode();
        $result = sendVerificationEmail($user['email'], $test_code);
        
        echo json_encode([
            'action' => 'test_original',
            'success' => $result,
            'test_code' => $test_code,
            'target_email' => $user['email'],
            'message' => $result ? '原始函数发送成功' : '原始函数发送失败'
        ]);
        
    } elseif ($action === 'test_fixed') {
        // 测试修复后的验证码发送函数
        $test_code = generateVerificationCode();
        $result = sendVerificationEmailFixed($user['email'], $test_code);
        
        echo json_encode([
            'action' => 'test_fixed',
            'success' => $result,
            'test_code' => $test_code,
            'target_email' => $user['email'],
            'message' => $result ? '修复版函数发送成功' : '修复版函数发送失败'
        ]);
        
    } elseif ($action === 'test_smtp') {
        // 测试SMTP配置函数
        $result = sendTestEmailWithConfig(
            $user['email'],
            $settings['smtp_host'],
            $settings['smtp_port'],
            $settings['smtp_username'],
            $settings['smtp_password']
        );
        
        echo json_encode([
            'action' => 'test_smtp',
            'success' => $result,
            'target_email' => $user['email'],
            'message' => $result ? 'SMTP测试函数发送成功' : 'SMTP测试函数发送失败'
        ]);
        
    } elseif ($action === 'compare_all') {
        // 比较所有三个函数
        $test_code1 = generateVerificationCode();
        $test_code2 = generateVerificationCode();
        
        $original_result = sendVerificationEmail($user['email'], $test_code1);
        $fixed_result = sendVerificationEmailFixed($user['email'], $test_code2);
        $smtp_result = sendTestEmailWithConfig(
            $user['email'],
            $settings['smtp_host'],
            $settings['smtp_port'],
            $settings['smtp_username'],
            $settings['smtp_password']
        );
        
        echo json_encode([
            'action' => 'compare_all',
            'results' => [
                'original_verification' => [
                    'success' => $original_result,
                    'test_code' => $test_code1
                ],
                'fixed_verification' => [
                    'success' => $fixed_result,
                    'test_code' => $test_code2
                ],
                'smtp_test' => [
                    'success' => $smtp_result
                ]
            ],
            'target_email' => $user['email'],
            'summary' => [
                'all_success' => $original_result && $fixed_result && $smtp_result,
                'fixed_vs_smtp' => $fixed_result === $smtp_result,
                'original_vs_fixed' => $original_result === $fixed_result
            ]
        ]);
        
    } else {
        // 显示配置信息
        echo json_encode([
            'action' => 'info',
            'user_email' => $user['email'],
            'smtp_config' => [
                'host' => $settings['smtp_host'],
                'port' => $settings['smtp_port'],
                'username' => $settings['smtp_username'],
                'configured' => $smtp_configured
            ],
            'available_actions' => [
                'test_original' => '测试原始验证码发送函数',
                'test_fixed' => '测试修复后的验证码发送函数',
                'test_smtp' => '测试SMTP配置函数',
                'compare_all' => '比较所有函数'
            ]
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'error' => '脚本执行失败',
        'message' => $e->getMessage()
    ]);
}
?> 