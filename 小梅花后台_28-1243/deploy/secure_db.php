<?php
/**
 * 安全数据库连接
 * 版本: v2.0.0
 * 功能: 使用加密配置和环境变量的安全数据库连接
 */

// 防止直接访问
if (!defined('SECURE_DB_ACCESS')) {
    http_response_code(403);
    exit('Access denied');
}

require_once __DIR__ . '/../api/config.php';

class SecureDatabaseConnection {
    private static $instance = null;
    private $pdo = null;
    private $config;
    
    private function __construct() {
        $this->config = SecureApiConfig::getInstance();
        $this->connect();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function connect() {
        try {
            $this->pdo = $this->config->getDatabaseConnection();
            
            // 设置安全选项
            $this->pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
            $this->pdo->setAttribute(PDO::ATTR_EMULATE_PREPARES, false);
            
            // 设置字符集
            $this->pdo->exec("SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci");
            
        } catch (PDOException $e) {
            // 记录错误但不暴露敏感信息
            error_log("Database connection failed: " . $e->getMessage());
            
            // 在生产环境中显示通用错误信息
            if (getenv('APP_ENV') === 'production') {
                throw new Exception("数据库连接失败，请联系系统管理员");
            } else {
                throw new Exception("数据库连接失败: " . $e->getMessage());
            }
        }
    }
    
    public function getConnection() {
        if ($this->pdo === null) {
            $this->connect();
        }
        return $this->pdo;
    }
    
    /**
     * 执行安全查询
     */
    public function query($sql, $params = []) {
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log("Query failed: " . $e->getMessage() . " SQL: " . $sql);
            throw new Exception("查询执行失败");
        }
    }
    
    /**
     * 获取单行数据
     */
    public function fetchOne($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }
    
    /**
     * 获取多行数据
     */
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }
    
    /**
     * 执行插入并返回ID
     */
    public function insert($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $this->pdo->lastInsertId();
    }
    
    /**
     * 执行更新/删除并返回受影响行数
     */
    public function execute($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }
    
    /**
     * 开始事务
     */
    public function beginTransaction() {
        return $this->pdo->beginTransaction();
    }
    
    /**
     * 提交事务
     */
    public function commit() {
        return $this->pdo->commit();
    }
    
    /**
     * 回滚事务
     */
    public function rollback() {
        return $this->pdo->rollback();
    }
    
    /**
     * 检查连接是否有效
     */
    public function isConnected() {
        try {
            $this->pdo->query('SELECT 1');
            return true;
        } catch (PDOException $e) {
            return false;
        }
    }
    
    /**
     * 重新连接
     */
    public function reconnect() {
        $this->pdo = null;
        $this->connect();
    }
    
    /**
     * 清理资源
     */
    public function __destruct() {
        $this->pdo = null;
    }
}

// 定义安全访问标识
define('SECURE_DB_ACCESS', true);

// 获取安全数据库连接实例
function getSecureDB() {
    return SecureDatabaseConnection::getInstance();
}

// 获取PDO连接（向后兼容）
function getSecurePDO() {
    return SecureDatabaseConnection::getInstance()->getConnection();
}

// 兼容性：提供全局$pdo变量（逐步废弃）
if (!isset($pdo)) {
    $pdo = getSecurePDO();
} 