<?php
// 获取统计数据
try {
    $stats = [];
    
    // 卡密统计
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM license_keys");
    $stats['total_keys'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) as active FROM license_keys WHERE status = 'active'");
    $stats['active_keys'] = $stmt->fetchColumn();
    
    // 脚本统计
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM scripts");
    $stats['total_scripts'] = $stmt->fetchColumn();
    
    // 今日新增
    $today_new_keys = $pdo->query("SELECT COUNT(*) FROM license_keys WHERE DATE(created_at) = DATE('now')")->fetchColumn();
    
} catch (Exception $e) {
    $stats = [
        'total_keys' => 0,
        'active_keys' => 0,
        'total_scripts' => 0
    ];
    $today_new_keys = 0;
}

// 获取各类型卡密数量
$types = ['yearly', 'monthly', 'weekly', 'daily'];
$type_counts = [];
foreach ($types as $type) {
    try {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM license_keys WHERE type = ?");
        $stmt->execute([$type]);
        $type_counts[$type] = $stmt->fetchColumn();
    } catch (Exception $e) {
        $type_counts[$type] = 0;
    }
}

// 获取最近活动
try {
    $recent_activities = $pdo->query("
        SELECT lk.key_value, lk.last_heartbeat, lk.last_used_ip, s.name as script_name 
        FROM license_keys lk 
        LEFT JOIN scripts s ON lk.script_id = s.id 
        WHERE lk.last_heartbeat IS NOT NULL 
        ORDER BY lk.last_heartbeat DESC 
        LIMIT 10
    ")->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $recent_activities = [];
}

// 计算使用率
$usage_rate = $stats['total_keys'] > 0 ? round(($stats['active_keys'] / $stats['total_keys']) * 100, 1) : 0;
?>

<div class="dashboard-container">
    <?php
    // 获取统计数据
    try {
        // 总卡密数量
        $total_keys = $pdo->query("SELECT COUNT(*) FROM license_keys")->fetchColumn();
        
        // 有效卡密数量
        $valid_keys = $pdo->query("SELECT COUNT(*) FROM license_keys WHERE expiry_date > datetime('now')")->fetchColumn();
        
        // 今日新增
        $today_keys = $pdo->query("SELECT COUNT(*) FROM license_keys WHERE DATE(created_at) = DATE('now')")->fetchColumn();
        
        // 脚本总数
        $total_scripts = $pdo->query("SELECT COUNT(*) FROM scripts")->fetchColumn();
    } catch (Exception $e) {
        $total_keys = 0;
        $valid_keys = 0;
        $today_keys = 0;
        $total_scripts = 0;
    }
    ?>

    <!-- 系统概览 -->
    <div class="card">
        <h2><i class="fas fa-chart-line"></i> 系统概览</h2>
        <div class="stats-grid">
            <div class="stat-item">
                <div class="value"><?php echo $total_keys; ?></div>
                <div class="label">总卡密数</div>
            </div>
            <div class="stat-item">
                <div class="value"><?php echo $valid_keys; ?></div>
                <div class="label">有效卡密</div>
            </div>
            <div class="stat-item">
                <div class="value"><?php echo $today_keys; ?></div>
                <div class="label">今日新增</div>
            </div>
            <div class="stat-item">
                <div class="value"><?php echo $total_scripts; ?></div>
                <div class="label">脚本总数</div>
            </div>
        </div>
    </div>

    <!-- 卡密类型分布 -->
    <div class="card">
        <h2><i class="fas fa-pie-chart"></i> 卡密类型分布</h2>
        <div class="stats-grid">
            <?php
            try {
                $types = $pdo->query("SELECT type, COUNT(*) as count FROM license_keys GROUP BY type")->fetchAll(PDO::FETCH_ASSOC);
                $type_names = [
                    'daily' => '天卡',
                    'monthly' => '月卡',
                    'yearly' => '年卡',
                    'permanent' => '永久卡'
                ];
                
                foreach ($type_names as $type => $name) {
                    $count = 0;
                    foreach ($types as $t) {
                        if ($t['type'] == $type) {
                            $count = $t['count'];
                            break;
                        }
                    }
                    echo "<div class='stat-item'>";
                    echo "<div class='value'>$count</div>";
                    echo "<div class='label'>$name</div>";
                    echo "</div>";
                }
            } catch (Exception $e) {
                echo "<div class='stat-item'><div class='value'>0</div><div class='label'>天卡</div></div>";
                echo "<div class='stat-item'><div class='value'>0</div><div class='label'>月卡</div></div>";
                echo "<div class='stat-item'><div class='value'>0</div><div class='label'>年卡</div></div>";
                echo "<div class='stat-item'><div class='value'>0</div><div class='label'>永久卡</div></div>";
            }
            ?>
        </div>
    </div>

    <!-- 系统状态 -->
    <div class="card">
        <h2><i class="fas fa-server"></i> 系统状态</h2>
        <div class="stats-grid">
            <div class="stat-item">
                <div class="value">正常</div>
                <div class="label">服务状态</div>
            </div>
            <div class="stat-item">
                <div class="value"><?php echo date('Y-m-d'); ?></div>
                <div class="label">当前日期</div>
            </div>
            <div class="stat-item">
                <div class="value">v1.2.0</div>
                <div class="label">系统版本</div>
            </div>
            <div class="stat-item">
                <div class="value">MySQL</div>
                <div class="label">数据库</div>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <h2><i class="fas fa-cog"></i> 系统状态</h2>
    <div class="table-container">
        <table>
            <thead>
                <tr>
                    <th>项目</th>
                    <th>状态</th>
                    <th>说明</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>数据库连接</td>
                    <td><span class="status-badge status-active">正常</span></td>
                    <td>MySQL 连接正常</td>
                </tr>
                <tr>
                    <td>API服务</td>
                    <td><span class="status-badge status-active">运行中</span></td>
                    <td>验证接口正常</td>
                </tr>
                <tr>
                    <td>系统版本</td>
                    <td><span class="status-badge status-active">v1.2.0</span></td>
                    <td>最新版本</td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<div class="card">
    <h2><i class="fas fa-rocket"></i> 快速操作</h2>
    <div class="form-group">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
            <a href="?page=keys" class="btn btn-primary glass-btn">
                <i class="fas fa-plus"></i> 添加卡密
            </a>
            <a href="?page=scripts" class="btn btn-secondary">
                <i class="fas fa-upload"></i> 管理脚本
            </a>
            <a href="?page=users" class="btn btn-secondary">
                <i class="fas fa-users"></i> 用户管理
            </a>
            <a href="?page=settings" class="btn btn-secondary">
                <i class="fas fa-cog"></i> 系统设置
            </a>
        </div>
    </div>
</div>

<div class="card">
    <h2><i class="fas fa-history"></i> 最近活动记录</h2>
    <?php if (empty($recent_activities)): ?>
        <p style="color: rgba(255,255,255,0.7); text-align: center; padding: 40px;">
            <i class="fas fa-info-circle"></i> 暂无活动记录
        </p>
    <?php else: ?>
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th><i class="fas fa-key"></i> 卡密</th>
                        <th><i class="fas fa-code"></i> 关联脚本</th>
                        <th><i class="fas fa-globe"></i> IP地址</th>
                        <th><i class="fas fa-clock"></i> 最后活动</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($recent_activities as $activity): ?>
                    <tr>
                        <td><?php echo htmlspecialchars(substr($activity['key_value'], 0, 15) . '...'); ?></td>
                        <td><?php echo htmlspecialchars($activity['script_name'] ?? '未绑定'); ?></td>
                        <td><?php echo htmlspecialchars($activity['last_used_ip'] ?? '未知'); ?></td>
                        <td><?php echo $activity['last_heartbeat'] ? format_time_ago($activity['last_heartbeat']) : '从未'; ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php endif; ?>
</div>

<style>
    /* 仪表盘页面专用样式 - 与脚本管理保持一致 */
    .dashboard-container {
        max-width: 100%;
        margin: 0;
        padding: 20px;
        box-sizing: border-box;
    }
    
    .card {
        background: rgba(255, 255, 255, 0.1) !important;
        backdrop-filter: blur(20px) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        border-radius: 15px !important;
        padding: 20px !important;
        margin-bottom: 20px !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
    }
    
    .card h2 {
        color: white !important;
        margin-bottom: 20px !important;
        font-size: 18px !important;
        font-weight: 600 !important;
        display: flex !important;
        align-items: center !important;
        gap: 10px !important;
    }
    
    .stats-grid {
        display: grid !important;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
        gap: 20px !important;
        margin-bottom: 30px !important;
    }
    
    .stat-item {
        background: rgba(255, 255, 255, 0.2) !important;
        backdrop-filter: blur(15px) !important;
        padding: 25px !important;
        border-radius: 15px !important;
        text-align: center !important;
        border: 1px solid rgba(255, 255, 255, 0.3) !important;
        transition: all 0.3s ease !important;
    }
    
    .stat-item:hover {
        transform: translateY(-5px) !important;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2) !important;
    }
    
    .stat-item .value {
        font-size: 36px !important;
        font-weight: 700 !important;
        color: white !important;
        margin-bottom: 8px !important;
    }
    
    .stat-item .label {
        font-size: 14px !important;
        color: rgba(255, 255, 255, 0.9) !important;
        font-weight: 500 !important;
    }

    .dashboard {
        padding: 30px;
        max-width: 1200px;
        margin: 0 auto;
    }

    .dashboard-header {
        margin-bottom: 30px;
    }

    .dashboard-header h2 {
        color: white;
        font-size: 28px;
        font-weight: 600;
        margin-bottom: 8px;
    }

    .dashboard-header p {
        color: rgba(255, 255, 255, 0.7);
        font-size: 16px;
    }

    .dashboard-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .dashboard-card {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        padding: 25px;
    }

    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .card-header h3 {
        color: white;
        font-size: 18px;
        font-weight: 600;
    }

    .view-all {
        color: #ff6b9d;
        text-decoration: none;
        font-size: 14px;
        transition: all 0.3s ease;
    }

    .view-all:hover {
        color: #c44569;
    }

    .status-indicator {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
    }

    .status-indicator.online {
        background: rgba(255, 255, 255, 0.2);
        color: white;
    }

    .activity-list {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }

    .activity-item {
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .activity-icon {
        width: 40px;
        height: 40px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #ff6b9d;
    }

    .activity-content p {
        color: white;
        font-size: 14px;
        margin-bottom: 2px;
    }

    .activity-content small {
        color: rgba(255, 255, 255, 0.6);
        font-size: 12px;
    }

    .system-status {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }

    .status-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
    }

    .status-label {
        color: rgba(255, 255, 255, 0.8);
        font-size: 14px;
    }

    .status-value {
        font-size: 14px;
        font-weight: 500;
    }

    .status-value.online {
        color: white;
    }

    .status-value.offline {
        color: #dc3545;
    }

    .quick-actions h3 {
        color: white;
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 20px;
    }

    .action-buttons {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }

    .action-btn {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 20px;
        text-decoration: none;
        color: white;
        display: flex;
        align-items: center;
        gap: 12px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .action-btn:hover {
        background: rgba(255, 255, 255, 0.15);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .action-btn i {
        font-size: 18px;
        color: #ff6b9d;
    }

    @media (max-width: 768px) {
        .dashboard {
            padding: 20px;
        }
        
        .stats-grid {
            grid-template-columns: 1fr;
        }
        
        .dashboard-grid {
            grid-template-columns: 1fr;
        }
        
        .action-buttons {
            grid-template-columns: 1fr;
        }
    }

    /* 毛玻璃按钮效果 */
    .glass-btn {
        background: rgba(255, 255, 255, 0.1) !important;
        backdrop-filter: blur(20px) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        transition: all 0.3s ease !important;
    }

    .glass-btn:hover {
        background: rgba(255, 255, 255, 0.2) !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
    }
</style> 