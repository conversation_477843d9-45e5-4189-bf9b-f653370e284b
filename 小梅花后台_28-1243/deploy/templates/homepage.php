<?php
// 处理首页内容保存
$success_message = '';
$error_message = '';

// 处理保存操作
if ($_POST && isset($_POST['action']) && $_POST['action'] === 'save_homepage') {
    $homepage_data = [
        'site_title' => $_POST['site_title'] ?? '',
        'hero_title' => $_POST['hero_title'] ?? '',
        'hero_subtitle' => $_POST['hero_subtitle'] ?? '',
        'hero_description' => $_POST['hero_description'] ?? '',
        'features_title' => $_POST['features_title'] ?? '',
        'capabilities_title' => $_POST['capabilities_title'] ?? '',
        'cta_title' => $_POST['cta_title'] ?? '',
        'cta_description' => $_POST['cta_description'] ?? '',
        'cta_button_text' => $_POST['cta_button_text'] ?? '',
        'cta_button_link' => $_POST['cta_button_link'] ?? '',
        'footer_text' => $_POST['footer_text'] ?? '',
        'features' => [],
        'capabilities' => []
    ];
    
    // 处理功能特性
    for ($i = 1; $i <= 6; $i++) {
        if (isset($_POST["feature_{$i}_icon"]) && isset($_POST["feature_{$i}_title"]) && isset($_POST["feature_{$i}_description"])) {
            $homepage_data['features'][] = [
                'icon' => $_POST["feature_{$i}_icon"],
                'title' => $_POST["feature_{$i}_title"],
                'description' => $_POST["feature_{$i}_description"]
            ];
        }
    }
    
    // 处理AI能力
    for ($i = 1; $i <= 8; $i++) {
        if (isset($_POST["capability_{$i}_title"]) && isset($_POST["capability_{$i}_description"])) {
            $homepage_data['capabilities'][] = [
                'title' => $_POST["capability_{$i}_title"],
                'description' => $_POST["capability_{$i}_description"]
            ];
        }
    }
    
    // 保存到数据库
    try {
        $stmt = $pdo->prepare("INSERT INTO system_settings (setting_key, setting_value) VALUES ('homepage_data', ?) ON DUPLICATE KEY UPDATE setting_value = ?");
        $json_data = json_encode($homepage_data, JSON_UNESCAPED_UNICODE);
        $stmt->execute([$json_data, $json_data]);
        
        // 更新index.html文件
        updateIndexHtml($homepage_data);
        
        $success_message = "首页内容已成功更新并发布！";
    } catch (Exception $e) {
        $error_message = "保存失败：" . $e->getMessage();
    }
}

// 获取当前首页数据
$homepage_data = getHomepageData();

function getHomepageData() {
    global $pdo;
    try {
        $stmt = $pdo->prepare("SELECT setting_value FROM system_settings WHERE setting_key = 'homepage_data'");
        $stmt->execute();
        $result = $stmt->fetch();
        
        if ($result) {
            return json_decode($result['setting_value'], true);
        }
    } catch (Exception $e) {
        // 忽略错误，使用默认数据
    }
    
    // 返回默认数据
    return [
        'site_title' => '小梅花AI客服系统 - 智能客服解决方案',
        'hero_title' => '小梅花AI客服系统',
        'hero_subtitle' => '智能、高效、全天候的AI客服解决方案',
        'hero_description' => '基于先进的人工智能技术，为您的企业提供24小时不间断的智能客服服务。通过深度学习和自然语言处理，让AI客服更懂您的客户需求，提升服务质量和效率。',
        'features_title' => '核心功能特性',
        'capabilities_title' => 'AI核心能力',
        'cta_title' => '开启智能客服新时代',
        'cta_description' => '让AI为您的客户服务赋能，提升效率，降低成本，创造更好的客户体验',
        'cta_button_text' => '联系我们',
        'cta_button_link' => 'mailto:<EMAIL>',
        'footer_text' => '© 2024 小梅花AI客服系统. 保留所有权利. | 技术支持：小梅花科技',
        'features' => [
            ['icon' => '🤖', 'title' => '智能对话', 'description' => '基于GPT技术的智能对话系统，能够理解复杂语义，提供准确、自然的回复，让客户感受到真人般的服务体验。'],
            ['icon' => '🕐', 'title' => '24小时在线', 'description' => '全天候不间断服务，无论何时客户咨询，都能得到及时响应。节假日、深夜时段也能保持高质量的客服服务。'],
            ['icon' => '📦', 'title' => '订单查询', 'description' => '集成订单管理系统，客户可以通过自然语言查询订单状态、物流信息、退换货进度等，操作简单便捷。'],
            ['icon' => '🧠', 'title' => '自主学习', 'description' => 'AI具备强大的自主学习能力，通过分析历史对话数据，不断优化回复质量，提升问题解决率。'],
            ['icon' => '📚', 'title' => '知识训练', 'description' => '支持上传产品手册、FAQ文档，AI会自动学习产品知识，确保回答的专业性和准确性。'],
            ['icon' => '🎯', 'title' => '精准识别', 'description' => '智能识别客户意图，自动分类问题类型，匹配最合适的解决方案，提高问题解决效率。']
        ],
        'capabilities' => [
            ['title' => '🔍 智能意图识别', 'description' => '准确识别客户咨询意图，包括售前咨询、售后服务、投诉建议等，自动路由到对应处理流程。'],
            ['title' => '💬 多轮对话管理', 'description' => '维护完整的对话上下文，支持复杂的多轮对话，确保对话的连贯性和逻辑性。'],
            ['title' => '📊 情感分析', 'description' => '实时分析客户情绪状态，识别不满或紧急情况，及时调整回复策略或转接人工客服。'],
            ['title' => '🔄 实时学习', 'description' => '从每次对话中学习，持续优化回复策略，知识库自动更新，服务质量不断提升。'],
            ['title' => '🌐 多平台集成', 'description' => '支持微信、QQ、网站、APP等多个平台接入，统一管理，数据互通。'],
            ['title' => '📈 数据分析', 'description' => '提供详细的服务数据分析，包括问题分布、解决率、满意度等关键指标。'],
            ['title' => '🛡️ 安全防护', 'description' => '内置敏感词过滤、反垃圾机制，保护企业品牌形象，确保服务质量。'],
            ['title' => '⚡ 快速响应', 'description' => '毫秒级响应速度，支持高并发访问，即使在业务高峰期也能保持稳定服务。']
        ]
    ];
}

function updateIndexHtml($data) {
    $template = '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . htmlspecialchars($data['site_title']) . '</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: \'Microsoft YaHei\', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            overflow-x: hidden;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        /* 头部导航 */
        .header {
            padding: 20px 0;
            position: relative;
            z-index: 100;
        }
        
        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 2rem;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .nav-links {
            display: flex;
            list-style: none;
            gap: 30px;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: opacity 0.3s ease;
        }
        
        .nav-links a:hover {
            opacity: 0.8;
        }
        
        /* 主要内容区 */
        .hero {
            position: relative;
            padding: 120px 0;
            text-align: center;
            color: white;
            overflow: hidden;
        }
        
        .hero-content {
            position: relative;
            z-index: 10;
        }
        
        .hero h1 {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            animation: slideInDown 1s ease-out;
        }
        
        .hero .subtitle {
            font-size: 1.3rem;
            margin-bottom: 30px;
            opacity: 0.9;
            animation: slideInUp 1s ease-out 0.2s both;
        }
        
        .hero .description {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 40px;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
            animation: fadeIn 1s ease-out 0.4s both;
        }
        
        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        /* 功能特性 */
        .features {
            padding: 80px 0;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
        }
        
        .features h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 60px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
            margin-bottom: 60px;
        }
        
        .feature-card {
            background: rgba(255,255,255,0.15);
            padding: 40px 30px;
            border-radius: 20px;
            text-align: center;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            animation: fadeInUp 0.8s ease-out;
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }
        
        .feature-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            display: block;
        }
        
        .feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        .feature-card p {
            font-size: 1rem;
            line-height: 1.6;
            opacity: 0.9;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* AI能力展示 */
        .ai-capabilities {
            padding: 80px 0;
        }
        
        .ai-capabilities h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 60px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .capabilities-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
        }
        
        .capability-item {
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            border-left: 4px solid #fff;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .capability-item:hover {
            background: rgba(255,255,255,0.2);
            transform: translateX(10px);
        }
        
        .capability-item h4 {
            font-size: 1.3rem;
            margin-bottom: 10px;
            font-weight: 600;
        }
        
        .capability-item p {
            font-size: 0.95rem;
            line-height: 1.5;
            opacity: 0.9;
        }
        
        /* CTA区域 */
        .cta {
            padding: 80px 0;
            text-align: center;
            background: rgba(0,0,0,0.1);
        }
        
        .cta h2 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .cta p {
            font-size: 1.2rem;
            margin-bottom: 40px;
            opacity: 0.9;
        }
        
        .cta-button {
            display: inline-block;
            padding: 15px 40px;
            background: rgba(255,255,255,0.2);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            border: 2px solid rgba(255,255,255,0.3);
        }
        
        .cta-button:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }
        
        /* 底部 */
        .footer {
            padding: 40px 0;
            text-align: center;
            border-top: 1px solid rgba(255,255,255,0.2);
            background: rgba(0,0,0,0.1);
        }
        
        .footer p {
            opacity: 0.7;
            font-size: 0.9rem;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .hero .subtitle {
                font-size: 1.1rem;
            }
            
            .hero .description {
                font-size: 1rem;
            }
            
            .features h2,
            .ai-capabilities h2,
            .cta h2 {
                font-size: 2rem;
            }
            
            .nav-links {
                display: none;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }
            
            .capabilities-list {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <nav class="nav">
                <div class="logo">小梅花AI客服</div>
                <ul class="nav-links">
                    <li><a href="#features">功能特性</a></li>
                    <li><a href="#capabilities">AI能力</a></li>
                    <li><a href="#contact">联系我们</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main>
        <section class="hero">
            <div class="container">
                <div class="hero-content">
                    <h1>' . htmlspecialchars($data['hero_title']) . '</h1>
                    <p class="subtitle">' . htmlspecialchars($data['hero_subtitle']) . '</p>
                    <p class="description">' . htmlspecialchars($data['hero_description']) . '</p>
                </div>
            </div>
        </section>

        <section id="features" class="features">
            <div class="container">
                <h2>' . htmlspecialchars($data['features_title']) . '</h2>
                <div class="features-grid">';
    
    foreach ($data['features'] as $feature) {
        $template .= '
                    <div class="feature-card">
                        <span class="feature-icon">' . htmlspecialchars($feature['icon']) . '</span>
                        <h3>' . htmlspecialchars($feature['title']) . '</h3>
                        <p>' . htmlspecialchars($feature['description']) . '</p>
                    </div>';
    }
    
    $template .= '
                </div>
            </div>
        </section>

        <section id="capabilities" class="ai-capabilities">
            <div class="container">
                <h2>' . htmlspecialchars($data['capabilities_title']) . '</h2>
                <div class="capabilities-list">';
    
    foreach ($data['capabilities'] as $capability) {
        $template .= '
                    <div class="capability-item">
                        <h4>' . htmlspecialchars($capability['title']) . '</h4>
                        <p>' . htmlspecialchars($capability['description']) . '</p>
                    </div>';
    }
    
    $template .= '
                </div>
            </div>
        </section>

        <section id="contact" class="cta">
            <div class="container">
                <h2>' . htmlspecialchars($data['cta_title']) . '</h2>
                <p>' . htmlspecialchars($data['cta_description']) . '</p>
                <a href="' . htmlspecialchars($data['cta_button_link']) . '" class="cta-button">' . htmlspecialchars($data['cta_button_text']) . '</a>
            </div>
        </section>
    </main>

    <footer class="footer">
        <div class="container">
            <p>' . htmlspecialchars($data['footer_text']) . '</p>
        </div>
    </footer>
</body>
</html>';
    
    file_put_contents('../index.html', $template);
}
?>

<div class="homepage-editor">
    <div class="editor-header">
        <h2><i class="fas fa-home"></i> 首页内容编辑器</h2>
        <p class="help-text">编辑首页的各个模块内容，修改后点击保存发布即可更新首页</p>
    </div>
    
    <?php if (isset($success_message) && $success_message): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i>
            <?php echo htmlspecialchars($success_message); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($error_message) && $error_message): ?>
        <div class="alert alert-error">
            <i class="fas fa-exclamation-circle"></i>
            <?php echo htmlspecialchars($error_message); ?>
        </div>
    <?php endif; ?>
    
    <form method="POST" class="homepage-form">
        <input type="hidden" name="action" value="save_homepage">
        
        <!-- 基本信息模块 -->
        <div class="editor-module">
            <div class="module-header">
                <h3><i class="fas fa-info-circle"></i> 基本信息</h3>
            </div>
            <div class="module-content">
                <div class="form-group">
                    <label>网站标题</label>
                    <input type="text" name="site_title" value="<?php echo htmlspecialchars($homepage_data['site_title']); ?>" required>
                </div>
            </div>
        </div>
        
        <!-- 主标题区域模块 -->
        <div class="editor-module">
            <div class="module-header">
                <h3><i class="fas fa-star"></i> 主标题区域</h3>
            </div>
            <div class="module-content">
                <div class="form-row">
                    <div class="form-group">
                        <label>主标题</label>
                        <input type="text" name="hero_title" value="<?php echo htmlspecialchars($homepage_data['hero_title']); ?>" required>
                    </div>
                    <div class="form-group">
                        <label>副标题</label>
                        <input type="text" name="hero_subtitle" value="<?php echo htmlspecialchars($homepage_data['hero_subtitle']); ?>" required>
                    </div>
                </div>
                <div class="form-group">
                    <label>描述内容</label>
                    <textarea name="hero_description" rows="4" required><?php echo htmlspecialchars($homepage_data['hero_description']); ?></textarea>
                </div>
            </div>
        </div>
        
        <!-- 功能特性模块 -->
        <div class="editor-module">
            <div class="module-header">
                <h3><i class="fas fa-cogs"></i> 功能特性</h3>
            </div>
            <div class="module-content">
                <div class="form-group">
                    <label>区块标题</label>
                    <input type="text" name="features_title" value="<?php echo htmlspecialchars($homepage_data['features_title']); ?>" required>
                </div>
                
                <div class="features-grid">
                    <?php for ($i = 0; $i < 6; $i++): ?>
                        <?php $feature = $homepage_data['features'][$i] ?? ['icon' => '', 'title' => '', 'description' => '']; ?>
                        <div class="feature-item">
                            <h4>功能 <?php echo $i + 1; ?></h4>
                            <div class="form-group">
                                <label>图标</label>
                                <input type="text" name="feature_<?php echo $i + 1; ?>_icon" value="<?php echo htmlspecialchars($feature['icon']); ?>" placeholder="🤖">
                            </div>
                            <div class="form-group">
                                <label>标题</label>
                                <input type="text" name="feature_<?php echo $i + 1; ?>_title" value="<?php echo htmlspecialchars($feature['title']); ?>">
                            </div>
                            <div class="form-group">
                                <label>描述</label>
                                <textarea name="feature_<?php echo $i + 1; ?>_description" rows="3"><?php echo htmlspecialchars($feature['description']); ?></textarea>
                            </div>
                        </div>
                    <?php endfor; ?>
                </div>
            </div>
        </div>
        
        <!-- AI能力模块 -->
        <div class="editor-module">
            <div class="module-header">
                <h3><i class="fas fa-brain"></i> AI核心能力</h3>
            </div>
            <div class="module-content">
                <div class="form-group">
                    <label>区块标题</label>
                    <input type="text" name="capabilities_title" value="<?php echo htmlspecialchars($homepage_data['capabilities_title']); ?>" required>
                </div>
                
                <div class="capabilities-grid">
                    <?php for ($i = 0; $i < 8; $i++): ?>
                        <?php $capability = $homepage_data['capabilities'][$i] ?? ['title' => '', 'description' => '']; ?>
                        <div class="capability-item">
                            <h4>能力 <?php echo $i + 1; ?></h4>
                            <div class="form-group">
                                <label>标题</label>
                                <input type="text" name="capability_<?php echo $i + 1; ?>_title" value="<?php echo htmlspecialchars($capability['title']); ?>">
                            </div>
                            <div class="form-group">
                                <label>描述</label>
                                <textarea name="capability_<?php echo $i + 1; ?>_description" rows="3"><?php echo htmlspecialchars($capability['description']); ?></textarea>
                            </div>
                        </div>
                    <?php endfor; ?>
                </div>
            </div>
        </div>
        
        <!-- 行动号召模块 -->
        <div class="editor-module">
            <div class="module-header">
                <h3><i class="fas fa-bullhorn"></i> 行动号召区域</h3>
            </div>
            <div class="module-content">
                <div class="form-row">
                    <div class="form-group">
                        <label>标题</label>
                        <input type="text" name="cta_title" value="<?php echo htmlspecialchars($homepage_data['cta_title']); ?>" required>
                    </div>
                    <div class="form-group">
                        <label>描述</label>
                        <input type="text" name="cta_description" value="<?php echo htmlspecialchars($homepage_data['cta_description']); ?>" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>按钮文字</label>
                        <input type="text" name="cta_button_text" value="<?php echo htmlspecialchars($homepage_data['cta_button_text']); ?>" required>
                    </div>
                    <div class="form-group">
                        <label>按钮链接</label>
                        <input type="url" name="cta_button_link" value="<?php echo htmlspecialchars($homepage_data['cta_button_link']); ?>" required>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 页脚模块 -->
        <div class="editor-module">
            <div class="module-header">
                <h3><i class="fas fa-copyright"></i> 页脚信息</h3>
            </div>
            <div class="module-content">
                <div class="form-group">
                    <label>版权信息</label>
                    <input type="text" name="footer_text" value="<?php echo htmlspecialchars($homepage_data['footer_text']); ?>" required>
                </div>
            </div>
        </div>
        
        <!-- 保存按钮 -->
        <div class="form-actions">
            <button type="submit" class="btn btn-primary btn-large">
                <i class="fas fa-save"></i> 保存并发布
            </button>
            <a href="../index.html" target="_blank" class="btn btn-secondary">
                <i class="fas fa-eye"></i> 预览首页
            </a>
        </div>
    </form>
</div>

<style>
.homepage-editor {
    padding: 0;
}

.editor-header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 30px;
    margin-bottom: 30px;
    text-align: center;
}

.editor-header h2 {
    color: white;
    font-size: 28px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.editor-header .help-text {
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
    margin: 0;
}

.editor-module {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    margin-bottom: 25px;
    overflow: hidden;
}

.module-header {
    background: rgba(255, 255, 255, 0.1);
    padding: 20px 25px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.module-header h3 {
    color: white;
    font-size: 18px;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.module-content {
    padding: 25px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    color: white;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
}

.form-group input,
.form-group textarea {
    width: 100%;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 12px 15px;
    color: white;
    font-size: 14px;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.5);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.capabilities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.feature-item,
.capability-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 20px;
}

.feature-item h4,
.capability-item h4 {
    color: white;
    font-size: 16px;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.form-actions {
    display: flex;
    gap: 15px;
    align-items: center;
    justify-content: center;
    padding: 30px;
    background: rgba(255, 255, 255, 0.05);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 30px;
}

.btn-large {
    padding: 15px 30px;
    font-size: 16px;
    font-weight: 600;
}

.alert {
    padding: 15px 20px;
    border-radius: 12px;
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
    font-weight: 500;
}

.alert-success {
    background: rgba(40, 167, 69, 0.2);
    border: 1px solid rgba(40, 167, 69, 0.3);
    color: #28a745;
}

.alert-error {
    background: rgba(220, 53, 69, 0.2);
    border: 1px solid rgba(220, 53, 69, 0.3);
    color: #dc3545;
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .features-grid,
    .capabilities-grid {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
}
</style> 