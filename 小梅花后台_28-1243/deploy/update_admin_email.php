<?php
// 更新管理员邮箱脚本
session_start();
require_once '../includes/db.php';
require_once '../includes/functions.php';

// 检查登录状态
if (!isset($_SESSION['admin_user_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => '请先登录']);
    exit;
}

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $new_email = trim($_POST['email'] ?? '');
    
    if (empty($new_email)) {
        echo json_encode(['success' => false, 'message' => '邮箱地址不能为空']);
        exit;
    }
    
    if (!filter_var($new_email, FILTER_VALIDATE_EMAIL)) {
        echo json_encode(['success' => false, 'message' => '邮箱地址格式不正确']);
        exit;
    }
    
    try {
        $stmt = $pdo->prepare("UPDATE admin_users SET email = ?, updated_at = NOW() WHERE id = ?");
        $result = $stmt->execute([$new_email, $_SESSION['admin_user_id']]);
        
        if ($result) {
            echo json_encode([
                'success' => true, 
                'message' => '邮箱地址更新成功',
                'new_email' => $new_email
            ]);
        } else {
            echo json_encode(['success' => false, 'message' => '邮箱地址更新失败']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => '数据库错误：' . $e->getMessage()]);
    }
} else {
    // GET请求，返回当前邮箱
    try {
        $stmt = $pdo->prepare("SELECT email FROM admin_users WHERE id = ?");
        $stmt->execute([$_SESSION['admin_user_id']]);
        $user = $stmt->fetch();
        
        if ($user) {
            echo json_encode([
                'success' => true,
                'current_email' => $user['email']
            ]);
        } else {
            echo json_encode(['success' => false, 'message' => '用户不存在']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => '查询失败：' . $e->getMessage()]);
    }
}
?> 