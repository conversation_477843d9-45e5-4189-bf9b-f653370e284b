-- 小梅花智能AI客服系统 - 完整数据库安装脚本
-- 版本: v2.0.0 - 优化合并版
-- 日期: 2024-01-20
-- 功能: 完整安装所有表结构和数据，包含所有功能模块
-- 兼容: MySQL 5.7+ 和宝塔环境

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ====================================
-- 核心系统表
-- ====================================

-- 1. 管理员用户表
DROP TABLE IF EXISTS `admin_users`;
CREATE TABLE `admin_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_login` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员用户表';

-- 2. 系统设置表
DROP TABLE IF EXISTS `system_settings`;
CREATE TABLE `system_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text,
  `description` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统设置表';

-- 3. SMTP配置表
DROP TABLE IF EXISTS `smtp_config`;
CREATE TABLE `smtp_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `smtp_host` varchar(255) NOT NULL,
  `smtp_port` int(11) NOT NULL DEFAULT 465,
  `smtp_username` varchar(255) NOT NULL,
  `smtp_password` varchar(255) NOT NULL,
  `from_email` varchar(255) DEFAULT NULL,
  `from_name` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_smtp` (`user_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='SMTP配置表';

-- ====================================
-- 安全验证表
-- ====================================

-- 4. 信任设备表
DROP TABLE IF EXISTS `trusted_devices`;
CREATE TABLE `trusted_devices` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `device_name` varchar(255) NOT NULL,
  `device_fingerprint` varchar(255) NOT NULL,
  `user_agent` text,
  `ip_address` varchar(45) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_device` (`user_id`, `device_fingerprint`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_fingerprint` (`device_fingerprint`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='信任设备表';

-- 5. 验证码表
DROP TABLE IF EXISTS `verification_codes`;
CREATE TABLE `verification_codes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `code` varchar(6) NOT NULL,
  `type` varchar(50) NOT NULL,
  `expires_at` timestamp NOT NULL,
  `is_used` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_code` (`user_id`, `code`),
  KEY `idx_expires` (`expires_at`),
  KEY `idx_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='验证码表';

-- 6. 登录日志表
DROP TABLE IF EXISTS `login_logs`;
CREATE TABLE `login_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `device_info` text,
  `device_fingerprint` varchar(255) DEFAULT NULL,
  `login_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `login_status` enum('success','failed','blocked') NOT NULL DEFAULT 'success',
  `is_trusted_device` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_login_time` (`login_time`),
  KEY `idx_status` (`login_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='登录日志表';

-- ====================================
-- 业务功能表
-- ====================================

-- 7. 卡密表（统一命名为license_keys）
DROP TABLE IF EXISTS `cards`;
DROP TABLE IF EXISTS `license_keys`;
CREATE TABLE `license_keys` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key_value` varchar(255) NOT NULL COMMENT '卡密值',
  `license_key` varchar(255) DEFAULT NULL COMMENT '卡密值（兼容字段）',
  `card_number` varchar(50) DEFAULT NULL COMMENT '卡号（兼容字段）',
  `type` enum('hour','day','month','year','hourly','daily','monthly','half_yearly','yearly') NOT NULL DEFAULT 'day',
  `card_type` enum('hour','day','month','year') DEFAULT NULL COMMENT '卡密类型（兼容字段）',
  `duration` int(11) NOT NULL DEFAULT 1,
  `status` enum('unused','used','expired','active','banned','disabled') NOT NULL DEFAULT 'unused',
  `used_by` varchar(100) DEFAULT NULL,
  `used_at` timestamp NULL DEFAULT NULL,
  `last_heartbeat` timestamp NULL DEFAULT NULL,
  `last_used_ip` varchar(45) DEFAULT NULL,
  `script_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `store_name` varchar(200) DEFAULT NULL COMMENT '绑定的店铺名称',
  `wechat_store_id` varchar(100) DEFAULT NULL COMMENT '绑定的微信小店ID',
  `user_wechat` varchar(100) DEFAULT NULL COMMENT '用户微信号',
  `ai_settings_id` int(11) DEFAULT NULL COMMENT '关联的AI设置ID',
  `has_customer_service` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否开通客服功能',
  `has_product_listing` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否开通上架产品功能',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `expires_at` timestamp NULL DEFAULT NULL,
  `expiry_date` timestamp NULL DEFAULT NULL COMMENT '过期日期（兼容字段）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `key_value` (`key_value`),
  UNIQUE KEY `card_number` (`card_number`),
  UNIQUE KEY `license_key` (`license_key`),
  KEY `idx_status` (`status`),
  KEY `idx_type` (`type`),
  KEY `idx_card_type` (`card_type`),
  KEY `idx_script_id` (`script_id`),
  KEY `idx_store_info` (`store_name`, `wechat_store_id`),
  KEY `idx_ai_settings` (`ai_settings_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='卡密表';

-- 8. 脚本表
DROP TABLE IF EXISTS `scripts`;
CREATE TABLE `scripts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text,
  `content` longtext DEFAULT NULL COMMENT '脚本内容（兼容字段）',
  `script_code` longtext NOT NULL COMMENT '脚本代码',
  `loader_code` longtext DEFAULT NULL COMMENT '加载器代码',
  `version` varchar(50) NOT NULL DEFAULT '1.0.0',
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活（兼容字段）',
  `default_ai_enabled` tinyint(1) DEFAULT 0 COMMENT '默认是否启用AI',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`),
  KEY `idx_status` (`status`),
  KEY `idx_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='脚本表';

-- 9. 用户表
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `status` enum('online','offline') NOT NULL DEFAULT 'offline',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_login` timestamp NULL DEFAULT NULL,
  `key_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  KEY `idx_status` (`status`),
  KEY `idx_key_id` (`key_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- ====================================
-- AI功能表
-- ====================================

-- 10. AI设置表
DROP TABLE IF EXISTS `ai_settings`;
CREATE TABLE `ai_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `script_id` int(11) NOT NULL COMMENT '关联的脚本ID',
  `ai_enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用AI回复',
  `api_key` varchar(255) DEFAULT NULL COMMENT 'DeepSeek API密钥',
  `model` varchar(100) DEFAULT 'deepseek-chat' COMMENT 'AI模型',
  `deep_thinking_enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用深度思考(R1)',
  `scan_interval` int(11) DEFAULT 5 COMMENT '扫描间隔(秒)',
  `reply_delay` int(11) DEFAULT 0 COMMENT '回复延迟(秒)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_script_ai` (`script_id`),
  KEY `idx_script_id` (`script_id`),
  KEY `idx_ai_enabled` (`ai_enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI设置表';

-- ====================================
-- 日志表
-- ====================================

-- 11. API日志表
DROP TABLE IF EXISTS `api_logs`;
CREATE TABLE `api_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `endpoint` varchar(100) NOT NULL,
  `method` varchar(10) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` text,
  `request_data` text,
  `response_data` text,
  `status_code` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_endpoint` (`endpoint`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_status_code` (`status_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API日志表';

-- ====================================
-- 插入默认数据
-- ====================================

-- 插入默认管理员用户（用户名：admin，密码：123456）
INSERT INTO `admin_users` (`username`, `password`, `email`) VALUES 
('admin', 'e10adc3949ba59abbe56e057f20f883e', '<EMAIL>');

-- 插入默认系统设置
INSERT INTO `system_settings` (`setting_key`, `setting_value`, `description`) VALUES
('smtp_host', 'smtp.163.com', 'SMTP服务器地址'),
('smtp_port', '465', 'SMTP端口'),
('smtp_username', '', 'SMTP用户名'),
('smtp_password', '', 'SMTP密码'),
('from_email', '', '发件人邮箱'),
('from_name', '小梅花AI客服系统', '发件人名称'),
('email_verification_enabled', '0', '是否启用邮箱验证'),
('login_notification_enabled', '0', '是否启用登录通知'),
('site_title', '小梅花AI客服系统', '网站标题'),
('site_description', '智能客服管理系统', '网站描述');

-- INSERT INTO `license_keys` (`key_value`, `license_key`, `card_number`, `type`, `card_type`, `duration`, `status`, `expires_at`, `expiry_date`, `store_name`) VALUES (已注释掉不完整的INSERT语句)

-- INSERT INTO `scripts` (`name`, `description`, `content`, `script_code`, `version`, `default_ai_enabled`) VALUES (已注释掉不完整的INSERT语句)

-- 为现有脚本创建默认AI设置（已注释：可能导致错误的子查询）
-- INSERT INTO `ai_settings` (`script_id`, `ai_enabled`, `api_key`, `model`, `deep_thinking_enabled`, `scan_interval`, `reply_delay`)
-- SELECT 
--     s.id as script_id,
--     0 as ai_enabled,
--     NULL as api_key,
--     'deepseek-chat' as model,
--     0 as deep_thinking_enabled,
--     5 as scan_interval,
--     0 as reply_delay
-- FROM scripts s
-- WHERE NOT EXISTS (
--     SELECT 1 FROM ai_settings WHERE script_id = s.id
-- );

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- ====================================
-- 安装完成信息
-- ====================================

SELECT 
    '数据库安装完成！' as '安装状态',
    'admin' as '默认用户名',
    '123456' as '默认密码',
    '所有功能模块已安装' as '功能状态',
    '包含：用户管理、安全验证、卡密管理、脚本管理、AI设置、日志记录' as '功能详情';
