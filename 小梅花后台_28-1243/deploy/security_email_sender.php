<?php
// 安全邮件发送脚本 - 处理登录通知和陌生设备访问警告
set_time_limit(30);
ignore_user_abort(true);

require_once '../includes/db.php';
require_once '../includes/functions.php';

// 检查是否为内部调用
$is_internal_call = (php_sapi_name() === 'cli') || 
                   (isset($_SERVER['HTTP_X_INTERNAL_CALL']) && $_SERVER['HTTP_X_INTERNAL_CALL'] === 'true') ||
                   (isset($_SERVER['HTTP_X_SECURITY_TOKEN']) && $_SERVER['HTTP_X_SECURITY_TOKEN'] === 'xiaomeihua_security_2024');

if (!$is_internal_call) {
    http_response_code(403);
    exit('Access denied');
}

/**
 * 发送优化版登录通知邮件
 */
function sendEnhancedLoginNotification($data) {
    try {
        $settings = getSystemSettings();
        
        if (($settings['login_notification_enabled'] ?? '0') !== '1') {
            return ['success' => true, 'message' => '登录提醒未启用'];
        }
        
        if (empty($settings['smtp_host']) || empty($settings['smtp_username']) || empty($settings['smtp_password'])) {
            return ['success' => false, 'message' => 'SMTP配置不完整'];
        }
        
        // 智能检测PHPMailer路径
        $phpmailer_paths = [
            __DIR__ . '/../includes/PHPMailer/src/PHPMailer.php',
            __DIR__ . '/../includes/PHPMailer/PHPMailer.php'
        ];
        
        $phpmailer_found = false;
        foreach ($phpmailer_paths as $path) {
            if (file_exists($path)) {
                $base_dir = dirname($path);
                if (file_exists($base_dir . '/Exception.php')) {
                    require_once $base_dir . '/Exception.php';
                }
                if (file_exists($base_dir . '/PHPMailer.php')) {
                    require_once $base_dir . '/PHPMailer.php';
                }
                if (file_exists($base_dir . '/SMTP.php')) {
                    require_once $base_dir . '/SMTP.php';
                }
                $phpmailer_found = true;
                break;
            }
        }
        
        if (!$phpmailer_found) {
            return ['success' => false, 'message' => 'PHPMailer库未找到'];
        }
        
        // 根据不同版本使用不同的类名
        if (class_exists('PHPMailer\PHPMailer\PHPMailer')) {
            $mail = new PHPMailer\PHPMailer\PHPMailer(true);
            $encryption_starttls = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS;
            $encryption_smtps = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_SMTPS;
        } elseif (class_exists('PHPMailer')) {
            $mail = new PHPMailer(true);
            $encryption_starttls = 'tls';
            $encryption_smtps = 'ssl';
        } else {
            return ['success' => false, 'message' => 'PHPMailer类未找到'];
        }
        
        // SMTP配置
        $mail->isSMTP();
        $mail->Host = $settings['smtp_host'];
        $mail->SMTPAuth = true;
        $mail->Username = $settings['smtp_username'];
        $mail->Password = $settings['smtp_password'];
        
        $port = intval($settings['smtp_port'] ?? 465);
        if ($port == 465) {
            $mail->SMTPSecure = $encryption_smtps;
        } elseif ($port == 587) {
            $mail->SMTPSecure = $encryption_starttls;
        } elseif ($port == 25) {
            $mail->SMTPSecure = false;
            $mail->SMTPAuth = true;
        } else {
            $mail->SMTPSecure = $encryption_starttls;
        }
        
        $mail->Port = $port;
        $mail->CharSet = 'UTF-8';
        $mail->Timeout = 30;
        $mail->SMTPDebug = 0;
        
        $mail->setFrom($settings['smtp_username'], '小梅花AI客服系统安全中心');
        $mail->addAddress($data['email']);
        
        $mail->isHTML(true);
        
        // 根据设备信任状态设置不同的邮件内容
        $trust_status = $data['is_trusted'] ? '信任设备' : '⚠️ 陌生设备';
        $trust_color = $data['is_trusted'] ? '#28a745' : '#dc3545';
        $trust_icon = $data['is_trusted'] ? '✅' : '⚠️';
        
        $mail->Subject = "【小梅花AI】{$trust_status}登录提醒 - " . date('Y-m-d H:i');
        
        $mail->Body = '
        <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; background: #f8f9fa;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 15px 15px 0 0;">
                <h1 style="color: white; margin: 0; font-size: 24px;">🌸 小梅花AI安全中心</h1>
                <p style="color: rgba(255,255,255,0.9); margin: 10px 0 0 0;">登录安全提醒</p>
            </div>
            
            <div style="background: white; padding: 30px; border-radius: 0 0 15px 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                <div style="background: ' . ($data['is_trusted'] ? '#d4edda' : '#f8d7da') . '; border: 2px solid ' . $trust_color . '; border-radius: 12px; padding: 20px; margin-bottom: 25px; text-align: center;">
                    <h2 style="color: ' . $trust_color . '; margin: 0 0 10px 0; font-size: 20px;">' . $trust_icon . ' ' . $trust_status . '登录</h2>
                    <p style="color: #333; margin: 0; font-size: 16px;">您的管理员账户已成功登录</p>
                </div>
                
                <div style="background: #f8f9fa; border-radius: 10px; padding: 20px; margin: 20px 0;">
                    <h3 style="color: #333; margin-bottom: 15px; font-size: 16px;">📊 登录详情</h3>
                    <table style="width: 100%; border-collapse: collapse;">
                        <tr>
                            <td style="padding: 8px 0; color: #666; font-weight: bold; width: 100px;">登录时间：</td>
                            <td style="padding: 8px 0; color: #333;">' . $data['time'] . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; color: #666; font-weight: bold;">登录IP：</td>
                            <td style="padding: 8px 0; color: #333;">' . $data['ip'] . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; color: #666; font-weight: bold;">登录地点：</td>
                            <td style="padding: 8px 0; color: #333;">' . $data['location'] . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; color: #666; font-weight: bold;">设备信息：</td>
                            <td style="padding: 8px 0; color: #333;">' . htmlspecialchars($data['device_name'] ?? '未知设备') . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; color: #666; font-weight: bold;">设备状态：</td>
                            <td style="padding: 8px 0; color: ' . $trust_color . '; font-weight: bold;">' . $trust_status . '</td>
                        </tr>
                    </table>
                </div>';
        
        if (!$data['is_trusted']) {
            $mail->Body .= '
                <div style="background: #fff3cd; border: 2px solid #ffc107; border-radius: 10px; padding: 20px; margin: 20px 0;">
                    <h3 style="color: #856404; margin-bottom: 10px;">⚠️ 安全提醒</h3>
                    <p style="color: #856404; margin: 0; line-height: 1.6;">
                        这是一个<strong>陌生设备</strong>的登录，如果这不是您的操作，请立即：
                    </p>
                    <ul style="color: #856404; margin: 10px 0; padding-left: 20px;">
                        <li>修改管理员密码</li>
                        <li>检查系统设置</li>
                        <li>查看最近的登录记录</li>
                        <li>启用更严格的安全设置</li>
                    </ul>
                </div>';
        } else {
            $mail->Body .= '
                <div style="background: #d1ecf1; border: 2px solid #17a2b8; border-radius: 10px; padding: 20px; margin: 20px 0;">
                    <h3 style="color: #0c5460; margin-bottom: 10px;">✅ 安全确认</h3>
                    <p style="color: #0c5460; margin: 0;">
                        这是来自您的<strong>信任设备</strong>的正常登录，系统运行安全。
                    </p>
                </div>';
        }
        
        $mail->Body .= '
                <div style="text-align: center; margin: 25px 0;">
                    <a href="https://xiaomeihuakefu.cn/xuxuemei/" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 12px 25px; text-decoration: none; border-radius: 8px; font-weight: bold;">访问管理后台</a>
                </div>
                
                <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; text-align: center;">
                    <p style="color: #999; font-size: 12px; margin: 0;">
                        © 2024 小梅花AI客服系统安全中心 | 此邮件由系统自动发送，请勿回复
                    </p>
                </div>
            </div>
        </div>';
        
        $mail->send();
        
        return [
            'success' => true,
            'message' => '登录提醒发送成功',
            'email' => $data['email'],
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
    } catch (Exception $e) {
        error_log('登录提醒邮件发送失败: ' . $e->getMessage());
        return [
            'success' => false,
            'message' => '发送失败: ' . $e->getMessage()
        ];
    }
}

/**
 * 发送陌生设备访问警告邮件
 */
function sendUntrustedAccessAlert($data) {
    try {
        $settings = getSystemSettings();
        
        if (($settings['login_notification_enabled'] ?? '0') !== '1') {
            return ['success' => true, 'message' => '安全提醒未启用'];
        }
        
        if (empty($settings['smtp_host']) || empty($settings['smtp_username']) || empty($settings['smtp_password'])) {
            return ['success' => false, 'message' => 'SMTP配置不完整'];
        }
        
        // 智能检测PHPMailer路径
        $phpmailer_paths = [
            __DIR__ . '/../includes/PHPMailer/src/PHPMailer.php',
            __DIR__ . '/../includes/PHPMailer/PHPMailer.php'
        ];
        
        $phpmailer_found = false;
        foreach ($phpmailer_paths as $path) {
            if (file_exists($path)) {
                $base_dir = dirname($path);
                if (file_exists($base_dir . '/Exception.php')) {
                    require_once $base_dir . '/Exception.php';
                }
                if (file_exists($base_dir . '/PHPMailer.php')) {
                    require_once $base_dir . '/PHPMailer.php';
                }
                if (file_exists($base_dir . '/SMTP.php')) {
                    require_once $base_dir . '/SMTP.php';
                }
                $phpmailer_found = true;
                break;
            }
        }
        
        if (!$phpmailer_found) {
            return ['success' => false, 'message' => 'PHPMailer库未找到'];
        }
        
        // 根据不同版本使用不同的类名
        if (class_exists('PHPMailer\PHPMailer\PHPMailer')) {
            $mail = new PHPMailer\PHPMailer\PHPMailer(true);
            $encryption_starttls = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS;
            $encryption_smtps = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_SMTPS;
        } elseif (class_exists('PHPMailer')) {
            $mail = new PHPMailer(true);
            $encryption_starttls = 'tls';
            $encryption_smtps = 'ssl';
        } else {
            return ['success' => false, 'message' => 'PHPMailer类未找到'];
        }
        
        // SMTP配置
        $mail->isSMTP();
        $mail->Host = $settings['smtp_host'];
        $mail->SMTPAuth = true;
        $mail->Username = $settings['smtp_username'];
        $mail->Password = $settings['smtp_password'];
        
        $port = intval($settings['smtp_port'] ?? 465);
        if ($port == 465) {
            $mail->SMTPSecure = $encryption_smtps;
        } elseif ($port == 587) {
            $mail->SMTPSecure = $encryption_starttls;
        } elseif ($port == 25) {
            $mail->SMTPSecure = false;
            $mail->SMTPAuth = true;
        } else {
            $mail->SMTPSecure = $encryption_starttls;
        }
        
        $mail->Port = $port;
        $mail->CharSet = 'UTF-8';
        $mail->Timeout = 30;
        $mail->SMTPDebug = 0;
        
        $mail->setFrom($settings['smtp_username'], '小梅花AI客服系统安全中心');
        $mail->addAddress($data['email']);
        
        $mail->isHTML(true);
        $mail->Subject = '🚨【紧急安全警告】陌生设备访问后台敏感页面 - ' . date('Y-m-d H:i');
        
        // 页面名称映射
        $page_names = [
            'dashboard' => '仪表盘',
            'keys' => '卡密管理',
            'scripts' => '脚本管理',
            'users' => '用户管理',
            'analytics' => '数据分析',
            'settings' => '系统设置'
        ];
        
        $page_display_name = $page_names[$data['page']] ?? $data['page'];
        
        $mail->Body = '
        <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; background: #f8f9fa;">
            <div style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); padding: 30px; text-align: center; border-radius: 15px 15px 0 0;">
                <h1 style="color: white; margin: 0; font-size: 24px;">🚨 小梅花AI安全警报</h1>
                <p style="color: rgba(255,255,255,0.9); margin: 10px 0 0 0;">陌生设备访问警告</p>
            </div>
            
            <div style="background: white; padding: 30px; border-radius: 0 0 15px 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                <div style="background: #f8d7da; border: 3px solid #dc3545; border-radius: 12px; padding: 20px; margin-bottom: 25px; text-align: center;">
                    <h2 style="color: #721c24; margin: 0 0 10px 0; font-size: 20px;">⚠️ 高危安全事件</h2>
                    <p style="color: #721c24; margin: 0; font-size: 16px; font-weight: bold;">检测到陌生设备访问敏感页面</p>
                </div>
                
                <div style="background: #fff3cd; border: 2px solid #ffc107; border-radius: 10px; padding: 20px; margin: 20px 0;">
                    <h3 style="color: #856404; margin-bottom: 15px; font-size: 16px;">🎯 访问详情</h3>
                    <table style="width: 100%; border-collapse: collapse;">
                        <tr>
                            <td style="padding: 8px 0; color: #666; font-weight: bold; width: 100px;">访问时间：</td>
                            <td style="padding: 8px 0; color: #333;">' . $data['time'] . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; color: #666; font-weight: bold;">访问页面：</td>
                            <td style="padding: 8px 0; color: #dc3545; font-weight: bold;">' . $page_display_name . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; color: #666; font-weight: bold;">访问IP：</td>
                            <td style="padding: 8px 0; color: #333;">' . $data['ip'] . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; color: #666; font-weight: bold;">地理位置：</td>
                            <td style="padding: 8px 0; color: #333;">' . $data['location'] . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; color: #666; font-weight: bold;">设备指纹：</td>
                            <td style="padding: 8px 0; color: #333; font-family: monospace; font-size: 12px;">' . substr($data['device_fingerprint'], 0, 16) . '...</td>
                        </tr>
                    </table>
                </div>
                
                <div style="background: #f8d7da; border: 2px solid #dc3545; border-radius: 10px; padding: 20px; margin: 20px 0;">
                    <h3 style="color: #721c24; margin-bottom: 10px;">🚨 紧急安全措施</h3>
                    <p style="color: #721c24; margin: 0 0 15px 0; font-weight: bold;">
                        系统已自动阻止陌生设备访问敏感内容，但仍建议您立即采取以下措施：
                    </p>
                    <ol style="color: #721c24; margin: 0; padding-left: 20px; line-height: 1.8;">
                        <li><strong>立即修改管理员密码</strong></li>
                        <li><strong>检查所有信任设备列表</strong></li>
                        <li><strong>查看最近的登录记录</strong></li>
                        <li><strong>启用更严格的安全设置</strong></li>
                        <li><strong>检查系统是否存在异常</strong></li>
                    </ol>
                </div>
                
                <div style="background: #d1ecf1; border: 2px solid #17a2b8; border-radius: 10px; padding: 20px; margin: 20px 0;">
                    <h3 style="color: #0c5460; margin-bottom: 10px;">🛡️ 系统保护状态</h3>
                    <p style="color: #0c5460; margin: 0;">
                        ✅ 陌生设备已被阻止访问敏感内容<br>
                        ✅ 访问记录已保存到安全日志<br>
                        ✅ 实时监控系统正常运行
                    </p>
                </div>
                
                <div style="text-align: center; margin: 25px 0;">
                    <a href="https://xiaomeihuakefu.cn/xuxuemei/?page=settings" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; padding: 12px 25px; text-decoration: none; border-radius: 8px; font-weight: bold; margin-right: 10px;">立即检查安全设置</a>
                    <a href="https://xiaomeihuakefu.cn/xuxuemei/" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 12px 25px; text-decoration: none; border-radius: 8px; font-weight: bold;">访问管理后台</a>
                </div>
                
                <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; text-align: center;">
                    <p style="color: #999; font-size: 12px; margin: 0;">
                        © 2024 小梅花AI客服系统安全中心 | 此为系统安全警报，请勿回复<br>
                        如需帮助请联系技术支持
                    </p>
                </div>
            </div>
        </div>';
        
        $mail->send();
        
        return [
            'success' => true,
            'message' => '安全警告邮件发送成功',
            'email' => $data['email'],
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
    } catch (Exception $e) {
        error_log('安全警告邮件发送失败: ' . $e->getMessage());
        return [
            'success' => false,
            'message' => '发送失败: ' . $e->getMessage()
        ];
    }
}

/**
 * 发送入侵者警告邮件
 */
function sendIntruderAlert($data) {
    try {
        $settings = getSystemSettings();
        
        if (($settings['login_notification_enabled'] ?? '0') !== '1') {
            return ['success' => true, 'message' => '安全提醒未启用'];
        }
        
        if (empty($settings['smtp_host']) || empty($settings['smtp_username']) || empty($settings['smtp_password'])) {
            return ['success' => false, 'message' => 'SMTP配置不完整'];
        }
        
        // 智能检测PHPMailer路径
        $phpmailer_paths = [
            __DIR__ . '/../includes/PHPMailer/src/PHPMailer.php',
            __DIR__ . '/../includes/PHPMailer/PHPMailer.php'
        ];
        
        $phpmailer_found = false;
        foreach ($phpmailer_paths as $path) {
            if (file_exists($path)) {
                $base_dir = dirname($path);
                if (file_exists($base_dir . '/Exception.php')) {
                    require_once $base_dir . '/Exception.php';
                }
                if (file_exists($base_dir . '/PHPMailer.php')) {
                    require_once $base_dir . '/PHPMailer.php';
                }
                if (file_exists($base_dir . '/SMTP.php')) {
                    require_once $base_dir . '/SMTP.php';
                }
                $phpmailer_found = true;
                break;
            }
        }
        
        if (!$phpmailer_found) {
            return ['success' => false, 'message' => 'PHPMailer库未找到'];
        }
        
        // 根据不同版本使用不同的类名
        if (class_exists('PHPMailer\PHPMailer\PHPMailer')) {
            $mail = new PHPMailer\PHPMailer\PHPMailer(true);
            $encryption_starttls = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS;
            $encryption_smtps = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_SMTPS;
        } elseif (class_exists('PHPMailer')) {
            $mail = new PHPMailer(true);
            $encryption_starttls = 'tls';
            $encryption_smtps = 'ssl';
        } else {
            return ['success' => false, 'message' => 'PHPMailer类未找到'];
        }
        
        // SMTP配置
        $mail->isSMTP();
        $mail->Host = $settings['smtp_host'];
        $mail->SMTPAuth = true;
        $mail->Username = $settings['smtp_username'];
        $mail->Password = $settings['smtp_password'];
        
        $port = intval($settings['smtp_port'] ?? 465);
        if ($port == 465) {
            $mail->SMTPSecure = $encryption_smtps;
        } elseif ($port == 587) {
            $mail->SMTPSecure = $encryption_starttls;
        } elseif ($port == 25) {
            $mail->SMTPSecure = false;
            $mail->SMTPAuth = true;
        } else {
            $mail->SMTPSecure = $encryption_starttls;
        }
        
        $mail->Port = $port;
        $mail->CharSet = 'UTF-8';
        $mail->Timeout = 30;
        $mail->SMTPDebug = 0;
        
        $mail->setFrom($settings['smtp_username'], '小梅花AI客服系统安全中心');
        $mail->addAddress($data['email']);
        
        $mail->isHTML(true);
        
        // 根据威胁等级设置不同的邮件内容
        $threat_level = $data['threat_level'] ?? 'MEDIUM';
        $threat_colors = [
            'LOW' => '#ffc107',
            'MEDIUM' => '#fd7e14', 
            'HIGH' => '#dc3545',
            'CRITICAL' => '#6f42c1'
        ];
        $threat_color = $threat_colors[$threat_level] ?? '#dc3545';
        
        $threat_names = [
            'LOW' => '低危',
            'MEDIUM' => '中危',
            'HIGH' => '高危',
            'CRITICAL' => '极危'
        ];
        $threat_name = $threat_names[$threat_level] ?? '高危';
        
        $mail->Subject = "🚨【{$threat_name}入侵警报】疑似恶意行为检测 - " . date('Y-m-d H:i');
        
        // 页面名称映射
        $page_names = [
            'login' => '登录页面',
            'dashboard' => '仪表盘',
            'keys' => '卡密管理',
            'scripts' => '脚本管理',
            'users' => '用户管理',
            'analytics' => '数据分析',
            'settings' => '系统设置'
        ];
        
        $page_display_name = $page_names[$data['page']] ?? $data['page'];
        $reason = $data['reason'] ?? '未知原因';
        
        $mail->Body = '
        <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; background: #f8f9fa;">
            <div style="background: linear-gradient(135deg, ' . $threat_color . ' 0%, #721c24 100%); padding: 30px; text-align: center; border-radius: 15px 15px 0 0;">
                <h1 style="color: white; margin: 0; font-size: 24px;">🚨 小梅花AI安全警报</h1>
                <p style="color: rgba(255,255,255,0.9); margin: 10px 0 0 0;">' . $threat_name . '入侵警报</p>
            </div>
            
            <div style="background: white; padding: 30px; border-radius: 0 0 15px 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                <div style="background: #f8d7da; border: 3px solid ' . $threat_color . '; border-radius: 12px; padding: 20px; margin-bottom: 25px; text-align: center;">
                    <h2 style="color: #721c24; margin: 0 0 10px 0; font-size: 20px;">⚠️ ' . $threat_name . '安全威胁</h2>
                    <p style="color: #721c24; margin: 0; font-size: 16px; font-weight: bold;">检测到疑似入侵行为</p>
                </div>
                
                <div style="background: #fff3cd; border: 2px solid #ffc107; border-radius: 10px; padding: 20px; margin: 20px 0;">
                    <h3 style="color: #856404; margin-bottom: 15px; font-size: 16px;">🎯 威胁详情</h3>
                    <table style="width: 100%; border-collapse: collapse;">
                        <tr>
                            <td style="padding: 8px 0; color: #666; font-weight: bold; width: 100px;">威胁等级：</td>
                            <td style="padding: 8px 0; color: ' . $threat_color . '; font-weight: bold;">' . $threat_name . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; color: #666; font-weight: bold;">威胁原因：</td>
                            <td style="padding: 8px 0; color: #dc3545; font-weight: bold;">' . $reason . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; color: #666; font-weight: bold;">发生时间：</td>
                            <td style="padding: 8px 0; color: #333;">' . $data['time'] . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; color: #666; font-weight: bold;">目标页面：</td>
                            <td style="padding: 8px 0; color: #dc3545; font-weight: bold;">' . $page_display_name . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; color: #666; font-weight: bold;">攻击IP：</td>
                            <td style="padding: 8px 0; color: #333;">' . $data['ip'] . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; color: #666; font-weight: bold;">地理位置：</td>
                            <td style="padding: 8px 0; color: #333;">' . $data['location'] . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; color: #666; font-weight: bold;">设备指纹：</td>
                            <td style="padding: 8px 0; color: #333; font-family: monospace; font-size: 12px;">' . substr($data['device_fingerprint'], 0, 16) . '...</td>
                        </tr>
                    </table>
                </div>';
        
        if ($threat_level === 'CRITICAL') {
            $mail->Body .= '
                <div style="background: #e2e3e5; border: 3px solid #6c757d; border-radius: 10px; padding: 20px; margin: 20px 0;">
                    <h3 style="color: #495057; margin-bottom: 10px;">🔒 系统自动响应</h3>
                    <p style="color: #495057; margin: 0 0 15px 0; font-weight: bold;">
                        系统已自动采取以下保护措施：
                    </p>
                    <ul style="color: #495057; margin: 0; padding-left: 20px; line-height: 1.8;">
                        <li>✅ 已阻止可疑IP继续尝试</li>
                        <li>✅ 已清理相关登录会话</li>
                        <li>✅ 已记录详细安全日志</li>
                        <li>✅ 已启动实时监控模式</li>
                    </ul>
                </div>';
        }
        
        $mail->Body .= '
                <div style="background: #f8d7da; border: 2px solid #dc3545; border-radius: 10px; padding: 20px; margin: 20px 0;">
                    <h3 style="color: #721c24; margin-bottom: 10px;">🚨 紧急安全措施</h3>
                    <p style="color: #721c24; margin: 0 0 15px 0; font-weight: bold;">
                        建议您立即采取以下安全措施：
                    </p>
                    <ol style="color: #721c24; margin: 0; padding-left: 20px; line-height: 1.8;">
                        <li><strong>立即修改管理员密码</strong></li>
                        <li><strong>检查并清理所有信任设备</strong></li>
                        <li><strong>启用更严格的邮箱验证</strong></li>
                        <li><strong>检查系统日志和异常活动</strong></li>
                        <li><strong>考虑暂时限制管理后台访问</strong></li>
                    </ol>
                </div>
                
                <div style="background: #d1ecf1; border: 2px solid #17a2b8; border-radius: 10px; padding: 20px; margin: 20px 0;">
                    <h3 style="color: #0c5460; margin-bottom: 10px;">🛡️ 系统保护状态</h3>
                    <p style="color: #0c5460; margin: 0;">
                        ✅ 威胁已被成功阻止<br>
                        ✅ 系统完整性保持正常<br>
                        ✅ 实时监控系统正常运行<br>
                        ✅ 所有安全日志已完整记录
                    </p>
                </div>
                
                <div style="text-align: center; margin: 25px 0;">
                    <a href="https://xiaomeihuakefu.cn/xuxuemei/?page=settings" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; padding: 12px 25px; text-decoration: none; border-radius: 8px; font-weight: bold; margin-right: 10px;">紧急安全设置</a>
                    <a href="https://xiaomeihuakefu.cn/xuxuemei/" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 12px 25px; text-decoration: none; border-radius: 8px; font-weight: bold;">访问管理后台</a>
                </div>
                
                <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; text-align: center;">
                    <p style="color: #999; font-size: 12px; margin: 0;">
                        © 2024 小梅花AI客服系统安全中心 | 此为紧急安全警报，请立即处理<br>
                        如需紧急技术支持请联系管理员
                    </p>
                </div>
            </div>
        </div>';
        
        $mail->send();
        
        return [
            'success' => true,
            'message' => '入侵者警告邮件发送成功',
            'email' => $data['email'],
            'threat_level' => $threat_level,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
    } catch (Exception $e) {
        error_log('入侵者警告邮件发送失败: ' . $e->getMessage());
        return [
            'success' => false,
            'message' => '发送失败: ' . $e->getMessage()
        ];
    }
}

// 主处理逻辑
$response = ['success' => false, 'message' => '未知错误'];

try {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input || !isset($input['type'])) {
            $response = ['success' => false, 'message' => '无效的请求数据'];
        } else {
            switch ($input['type']) {
                case 'login_notification':
                    $response = sendEnhancedLoginNotification($input['data']);
                    break;
                    
                case 'untrusted_access_alert':
                    $response = sendUntrustedAccessAlert($input['data']);
                    break;
                    
                case 'intruder_alert':
                    $response = sendIntruderAlert($input['data']);
                    break;
                    
                default:
                    $response = ['success' => false, 'message' => '未知的邮件类型'];
            }
        }
    } else {
        $response = ['success' => false, 'message' => '不支持的请求方法'];
    }
    
} catch (Exception $e) {
    $response = [
        'success' => false,
        'message' => '处理异常: ' . $e->getMessage()
    ];
    error_log('安全邮件发送脚本异常: ' . $e->getMessage());
}

// 输出响应
header('Content-Type: application/json');
echo json_encode($response);
?> 