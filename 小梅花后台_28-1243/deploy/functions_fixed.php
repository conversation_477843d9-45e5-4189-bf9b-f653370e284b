<?php
// 修复版的sendVerificationEmail函数 - 使用测试邮件的成功逻辑

function sendVerificationEmail($email, $code) {
    $settings = getSystemSettings();
    
    error_log("=== 登录验证码邮件发送 ===");
    error_log("目标邮箱: $email");
    error_log("验证码: $code");
    
    if (empty($settings['smtp_host']) || empty($settings['smtp_username']) || empty($settings['smtp_password'])) {
        error_log('❌ SMTP配置不完整，无法发送邮件');
        return false;
    }
    
    // 使用与测试邮件相同的成功方法发送验证码邮件
    return sendVerificationCodeForLogin($email, $code, $settings['smtp_host'], $settings['smtp_port'], $settings['smtp_username'], $settings['smtp_password']);
}

// 专门用于登录验证码的邮件发送函数
function sendVerificationCodeForLogin($to_email, $verification_code, $smtp_host, $smtp_port, $smtp_username, $smtp_password) {
    error_log("使用测试邮件方法发送登录验证码 - 目标邮箱: $to_email, 验证码: $verification_code");
    
    // 智能检测PHPMailer路径
    $phpmailer_paths = [
        __DIR__ . '/PHPMailer/src/PHPMailer.php',
        __DIR__ . '/PHPMailer/PHPMailer.php'
    ];
    
    $phpmailer_found = false;
    foreach ($phpmailer_paths as $path) {
        if (file_exists($path)) {
            $base_dir = dirname($path);
            if (file_exists($base_dir . '/Exception.php')) {
                require_once $base_dir . '/Exception.php';
            }
            if (file_exists($base_dir . '/PHPMailer.php')) {
                require_once $base_dir . '/PHPMailer.php';
            }
            if (file_exists($base_dir . '/SMTP.php')) {
                require_once $base_dir . '/SMTP.php';
            }
            $phpmailer_found = true;
            error_log("PHPMailer库加载成功: $path");
            break;
        }
    }
    
    if (!$phpmailer_found) {
        error_log('PHPMailer库未找到');
        return false;
    }
    
    // 根据不同版本使用不同的类名
    if (class_exists('PHPMailer\PHPMailer\PHPMailer')) {
        $mail = new PHPMailer\PHPMailer\PHPMailer(true);
        $encryption_starttls = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS;
        $encryption_smtps = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_SMTPS;
        error_log("使用命名空间版本的PHPMailer");
    } elseif (class_exists('PHPMailer')) {
        $mail = new PHPMailer(true);
        $encryption_starttls = 'tls';
        $encryption_smtps = 'ssl';
        error_log("使用传统版本的PHPMailer");
    } else {
        error_log('PHPMailer类未找到');
        return false;
    }
    
    try {
        // 服务器设置
        $mail->isSMTP();
        $mail->Host = $smtp_host;
        $mail->SMTPAuth = true;
        $mail->Username = $smtp_username;
        $mail->Password = $smtp_password;
        
        // 根据端口选择加密方式
        $port = intval($smtp_port);
        
        error_log("配置SMTP - 主机: $smtp_host, 端口: $port");
        
        if ($port == 465) {
            $mail->SMTPSecure = $encryption_smtps;
            error_log("使用SSL加密 (端口465)");
        } elseif ($port == 587) {
            $mail->SMTPSecure = $encryption_starttls;
            error_log("使用STARTTLS加密 (端口587)");
        } elseif ($port == 25) {
            $mail->SMTPSecure = false;
            $mail->SMTPAuth = true;
            error_log("使用无加密连接 (端口25)");
        } else {
            $mail->SMTPSecure = $encryption_starttls;
            error_log("使用STARTTLS加密 (其他端口)");
        }
        
        $mail->Port = $port;
        $mail->CharSet = 'UTF-8';
        $mail->Timeout = 30;
        $mail->SMTPDebug = 0;
        
        // 发件人和收件人
        $mail->setFrom($smtp_username, '小梅花AI客服系统');
        $mail->addAddress($to_email);
        
        // 邮件内容
        $mail->isHTML(true);
        $mail->Subject = '【小梅花AI客服系统】登录验证码';
        
        $mail->Body = '
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 20px; overflow: hidden;">
            <div style="background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(20px); padding: 40px; text-align: center;">
                <div style="background: linear-gradient(135deg, #ff6b9d, #c44569); width: 80px; height: 80px; border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center; font-size: 32px; color: white;">
                    🔐
                </div>
                <h1 style="color: white; font-size: 24px; margin-bottom: 20px;">登录验证码</h1>
                <p style="color: rgba(255, 255, 255, 0.9); font-size: 16px; line-height: 1.6; margin-bottom: 30px;">
                    您正在登录小梅花AI客服系统管理后台
                </p>
                <div style="background: rgba(255, 255, 255, 0.1); border-radius: 12px; padding: 20px; margin: 20px 0;">
                    <p style="color: rgba(255, 255, 255, 0.8); margin: 0 0 10px 0; font-size: 14px;">您的验证码是：</p>
                    <div style="font-size: 36px; font-weight: bold; color: white; letter-spacing: 8px; font-family: monospace;">' . htmlspecialchars($verification_code) . '</div>
                    <p style="color: rgba(255, 255, 255, 0.8); margin: 10px 0 0 0; font-size: 14px;">验证码有效期：20秒</p>
                </div>
                <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid rgba(255, 255, 255, 0.2);">
                    <p style="color: rgba(255, 255, 255, 0.7); font-size: 14px;">
                        如果这不是您的操作，请忽略此邮件。<br>
                        此邮件由小梅花AI客服系统自动发送，请勿回复。<br>
                        发送时间: ' . date('Y-m-d H:i:s') . '
                    </p>
                </div>
            </div>
        </div>';
        
        $mail->AltBody = "【小梅花AI客服系统】登录验证码：" . $verification_code . "（有效期20秒）\n\n发送时间：" . date('Y-m-d H:i:s');
        
        error_log("开始发送邮件到: $to_email");
        $mail->send();
        error_log("登录验证码邮件发送成功: $to_email");
        return true;
    } catch (Exception $e) {
        $error_msg = $e->getMessage();
        error_log("登录验证码邮件发送失败 - 错误信息: " . $error_msg);
        error_log("登录验证码邮件发送失败 - PHPMailer错误: " . $mail->ErrorInfo);
        
        // 分析常见错误并提供解决建议
        if (strpos($error_msg, 'SMTP connect() failed') !== false) {
            error_log("SMTP连接失败 - 可能是主机或端口配置错误");
        } elseif (strpos($error_msg, 'SMTP AUTH') !== false || strpos($error_msg, 'Authentication') !== false) {
            error_log("SMTP认证失败 - 可能是用户名或密码错误");
        } elseif (strpos($error_msg, 'tls') !== false || strpos($error_msg, 'ssl') !== false) {
            error_log("SSL/TLS连接失败 - 可能是加密方式配置错误");
        }
        
        return false;
    }
}
?> 