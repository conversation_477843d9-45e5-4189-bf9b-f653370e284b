<?php
// 禁用错误报告，防止暴露敏感信息
error_reporting(0);
ini_set('display_errors', 0);

session_start();
require_once '../includes/db.php';
require_once '../includes/functions.php';

// 安全检查：验证用户是否已登录
if (!isset($_SESSION['admin_user_id'])) {
    die(json_encode(['success' => false, 'message' => '未授权访问']));
}

// 获取POST数据
$postData = file_get_contents('php://input');
$request = json_decode($postData, true);

// 如果没有action，返回错误
if (!isset($request['action'])) {
    die(json_encode(['success' => false, 'message' => '缺少操作参数']));
}

// 处理不同的操作
switch ($request['action']) {
    // 获取数据库配置
    case 'get_db_config':
        require_once 'env_manager.php';
        
        try {
            $env_manager = EnvManager::getInstance();
            $env_manager->load();
            
            $db_config = $env_manager->getDatabaseConfig();
            
            // 为了安全，不返回实际密码
            if (!empty($db_config['pass'])) {
                $db_config['pass_set'] = true;
                $db_config['pass'] = ''; // 不发送实际密码到前端
            } else {
                $db_config['pass_set'] = false;
            }
            
            echo json_encode([
                'success' => true,
                'config' => $db_config
            ]);
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => '获取配置失败: ' . $e->getMessage()]);
        }
        break;
        
    // 测试数据库连接
    case 'test_db_connection':
        if (!isset($request['config']) || empty($request['config'])) {
            die(json_encode(['success' => false, 'message' => '配置数据为空']));
        }
        
        try {
            $config = $request['config'];
            
            // 如果密码字段是占位符，则从环境变量获取实际密码
            if ($config['pass'] === '********') {
                require_once 'env_manager.php';
                $env_manager = EnvManager::getInstance();
                $env_manager->load();
                $db_config = $env_manager->getDatabaseConfig();
                $config['pass'] = $db_config['pass'];
            }
            
            // 尝试连接数据库
            $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['dbname']}";
            $test_pdo = new PDO($dsn, $config['user'], $config['pass'], [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_TIMEOUT => 5
            ]);
            
            // 获取服务器信息
            $server_info = $test_pdo->getAttribute(PDO::ATTR_SERVER_INFO);
            $version = $test_pdo->getAttribute(PDO::ATTR_SERVER_VERSION);
            
            // 测试连接成功
            echo json_encode([
                'success' => true, 
                'message' => '数据库连接成功', 
                'server_info' => $server_info,
                'version' => $version
            ]);
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'message' => '数据库连接失败: ' . $e->getMessage()]);
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => '操作失败: ' . $e->getMessage()]);
        }
        break;
        
    // 保存数据库配置
    case 'save_db_config':
        if (!isset($request['config']) || empty($request['config'])) {
            die(json_encode(['success' => false, 'message' => '配置数据为空']));
        }
        
        try {
            require_once 'env_manager.php';
            $env_manager = EnvManager::getInstance();
            $env_manager->load();
            
            $config = $request['config'];
            
            // 如果密码是占位符，使用现有密码
            if ($config['pass'] === '********') {
                $db_config = $env_manager->getDatabaseConfig();
                $config['pass'] = $db_config['pass'];
            }
            
            // 先测试连接是否成功
            $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['dbname']}";
            $test_connection = false;
            
            try {
                $test_pdo = new PDO($dsn, $config['user'], $config['pass'], [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_TIMEOUT => 5
                ]);
                $test_connection = true;
            } catch (PDOException $e) {
                // 如果连接测试失败，返回错误
                return json_encode([
                    'success' => false, 
                    'message' => '保存前连接测试失败: ' . $e->getMessage()
                ]);
            }
            
            // 连接测试成功，保存配置
            if ($test_connection) {
                $save_result = $env_manager->saveDatabaseConfig([
                    'host' => $config['host'],
                    'port' => $config['port'],
                    'dbname' => $config['dbname'],
                    'user' => $config['user'],
                    'pass' => $config['pass']
                ]);
                
                if ($save_result) {
                    // 记录操作日志
                    logAdminAction($_SESSION['admin_user_id'], 'update_db_config', '更新数据库连接配置');
                    
                    echo json_encode(['success' => true, 'message' => '数据库配置已保存到环境变量']);
                } else {
                    echo json_encode(['success' => false, 'message' => '保存环境变量失败']);
                }
            }
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => '操作失败: ' . $e->getMessage()]);
        }
        break;
        
    // 其他操作
    // ... 其他现有的操作处理 ...
    
    default:
        echo json_encode(['success' => false, 'message' => '未知的操作']);
        break;
}

// 记录管理员操作的函数
function logAdminAction($adminId, $action, $description) {
    global $pdo;
    
    try {
        // 检查表是否存在
        $stmt = $pdo->query("SHOW TABLES LIKE 'admin_action_log'");
        if ($stmt->rowCount() == 0) {
            $pdo->exec("CREATE TABLE admin_action_log (
                id INT AUTO_INCREMENT PRIMARY KEY,
                admin_id INT NOT NULL,
                action VARCHAR(64) NOT NULL,
                description VARCHAR(255),
                ip_address VARCHAR(45),
                user_agent VARCHAR(255),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )");
        }
        
        $stmt = $pdo->prepare("INSERT INTO admin_action_log (admin_id, action, description, ip_address, user_agent) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([
            $adminId,
            $action,
            $description,
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
    } catch (Exception $e) {
        // 静默失败，不影响主要操作
        error_log('日志记录失败: ' . $e->getMessage());
    }
}