<?php
/**
 * 片段脚本管理API
 * 处理脚本状态切换、片段管理、快速加载等功能
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once '../includes/db.php';

try {
    $action = $_POST['action'] ?? $_GET['action'] ?? '';
    
    switch ($action) {
        case 'toggle_script_status':
            toggleScriptStatus();
            break;
            
        case 'toggle_snippet_status':
            toggleSnippetStatus();
            break;
            
        case 'get_license_script':
            getLicenseScript();
            break;
            
        case 'get_enabled_snippets':
            getEnabledSnippets();
            break;
            
        case 'update_snippet_order':
            updateSnippetOrder();
            break;
            
        default:
            throw new Exception('无效的操作');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
} catch (Error $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => '服务器内部错误: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 切换脚本状态
 */
function toggleScriptStatus() {
    global $pdo;
    
    $script_id = (int)($_POST['script_id'] ?? 0);
    $new_status = $_POST['new_status'] ?? '';
    
    if (!$script_id || !in_array($new_status, ['active', 'inactive'])) {
        throw new Exception('参数错误');
    }
    
    $stmt = $pdo->prepare("UPDATE scripts SET status = ? WHERE id = ?");
    if ($stmt->execute([$new_status, $script_id])) {
        echo json_encode([
            'success' => true,
            'message' => '脚本状态已更新',
            'new_status' => $new_status
        ], JSON_UNESCAPED_UNICODE);
    } else {
        throw new Exception('状态更新失败');
    }
}

/**
 * 切换片段状态
 */
function toggleSnippetStatus() {
    global $pdo;
    
    $snippet_id = (int)($_POST['snippet_id'] ?? 0);
    $enabled = (int)($_POST['enabled'] ?? 0);
    
    if (!$snippet_id) {
        throw new Exception('参数错误');
    }
    
    $stmt = $pdo->prepare("UPDATE snippet_scripts SET enabled = ? WHERE id = ?");
    if ($stmt->execute([$enabled, $snippet_id])) {
        echo json_encode([
            'success' => true,
            'message' => '片段状态已更新',
            'enabled' => $enabled
        ], JSON_UNESCAPED_UNICODE);
    } else {
        throw new Exception('状态更新失败');
    }
}

/**
 * 获取卡密对应的完整脚本（包含启用的片段）
 */
function getLicenseScript() {
    global $pdo;
    
    $key = $_GET['key'] ?? '';
    if (empty($key)) {
        throw new Exception('卡密不能为空');
    }
    
    // 验证卡密并获取脚本ID
    $stmt = $pdo->prepare("
        SELECT lk.script_id, s.script_code, s.name, s.version, s.status
        FROM license_keys lk 
        JOIN scripts s ON lk.script_id = s.id 
        WHERE lk.license_key = ? AND lk.status = 'active' AND s.status = 'active'
    ");
    $stmt->execute([$key]);
    $license_data = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$license_data) {
        throw new Exception('卡密无效或已过期');
    }
    
    // 获取启用的片段脚本
    $snippets_stmt = $pdo->prepare("
        SELECT code, title, category, load_priority 
        FROM snippet_scripts 
        WHERE enabled = 1 
        ORDER BY load_priority DESC, sort_order ASC
    ");
    $snippets_stmt->execute();
    $snippets = $snippets_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 构建完整脚本
    $main_script = $license_data['script_code'];
    $merged_script = mergeScriptWithSnippets($main_script, $snippets);
    
    // 返回脚本内容
    header('Content-Type: text/javascript; charset=utf-8');
    echo $merged_script;
    exit;
}

/**
 * 获取启用的片段列表
 */
function getEnabledSnippets() {
    global $pdo;
    
    $stmt = $pdo->prepare("
        SELECT id, title, description, category, sort_order, load_priority
        FROM snippet_scripts 
        WHERE enabled = 1 
        ORDER BY load_priority DESC, sort_order ASC
    ");
    $stmt->execute();
    $snippets = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'snippets' => $snippets,
        'count' => count($snippets)
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 更新片段排序
 */
function updateSnippetOrder() {
    global $pdo;
    
    $snippet_id = (int)($_POST['snippet_id'] ?? 0);
    $new_order = (int)($_POST['new_order'] ?? 0);
    
    if (!$snippet_id) {
        throw new Exception('参数错误');
    }
    
    $stmt = $pdo->prepare("UPDATE snippet_scripts SET sort_order = ? WHERE id = ?");
    if ($stmt->execute([$new_order, $snippet_id])) {
        echo json_encode([
            'success' => true,
            'message' => '排序已更新'
        ], JSON_UNESCAPED_UNICODE);
    } else {
        throw new Exception('排序更新失败');
    }
}

/**
 * 合并主脚本和片段脚本
 */
function mergeScriptWithSnippets($mainScript, $snippets) {
    if (empty($snippets)) {
        return $mainScript;
    }
    
    // 添加片段加载器
    $loaderCode = generateSnippetLoader($snippets);
    
    // 智能插入位置检测
    $insertPosition = findOptimalInsertPosition($mainScript);
    
    if ($insertPosition !== false) {
        // 在最佳位置插入
        $beforeCode = substr($mainScript, 0, $insertPosition);
        $afterCode = substr($mainScript, $insertPosition);
        
        return $beforeCode . "\n\n" . $loaderCode . "\n\n" . $afterCode;
    } else {
        // 默认在末尾添加
        return $mainScript . "\n\n" . $loaderCode;
    }
}

/**
 * 生成片段加载器代码
 */
function generateSnippetLoader($snippets) {
    $loaderCode = "// === 动态片段加载器 ===\n";
    $loaderCode .= "(function() {\n";
    $loaderCode .= "    'use strict';\n\n";
    $loaderCode .= "    // 片段管理器\n";
    $loaderCode .= "    const SnippetManager = {\n";
    $loaderCode .= "        loadedSnippets: new Set(),\n";
    $loaderCode .= "        \n";
    $loaderCode .= "        // 加载片段\n";
    $loaderCode .= "        loadSnippet: function(id, code, title) {\n";
    $loaderCode .= "            if (this.loadedSnippets.has(id)) return;\n";
    $loaderCode .= "            \n";
    $loaderCode .= "            try {\n";
    $loaderCode .= "                console.log('[片段加载]', title);\n";
    $loaderCode .= "                // 在安全环境中执行片段代码\n";
    $loaderCode .= "                const snippetFunction = new Function('SnippetManager', code);\n";
    $loaderCode .= "                snippetFunction(this);\n";
    $loaderCode .= "                this.loadedSnippets.add(id);\n";
    $loaderCode .= "            } catch (error) {\n";
    $loaderCode .= "                console.error('[片段加载失败]', title, error);\n";
    $loaderCode .= "            }\n";
    $loaderCode .= "        },\n";
    $loaderCode .= "        \n";
    $loaderCode .= "        // 批量加载片段\n";
    $loaderCode .= "        loadAll: function() {\n";
    
    foreach ($snippets as $index => $snippet) {
        $snippetId = $snippet['id'] ?? $index;
        $snippetTitle = addslashes($snippet['title'] ?? '');
        $snippetCode = addslashes($snippet['code'] ?? '');
        
        $loaderCode .= "            // 片段: {$snippetTitle}\n";
        $loaderCode .= "            this.loadSnippet({$snippetId}, `{$snippetCode}`, '{$snippetTitle}');\n\n";
    }
    
    $loaderCode .= "        }\n";
    $loaderCode .= "    };\n\n";
    $loaderCode .= "    // 自动加载所有启用的片段\n";
    $loaderCode .= "    if (document.readyState === 'loading') {\n";
    $loaderCode .= "        document.addEventListener('DOMContentLoaded', () => SnippetManager.loadAll());\n";
    $loaderCode .= "    } else {\n";
    $loaderCode .= "        SnippetManager.loadAll();\n";
    $loaderCode .= "    }\n\n";
    $loaderCode .= "    // 导出到全局（可选）\n";
    $loaderCode .= "    if (typeof window !== 'undefined') {\n";
    $loaderCode .= "        window.SnippetManager = SnippetManager;\n";
    $loaderCode .= "    }\n";
    $loaderCode .= "})();\n";
    $loaderCode .= "// === 片段加载器结束 ===";
    
    return $loaderCode;
}

/**
 * 查找最佳插入位置
 */
function findOptimalInsertPosition($script) {
    // 查找IIFE函数内部的合适位置
    if (preg_match('/\(function\(\)\s*\{\s*[\'"]use strict[\'"];\s*/i', $script, $matches, PREG_OFFSET_CAPTURE)) {
        return $matches[0][1] + strlen($matches[0][0]);
    }
    
    // 查找第一个函数定义之前
    if (preg_match('/function\s+\w+\s*\(/i', $script, $matches, PREG_OFFSET_CAPTURE)) {
        return $matches[0][1];
    }
    
    // 查找变量声明之后
    if (preg_match('/(?:var|let|const)\s+\w+\s*=.*?;/i', $script, $matches, PREG_OFFSET_CAPTURE)) {
        return $matches[0][1] + strlen($matches[0][0]);
    }
    
    return false;
}

/**
 * 清理和优化脚本代码
 */
function optimizeScript($script) {
    // 移除多余的空行
    $script = preg_replace('/\n\s*\n\s*\n+/', "\n\n", $script);
    
    // 移除行末空格
    $script = preg_replace('/[ \t]+$/m', '', $script);
    
    // 确保文件末尾只有一个换行
    $script = rtrim($script) . "\n";
    
    return $script;
}
?> 