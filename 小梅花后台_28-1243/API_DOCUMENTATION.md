# APP在线升级API文档

## 概述

这是一个完整的APP在线升级系统API，支持版本管理、文件上传、升级检查和下载功能。

## 基础信息

- **API地址**: `app_update_standalone.php`
- **支持格式**: JSON
- **字符编码**: UTF-8
- **支持平台**: Windows (.exe), macOS (.dmg)

## API接口列表

### 1. 检查更新

**请求方式**: GET

**请求URL**: `app_update_standalone.php?action=check&version={当前版本}&platform={平台}`

**参数说明**:
- `version` (可选): 当前APP版本号，默认为 "1.0.0"
- `platform` (可选): 平台类型，可选值: all, windows, macos，默认为 "all"

**响应示例**:
```json
{
    "success": true,
    "message": "发现新版本",
    "data": {
        "has_update": true,
        "current_version": "1.0.0",
        "latest_version": "1.0.1",
        "update_info": {
            "id": 1,
            "title": "小梅花AI助手 v1.0.1 更新",
            "description": "修复了已知问题，优化了用户体验",
            "force_update": true,
            "download_urls": {
                "windows": "https://example.com/uploads/upgrade_packages/app_v1.0.1_exe.exe",
                "macos": "https://example.com/uploads/upgrade_packages/app_v1.0.1_dmg.dmg"
            },
            "release_notes": "详细的发布说明"
        }
    }
}
```

### 2. 获取版本列表

**请求方式**: GET

**请求URL**: `app_update_standalone.php?action=list`

**响应示例**:
```json
{
    "success": true,
    "message": "获取版本列表成功",
    "data": [
        {
            "id": 1,
            "version": "1.0.1",
            "title": "小梅花AI助手 v1.0.1 更新",
            "description": "修复了已知问题",
            "status": "published",
            "force_update": true,
            "download_count": 0,
            "created_at": "2024-01-01 12:00:00"
        }
    ]
}
```

### 3. 创建新版本

**请求方式**: POST

**请求URL**: `app_update_standalone.php`

**Content-Type**: `multipart/form-data`

**参数说明**:
- `action`: "create"
- `version` (可选): 版本号，留空自动生成
- `title`: 版本标题
- `description`: 更新说明
- `exe_file` (可选): Windows安装包文件
- `dmg_file` (可选): macOS安装包文件
- `exe_download_url` (可选): Windows下载链接（如果不上传文件）
- `dmg_download_url` (可选): macOS下载链接（如果不上传文件）
- `force_update` (可选): 是否强制更新，默认为1

**响应示例**:
```json
{
    "success": true,
    "message": "版本创建成功",
    "data": {
        "id": 1,
        "version": "1.0.1",
        "title": "小梅花AI助手 v1.0.1 更新"
    }
}
```

### 4. 发布版本

**请求方式**: POST

**请求URL**: `app_update_standalone.php`

**参数说明**:
- `action`: "publish"
- `id`: 版本ID

### 5. 撤回版本

**请求方式**: POST

**请求URL**: `app_update_standalone.php`

**参数说明**:
- `action`: "unpublish"
- `id`: 版本ID

### 6. 删除版本

**请求方式**: POST

**请求URL**: `app_update_standalone.php`

**参数说明**:
- `action`: "delete"
- `id`: 版本ID

### 7. 下载文件

**请求方式**: GET

**请求URL**: `app_update_standalone.php?action=download&id={版本ID}&platform={平台}`

**参数说明**:
- `id`: 版本ID
- `platform`: 平台类型，可选值: windows, macos

## 错误码说明

- `200`: 成功
- `400`: 请求参数错误
- `404`: 资源不存在
- `405`: 请求方法不支持
- `500`: 服务器内部错误

## 文件上传说明

### 支持的文件类型
- Windows: `.exe` 文件
- macOS: `.dmg` 文件

### 文件存储
- 上传的文件存储在 `uploads/upgrade_packages/` 目录
- 文件命名格式: `app_v{版本号}_{平台}.{扩展名}`
- 例如: `app_v1.0.1_exe.exe`, `app_v1.0.1_dmg.dmg`

### PHP配置要求
- `file_uploads = On`
- `upload_max_filesize >= 100M`
- `post_max_size >= 100M`
- `max_execution_time >= 300`
- `memory_limit >= 128M`

## 数据库结构

### app_updates 表
```sql
CREATE TABLE `app_updates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `version` varchar(50) NOT NULL COMMENT '版本号',
  `title` varchar(255) NOT NULL COMMENT '版本标题',
  `description` longtext NOT NULL COMMENT '更新说明',
  `exe_download_url` varchar(500) DEFAULT NULL COMMENT 'Windows下载链接',
  `dmg_download_url` varchar(500) DEFAULT NULL COMMENT 'macOS下载链接',
  `force_update` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否强制更新',
  `status` enum('draft','published') NOT NULL DEFAULT 'draft' COMMENT '状态',
  `download_count` int(11) NOT NULL DEFAULT 0 COMMENT '下载次数',
  `platform` enum('all','windows','macos','linux') NOT NULL DEFAULT 'all',
  `min_version` varchar(50) DEFAULT NULL COMMENT '最低兼容版本',
  `release_notes` text DEFAULT NULL COMMENT '发布说明',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_version` (`version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

## 使用示例

### APP端检查更新
```javascript
async function checkForUpdates() {
    try {
        const response = await fetch('app_update_standalone.php?action=check&version=1.0.0&platform=windows');
        const result = await response.json();
        
        if (result.success && result.data.has_update) {
            // 发现新版本，显示更新提示
            showUpdateDialog(result.data.update_info);
        }
    } catch (error) {
        console.error('检查更新失败:', error);
    }
}
```

### 后台管理端创建版本
```javascript
async function createVersion(formData) {
    formData.append('action', 'create');
    
    try {
        const response = await fetch('app_update_standalone.php', {
            method: 'POST',
            body: formData
        });
        const result = await response.json();
        
        if (result.success) {
            alert('版本创建成功！');
        } else {
            alert('创建失败：' + result.message);
        }
    } catch (error) {
        console.error('创建版本失败:', error);
    }
}
```

## 安全注意事项

1. 建议在生产环境中添加身份验证
2. 限制文件上传大小和类型
3. 验证文件完整性
4. 使用HTTPS传输
5. 定期备份数据库和文件
