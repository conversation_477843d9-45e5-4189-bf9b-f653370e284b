<?php
// 检查用户是否已登录
if (!isset($_SESSION['admin_user_id'])) {
    header('Location: login.php');
    exit;
}
?>

<div class="app-upgrade-container">
    <!-- 页面标题 -->
    <div class="page-header">
        <h1><i class="fas fa-download"></i> APP在线升级</h1>
        <p>管理APP版本发布，实现自动在线升级功能</p>
    </div>

    <!-- 创建新版本表单 -->
    <div class="upgrade-form-section">
        <div class="section-card">
            <div class="card-header">
                <h3><i class="fas fa-plus-circle"></i> 发布新版本</h3>
                <p>创建新的APP版本，支持Windows和macOS平台</p>
            </div>
            
            <form id="upgrade-form" class="upgrade-form" enctype="multipart/form-data">
                <!-- 版本基本信息 -->
                <div class="form-section">
                    <h4><i class="fas fa-info-circle"></i> 版本信息</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="version-number">
                                <i class="fas fa-tag"></i> 版本号
                                <span class="optional">(可选，留空自动生成)</span>
                            </label>
                            <input type="text" id="version-number" name="version" 
                                   placeholder="例如：1.0.2" pattern="^\d+\.\d+\.\d+$" 
                                   title="版本号格式：主版本号.次版本号.修订号">
                        </div>

                        <div class="form-group">
                            <label for="version-title">
                                <i class="fas fa-heading"></i> 版本标题
                                <span class="required">*</span>
                            </label>
                            <input type="text" id="version-title" name="title" 
                                   placeholder="请输入版本标题，如：小梅花AI助手 v1.0.2 更新" required>
                        </div>
                    </div>
                </div>

                <!-- 更新说明 -->
                <div class="form-section">
                    <h4><i class="fas fa-edit"></i> 更新说明</h4>
                    <div class="form-group">
                        <label for="update-description">
                            <i class="fas fa-file-alt"></i> 详细说明
                            <span class="required">*</span>
                        </label>
                        <div id="upgrade-word-editor" class="word-editor-container"></div>
                        <input type="hidden" id="update-description" name="description" required>
                        <div class="editor-help">
                            <i class="fas fa-lightbulb"></i>
                            <span>支持富文本编辑，可添加格式、列表、链接等</span>
                        </div>
                    </div>
                </div>

                <!-- 文件上传区域 -->
                <div class="form-section">
                    <h4><i class="fas fa-upload"></i> 安装包上传</h4>
                    <div class="upload-info">
                        <i class="fas fa-info-circle"></i>
                        <span>支持大文件上传，无大小限制。文件将自动存储到升级包文件夹中。</span>
                    </div>
                    
                    <div class="form-row">
                        <!-- Windows安装包 -->
                        <div class="form-group">
                            <label for="exe-file">
                                <i class="fab fa-windows"></i> Windows安装包 (.exe)
                                <span class="optional">(可选)</span>
                            </label>
                            <div class="file-upload-area" id="exe-upload-area">
                                <input type="file" id="exe-file" name="exe_file" accept=".exe" style="display: none;">
                                <div class="upload-content">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                    <p>点击选择或拖拽Windows安装包文件</p>
                                    <small>支持.exe格式，无大小限制</small>
                                </div>
                            </div>
                            <div id="exe-preview" class="file-preview" style="display: none;"></div>
                        </div>

                        <!-- macOS安装包 -->
                        <div class="form-group">
                            <label for="dmg-file">
                                <i class="fab fa-apple"></i> macOS安装包 (.dmg)
                                <span class="optional">(可选)</span>
                            </label>
                            <div class="file-upload-area" id="dmg-upload-area">
                                <input type="file" id="dmg-file" name="dmg_file" accept=".dmg" style="display: none;">
                                <div class="upload-content">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                    <p>点击选择或拖拽macOS安装包文件</p>
                                    <small>支持.dmg格式，无大小限制</small>
                                </div>
                            </div>
                            <div id="dmg-preview" class="file-preview" style="display: none;"></div>
                        </div>
                    </div>
                </div>

                <!-- 发布设置 -->
                <div class="form-section">
                    <h4><i class="fas fa-cog"></i> 发布设置</h4>
                    <div class="setting-group">
                        <div class="setting-item">
                            <label class="checkbox-label">
                                <input type="checkbox" id="force-update" name="force_update" checked>
                                <span class="checkmark"></span>
                                <span class="checkbox-text">
                                    强制更新
                                    <small>启用后，用户必须更新才能继续使用APP</small>
                                </span>
                            </label>
                        </div>
                        
                        <div class="setting-item">
                            <label class="checkbox-label">
                                <input type="checkbox" id="auto-publish" name="auto_publish" checked>
                                <span class="checkmark"></span>
                                <span class="checkbox-text">
                                    立即发布
                                    <small>创建后立即发布，用户可以收到更新通知</small>
                                </span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- 表单操作按钮 -->
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="resetUpgradeForm()">
                        <i class="fas fa-undo"></i> 重置表单
                    </button>
                    <button type="button" class="btn btn-info" onclick="previewUpgrade()">
                        <i class="fas fa-eye"></i> 预览效果
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-rocket"></i> 发布版本
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 版本管理列表 -->
    <div class="version-management-section">
        <div class="section-card">
            <div class="card-header">
                <div class="header-content">
                    <div class="header-text">
                        <h3><i class="fas fa-list"></i> 版本管理</h3>
                        <p>管理已发布的APP版本</p>
                    </div>
                    <div class="header-actions">
                        <button class="btn btn-info btn-sm" onclick="previewLatestUpgrade()" id="preview-latest-btn">
                            <i class="fas fa-mobile-alt"></i> 预览最新版本
                        </button>
                        <button class="btn btn-warning btn-sm" onclick="refreshVersionList()">
                            <i class="fas fa-sync-alt"></i> 刷新列表
                        </button>
                        <button class="btn btn-danger btn-sm" onclick="deleteAllVersions()" id="delete-all-btn" style="display: none;">
                            <i class="fas fa-trash-alt"></i> 清空所有版本
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="version-list" id="version-list">
                <!-- 版本列表将通过JavaScript动态加载 -->
                <div class="loading-placeholder">
                    <i class="fas fa-spinner fa-spin"></i>
                    <span>正在加载版本列表...</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 引入Word风格编辑器 -->
<link rel="stylesheet" href="../../assets/word-style-editor.css?v=<?php echo time(); ?>">
<script src="../../assets/word-style-editor.js?v=<?php echo time(); ?>"></script>

<!-- APP升级页面样式 -->
<style>
.app-upgrade-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 12px;
    margin-bottom: 30px;
    text-align: center;
}

.page-header h1 {
    margin: 0 0 10px 0;
    font-size: 2.5em;
    font-weight: 600;
}

.page-header p {
    margin: 0;
    font-size: 1.1em;
    opacity: 0.9;
}

.section-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    margin-bottom: 30px;
    overflow: hidden;
}

.card-header {
    background: #f8f9fa;
    padding: 25px 30px;
    border-bottom: 1px solid #e9ecef;
}

.card-header h3 {
    margin: 0 0 8px 0;
    color: #2c3e50;
    font-size: 1.4em;
    font-weight: 600;
}

.card-header p {
    margin: 0;
    color: #6c757d;
    font-size: 0.95em;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-actions {
    display: flex;
    gap: 10px;
}

.upgrade-form {
    padding: 30px;
}

.form-section {
    margin-bottom: 35px;
    padding-bottom: 25px;
    border-bottom: 1px solid #e9ecef;
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.form-section h4 {
    color: #495057;
    font-size: 1.2em;
    font-weight: 600;
    margin: 0 0 20px 0;
    padding-bottom: 10px;
    border-bottom: 2px solid #007bff;
    display: inline-block;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 25px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #495057;
    font-size: 0.95em;
}

.form-group input[type="text"] {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 0.95em;
    transition: all 0.3s ease;
}

.form-group input[type="text"]:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
}

.required {
    color: #dc3545;
    font-weight: bold;
}

.optional {
    color: #6c757d;
    font-weight: normal;
    font-size: 0.85em;
}

.word-editor-container {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    min-height: 200px;
    background: white;
}

.editor-help {
    margin-top: 8px;
    color: #6c757d;
    font-size: 0.85em;
}

.upload-info {
    background: #e3f2fd;
    padding: 12px 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    color: #1976d2;
    font-size: 0.9em;
}

.file-upload-area {
    border: 2px dashed #007bff;
    border-radius: 8px;
    padding: 30px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9ff;
}

.file-upload-area:hover {
    border-color: #0056b3;
    background: #e6f3ff;
}

.file-upload-area.dragover {
    border-color: #28a745;
    background: #e8f5e8;
}

.upload-content i {
    font-size: 2.5em;
    color: #007bff;
    margin-bottom: 15px;
    display: block;
}

.upload-content p {
    margin: 0 0 5px 0;
    font-weight: 600;
    color: #495057;
}

.upload-content small {
    color: #6c757d;
}

.file-preview {
    margin-top: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.setting-group {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.setting-item {
    display: flex;
    align-items: flex-start;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    cursor: pointer;
    font-size: 0.95em;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #007bff;
    border-radius: 4px;
    margin-right: 12px;
    position: relative;
    flex-shrink: 0;
    margin-top: 2px;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: #007bff;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: 12px;
}

.checkbox-text {
    display: flex;
    flex-direction: column;
}

.checkbox-text small {
    color: #6c757d;
    margin-top: 3px;
    font-size: 0.85em;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    padding-top: 25px;
    border-top: 1px solid #e9ecef;
    margin-top: 25px;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 0.95em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-info:hover {
    background: #117a8b;
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background: #e0a800;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

.btn-sm {
    padding: 8px 16px;
    font-size: 0.85em;
}

.version-list {
    padding: 20px 30px;
}

.loading-placeholder {
    text-align: center;
    padding: 40px;
    color: #6c757d;
}

.loading-placeholder i {
    font-size: 2em;
    margin-bottom: 15px;
    display: block;
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }

    .header-content {
        flex-direction: column;
        gap: 15px;
    }

    .header-actions {
        flex-wrap: wrap;
        justify-content: center;
    }

    .form-actions {
        flex-direction: column;
    }
}
</style>

<!-- APP升级页面JavaScript -->
<script>
// 全局变量
let upgradeWordEditor = null;
let currentVersions = [];

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeUpgradePage();
});

// 初始化页面
function initializeUpgradePage() {
    // 初始化Word编辑器
    initializeWordEditor();

    // 初始化文件上传
    initializeFileUpload();

    // 初始化表单提交
    initializeFormSubmit();

    // 加载版本列表
    loadVersionList();

    console.log('APP升级页面初始化完成');
}

// 初始化Word编辑器
function initializeWordEditor() {
    try {
        if (typeof WordStyleEditor !== 'undefined') {
            upgradeWordEditor = new WordStyleEditor('upgrade-word-editor', {
                placeholder: '请输入版本更新说明...\n\n例如：\n• 修复了已知问题\n• 优化了用户体验\n• 新增了XXX功能',
                height: 200,
                toolbar: [
                    'bold', 'italic', 'underline', '|',
                    'fontSize', 'fontColor', 'backgroundColor', '|',
                    'bulletList', 'numberedList', '|',
                    'link', 'image', '|',
                    'undo', 'redo'
                ]
            });

            // 监听内容变化
            upgradeWordEditor.on('change', function(content) {
                document.getElementById('update-description').value = content;
            });

            console.log('Word编辑器初始化成功');
        } else {
            console.warn('Word编辑器未加载，使用备用文本框');
            createFallbackEditor();
        }
    } catch (error) {
        console.error('Word编辑器初始化失败:', error);
        createFallbackEditor();
    }
}

// 创建备用编辑器
function createFallbackEditor() {
    const container = document.getElementById('upgrade-word-editor');
    container.innerHTML = `
        <textarea id="fallback-editor" name="description_fallback"
                  placeholder="请输入版本更新说明..."
                  style="width: 100%; height: 200px; padding: 15px; border: 1px solid #ddd; border-radius: 4px; resize: vertical;">
        </textarea>
    `;

    // 同步到隐藏字段
    document.getElementById('fallback-editor').addEventListener('input', function() {
        document.getElementById('update-description').value = this.value;
    });
}

// 初始化文件上传
function initializeFileUpload() {
    // Windows exe文件上传
    setupFileUpload('exe-upload-area', 'exe-file', 'exe-preview', '.exe');

    // macOS dmg文件上传
    setupFileUpload('dmg-upload-area', 'dmg-file', 'dmg-preview', '.dmg');
}

// 设置文件上传区域
function setupFileUpload(areaId, inputId, previewId, acceptType) {
    const uploadArea = document.getElementById(areaId);
    const fileInput = document.getElementById(inputId);
    const preview = document.getElementById(previewId);

    // 点击上传区域
    uploadArea.addEventListener('click', () => {
        fileInput.click();
    });

    // 文件选择
    fileInput.addEventListener('change', function() {
        handleFileSelect(this.files[0], preview, acceptType);
    });

    // 拖拽上传
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            handleFileSelect(files[0], preview, acceptType);
        }
    });
}

// 处理文件选择
function handleFileSelect(file, preview, acceptType) {
    if (!file) {
        preview.style.display = 'none';
        return;
    }

    // 检查文件类型
    if (!file.name.toLowerCase().endsWith(acceptType)) {
        alert(`请选择${acceptType}格式的文件`);
        return;
    }

    // 显示文件预览
    const fileSize = formatFileSize(file.size);
    const fileName = file.name;

    preview.innerHTML = `
        <div class="file-info">
            <div class="file-icon">
                <i class="fas fa-file-archive"></i>
            </div>
            <div class="file-details">
                <div class="file-name">${fileName}</div>
                <div class="file-size">${fileSize}</div>
                <div class="file-status">
                    <i class="fas fa-check-circle" style="color: #28a745;"></i>
                    <span>文件已选择</span>
                </div>
            </div>
            <button type="button" class="remove-file" onclick="removeFile('${preview.id}', '${preview.id.replace('-preview', '-file')}')">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    preview.style.display = 'block';
}

// 移除文件
function removeFile(previewId, inputId) {
    document.getElementById(previewId).style.display = 'none';
    document.getElementById(inputId).value = '';
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 初始化表单提交
function initializeFormSubmit() {
    const form = document.getElementById('upgrade-form');

    form.addEventListener('submit', function(e) {
        e.preventDefault();
        submitUpgradeForm();
    });
}

// 提交升级表单
async function submitUpgradeForm() {
    const form = document.getElementById('upgrade-form');
    const formData = new FormData(form);

    // 验证表单
    if (!validateUpgradeForm(formData)) {
        return;
    }

    // 显示加载状态
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 正在发布...';
    submitBtn.disabled = true;

    try {
        // 发送请求到API
        const response = await fetch('../app_update_standalone.php', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.success) {
            alert('版本发布成功！');
            resetUpgradeForm();
            loadVersionList(); // 重新加载版本列表
        } else {
            alert('发布失败：' + result.message);
        }
    } catch (error) {
        console.error('发布失败:', error);
        alert('发布失败：网络错误');
    } finally {
        // 恢复按钮状态
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }
}

// 验证升级表单
function validateUpgradeForm(formData) {
    const title = formData.get('title');
    const description = formData.get('description');
    const exeFile = formData.get('exe_file');
    const dmgFile = formData.get('dmg_file');

    if (!title || title.trim() === '') {
        alert('请输入版本标题');
        return false;
    }

    if (!description || description.trim() === '') {
        alert('请输入更新说明');
        return false;
    }

    if ((!exeFile || exeFile.size === 0) && (!dmgFile || dmgFile.size === 0)) {
        alert('请至少上传一个安装包文件（Windows或macOS）');
        return false;
    }

    return true;
}

// 重置升级表单
function resetUpgradeForm() {
    const form = document.getElementById('upgrade-form');
    form.reset();

    // 清空编辑器
    if (upgradeWordEditor) {
        upgradeWordEditor.setContent('');
    } else {
        const fallbackEditor = document.getElementById('fallback-editor');
        if (fallbackEditor) {
            fallbackEditor.value = '';
        }
    }

    // 隐藏文件预览
    document.getElementById('exe-preview').style.display = 'none';
    document.getElementById('dmg-preview').style.display = 'none';

    // 清空隐藏字段
    document.getElementById('update-description').value = '';
}

// 预览升级效果
function previewUpgrade() {
    const title = document.getElementById('version-title').value;
    const description = document.getElementById('update-description').value;

    if (!title || !description) {
        alert('请先填写版本标题和更新说明');
        return;
    }

    // 创建预览弹窗
    showUpgradePreview(title, description);
}

// 显示升级预览弹窗
function showUpgradePreview(title, description) {
    // 创建预览弹窗HTML
    const previewHTML = `
        <div class="upgrade-preview-overlay" id="upgrade-preview-overlay">
            <div class="upgrade-preview-modal">
                <div class="preview-header">
                    <h3><i class="fas fa-eye"></i> 升级预览</h3>
                    <button class="close-preview" onclick="closeUpgradePreview()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="preview-content">
                    <div class="app-update-dialog">
                        <div class="update-icon">
                            <i class="fas fa-download"></i>
                        </div>
                        <div class="update-title">${title}</div>
                        <div class="update-description">${description}</div>
                        <div class="update-actions">
                            <button class="update-btn update-now">立即更新</button>
                            <button class="update-btn update-later">稍后提醒</button>
                        </div>
                        <div class="update-progress" style="display: none;">
                            <div class="progress-bar">
                                <div class="progress-fill"></div>
                            </div>
                            <div class="progress-text">下载中... 0%</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', previewHTML);

    // 添加样式
    addPreviewStyles();
}

// 关闭升级预览
function closeUpgradePreview() {
    const overlay = document.getElementById('upgrade-preview-overlay');
    if (overlay) {
        overlay.remove();
    }
}

// 添加预览样式
function addPreviewStyles() {
    if (document.getElementById('preview-styles')) return;

    const styles = `
        <style id="preview-styles">
        .upgrade-preview-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
        }

        .upgrade-preview-modal {
            background: white;
            border-radius: 12px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow: auto;
        }

        .preview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 25px;
            border-bottom: 1px solid #e9ecef;
        }

        .preview-header h3 {
            margin: 0;
            color: #495057;
        }

        .close-preview {
            background: none;
            border: none;
            font-size: 1.2em;
            color: #6c757d;
            cursor: pointer;
            padding: 5px;
        }

        .preview-content {
            padding: 30px;
        }

        .app-update-dialog {
            text-align: center;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: #f8f9fa;
        }

        .update-icon {
            font-size: 3em;
            color: #007bff;
            margin-bottom: 20px;
        }

        .update-title {
            font-size: 1.3em;
            font-weight: 600;
            color: #495057;
            margin-bottom: 15px;
        }

        .update-description {
            color: #6c757d;
            margin-bottom: 25px;
            line-height: 1.6;
        }

        .update-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-bottom: 20px;
        }

        .update-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .update-now {
            background: #007bff;
            color: white;
        }

        .update-later {
            background: #6c757d;
            color: white;
        }

        .update-progress {
            margin-top: 20px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-fill {
            height: 100%;
            background: #007bff;
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-text {
            color: #6c757d;
            font-size: 0.9em;
        }
        </style>
    `;

    document.head.insertAdjacentHTML('beforeend', styles);
}
</script>
