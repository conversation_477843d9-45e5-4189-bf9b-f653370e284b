<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>APP升级客户端示例</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .app-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn:disabled { opacity: 0.6; cursor: not-allowed; }
        .update-dialog {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.3);
            z-index: 1000;
            max-width: 500px;
            width: 90%;
        }
        .update-dialog.show { display: block; }
        .overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 999;
        }
        .overlay.show { display: block; }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 15px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #007bff, #0056b3);
            width: 0%;
            transition: width 0.3s ease;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>APP升级客户端示例</h1>
        
        <div class="app-info">
            <h3>当前APP信息</h3>
            <p><strong>应用名称:</strong> 小梅花AI助手</p>
            <p><strong>当前版本:</strong> <span id="current-version">1.0.0</span></p>
            <p><strong>平台:</strong> <span id="current-platform">Windows</span></p>
        </div>

        <div>
            <button class="btn btn-primary" onclick="checkForUpdates()">检查更新</button>
            <button class="btn btn-success" onclick="simulateAutoCheck()">模拟自动检查</button>
            <button class="btn btn-warning" onclick="clearLog()">清空日志</button>
        </div>

        <div id="status" class="status" style="display: none;"></div>
        <div id="log" class="log"></div>
    </div>

    <!-- 更新对话框 -->
    <div id="overlay" class="overlay"></div>
    <div id="update-dialog" class="update-dialog">
        <h3 id="update-title">发现新版本</h3>
        <p id="update-description">正在准备更新...</p>
        
        <div id="update-progress" style="display: none;">
            <p>下载进度:</p>
            <div class="progress-bar">
                <div id="progress-fill" class="progress-fill"></div>
            </div>
            <p id="progress-text">0%</p>
        </div>

        <div id="update-buttons">
            <button class="btn btn-primary" onclick="startUpdate()">立即更新</button>
            <button class="btn" onclick="closeUpdateDialog()">稍后提醒</button>
        </div>
    </div>

    <script>
        let currentVersion = '1.0.0';
        let currentPlatform = 'windows';
        let updateInfo = null;

        // 添加日志
        function addLog(message, type = 'info') {
            const log = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            log.textContent += logEntry;
            log.scrollTop = log.scrollHeight;
        }

        // 显示状态
        function showStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = 'status ' + type;
            status.style.display = 'block';
        }

        // 清空日志
        function clearLog() {
            document.getElementById('log').textContent = '';
            document.getElementById('status').style.display = 'none';
        }

        // 检查更新
        async function checkForUpdates() {
            addLog('开始检查更新...');
            showStatus('正在检查更新...', 'info');

            try {
                const url = `app_update_standalone.php?action=check&version=${currentVersion}&platform=${currentPlatform}`;
                addLog(`请求URL: ${url}`);

                const response = await fetch(url);
                const result = await response.json();

                addLog(`服务器响应: ${JSON.stringify(result, null, 2)}`);

                if (result.success) {
                    if (result.data.has_update) {
                        updateInfo = result.data.update_info;
                        showStatus(`发现新版本 ${result.data.latest_version}`, 'success');
                        addLog(`发现新版本: ${result.data.latest_version}`);
                        showUpdateDialog();
                    } else {
                        showStatus('当前已是最新版本', 'success');
                        addLog('当前已是最新版本');
                    }
                } else {
                    showStatus('检查更新失败: ' + result.message, 'error');
                    addLog('检查更新失败: ' + result.message);
                }
            } catch (error) {
                showStatus('网络错误: ' + error.message, 'error');
                addLog('网络错误: ' + error.message);
            }
        }

        // 显示更新对话框
        function showUpdateDialog() {
            if (!updateInfo) return;

            document.getElementById('update-title').textContent = updateInfo.title;
            document.getElementById('update-description').innerHTML = updateInfo.description;
            
            document.getElementById('overlay').classList.add('show');
            document.getElementById('update-dialog').classList.add('show');
            
            addLog('显示更新对话框');
        }

        // 关闭更新对话框
        function closeUpdateDialog() {
            document.getElementById('overlay').classList.remove('show');
            document.getElementById('update-dialog').classList.remove('show');
            addLog('用户选择稍后更新');
        }

        // 开始更新
        function startUpdate() {
            if (!updateInfo) return;

            addLog('用户确认开始更新');
            
            // 隐藏按钮，显示进度条
            document.getElementById('update-buttons').style.display = 'none';
            document.getElementById('update-progress').style.display = 'block';

            // 模拟下载进度
            simulateDownload();
        }

        // 模拟下载进度
        function simulateDownload() {
            let progress = 0;
            const progressFill = document.getElementById('progress-fill');
            const progressText = document.getElementById('progress-text');

            const interval = setInterval(() => {
                progress += Math.random() * 10;
                if (progress > 100) progress = 100;

                progressFill.style.width = progress + '%';
                progressText.textContent = Math.round(progress) + '%';
                
                addLog(`下载进度: ${Math.round(progress)}%`);

                if (progress >= 100) {
                    clearInterval(interval);
                    setTimeout(() => {
                        completeUpdate();
                    }, 1000);
                }
            }, 500);
        }

        // 完成更新
        function completeUpdate() {
            addLog('下载完成，准备安装...');
            
            setTimeout(() => {
                addLog('安装完成，应用将重启');
                
                // 更新版本号
                currentVersion = updateInfo.version;
                document.getElementById('current-version').textContent = currentVersion;
                
                closeUpdateDialog();
                showStatus('更新完成！', 'success');
                
                // 重置对话框状态
                document.getElementById('update-buttons').style.display = 'block';
                document.getElementById('update-progress').style.display = 'none';
                document.getElementById('progress-fill').style.width = '0%';
                document.getElementById('progress-text').textContent = '0%';
                
                updateInfo = null;
            }, 2000);
        }

        // 模拟自动检查
        function simulateAutoCheck() {
            addLog('启动自动检查更新（每30秒检查一次）');
            
            let checkCount = 0;
            const autoCheckInterval = setInterval(() => {
                checkCount++;
                addLog(`自动检查 #${checkCount}`);
                checkForUpdates();
                
                // 5次后停止自动检查
                if (checkCount >= 5) {
                    clearInterval(autoCheckInterval);
                    addLog('自动检查已停止');
                }
            }, 30000);
        }

        // 页面加载时的初始化
        window.addEventListener('load', function() {
            addLog('APP升级客户端已启动');
            addLog(`当前版本: ${currentVersion}`);
            addLog(`当前平台: ${currentPlatform}`);
            
            // 模拟启动时自动检查
            setTimeout(() => {
                addLog('启动时自动检查更新...');
                checkForUpdates();
            }, 2000);
        });

        // 设置平台
        function setPlatform(platform) {
            currentPlatform = platform;
            document.getElementById('current-platform').textContent = platform === 'windows' ? 'Windows' : 'macOS';
            addLog(`平台已切换为: ${platform}`);
        }

        // 集成升级器类
        class AppUpdater {
            constructor(options = {}) {
                this.apiUrl = options.apiUrl || 'app_update_standalone.php';
                this.currentVersion = options.currentVersion || '1.0.0';
                this.platform = options.platform || 'windows';
                this.autoCheckInterval = options.autoCheckInterval || 30000; // 30秒用于演示
            }

            async checkForUpdates() {
                try {
                    const url = `${this.apiUrl}?action=check&version=${this.currentVersion}&platform=${this.platform}`;
                    addLog(`发送请求: ${url}`);

                    const response = await fetch(url);
                    const result = await response.json();

                    if (result.success && result.data.has_update) {
                        addLog(`发现新版本: ${result.data.latest_version}`);
                        this.showUpdateDialog(result.data.update_info);
                        return result.data.update_info;
                    } else {
                        addLog('当前已是最新版本');
                        return null;
                    }
                } catch (error) {
                    addLog('检查更新失败: ' + error.message);
                    return null;
                }
            }

            showUpdateDialog(updateInfo) {
                // 使用现有的更新对话框
                document.getElementById('update-title').textContent = updateInfo.title;
                document.getElementById('update-description').innerHTML = updateInfo.description;

                document.getElementById('overlay').classList.add('show');
                document.getElementById('update-dialog').classList.add('show');

                // 存储更新信息
                window.currentUpdateInfo = updateInfo;
                addLog('显示更新对话框');
            }

            async startDownload(updateInfo) {
                addLog('开始下载更新...');

                // 模拟真实下载过程
                return new Promise((resolve) => {
                    let progress = 0;
                    const interval = setInterval(() => {
                        progress += Math.random() * 8 + 2;
                        if (progress > 100) progress = 100;

                        const downloadedMB = (progress / 100) * 25.6;
                        const progressFill = document.getElementById('progress-fill');
                        const progressText = document.getElementById('progress-text');

                        progressFill.style.width = progress + '%';
                        progressText.textContent = `${Math.round(progress)}% - ${downloadedMB.toFixed(1)} MB / 25.6 MB`;

                        addLog(`下载进度: ${Math.round(progress)}%`);

                        if (progress >= 100) {
                            clearInterval(interval);
                            addLog('下载完成');
                            resolve();
                        }
                    }, 200);
                });
            }
        }

        // 创建升级器实例
        const appUpdater = new AppUpdater({
            currentVersion: currentVersion,
            platform: currentPlatform
        });

        // 重写检查更新函数使用升级器
        async function checkForUpdates() {
            showStatus('正在检查更新...', 'info');
            const updateInfo = await appUpdater.checkForUpdates();

            if (updateInfo) {
                showStatus(`发现新版本 ${updateInfo.version}`, 'success');
            } else {
                showStatus('当前已是最新版本', 'success');
            }
        }

        // 重写开始更新函数
        function startUpdate() {
            if (!window.currentUpdateInfo) return;

            addLog('用户确认开始更新');

            // 隐藏按钮，显示进度条
            document.getElementById('update-buttons').style.display = 'none';
            document.getElementById('update-progress').style.display = 'block';

            // 使用升级器下载
            appUpdater.startDownload(window.currentUpdateInfo).then(() => {
                setTimeout(() => {
                    addLog('安装完成，应用将重启');

                    // 更新版本号
                    currentVersion = window.currentUpdateInfo.version;
                    document.getElementById('current-version').textContent = currentVersion;

                    closeUpdateDialog();
                    showStatus('更新完成！', 'success');

                    // 重置对话框状态
                    document.getElementById('update-buttons').style.display = 'block';
                    document.getElementById('update-progress').style.display = 'none';
                    document.getElementById('progress-fill').style.width = '0%';
                    document.getElementById('progress-text').textContent = '0%';

                    window.currentUpdateInfo = null;
                }, 1000);
            });
        }

        // 添加平台切换按钮
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.querySelector('.container');
            const platformDiv = document.createElement('div');
            platformDiv.innerHTML = `
                <h3>平台设置</h3>
                <button class="btn btn-primary" onclick="setPlatform('windows')">Windows</button>
                <button class="btn btn-primary" onclick="setPlatform('macos')">macOS</button>
                <h3>升级器测试</h3>
                <button class="btn btn-success" onclick="testIntegratedUpdater()">测试集成升级器</button>
            `;
            container.insertBefore(platformDiv, container.children[2]);
        });

        // 测试集成升级器
        function testIntegratedUpdater() {
            addLog('=== 开始测试集成升级器 ===');
            addLog('这是一个完整的APP升级流程演示');

            // 更新升级器配置
            appUpdater.currentVersion = currentVersion;
            appUpdater.platform = currentPlatform;

            // 执行检查
            checkForUpdates();
        }
    </script>
</body>
</html>
