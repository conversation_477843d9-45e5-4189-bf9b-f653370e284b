/**
 * Word风格富文本编辑器
 * 完全自定义实现，无需第三方库
 */

class WordStyleEditor {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.options = {
            placeholder: '请输入内容...',
            minHeight: '200px',
            ...options
        };
        
        this.currentFont = 'Microsoft YaHei';
        this.currentSize = '14';
        this.currentTextColor = '#333333';
        this.currentBgColor = '#ffffff';
        
        this.init();
    }
    
    init() {
        this.createEditor();
        this.bindEvents();
        this.updateToolbarState();
    }
    
    createEditor() {
        this.container.innerHTML = `
            <div class="word-editor-container">
                <div class="word-toolbar">
                    <!-- 字体和大小组 -->
                    <div class="toolbar-group">
                        <div class="font-selector" data-action="font">
                            <span class="font-display">${this.currentFont}</span>
                            <div class="font-options">
                                <div class="font-option" data-font="Microsoft YaHei">微软雅黑</div>
                                <div class="font-option" data-font="SimSun">宋体</div>
                                <div class="font-option" data-font="SimHei">黑体</div>
                                <div class="font-option" data-font="KaiTi">楷体</div>
                                <div class="font-option" data-font="Arial">Arial</div>
                                <div class="font-option" data-font="Times New Roman">Times New Roman</div>
                                <div class="font-option" data-font="Helvetica">Helvetica</div>
                            </div>
                        </div>
                        
                        <div class="size-control-group">
                            <button class="toolbar-btn size-decrease" data-action="decreaseSize" title="减小字体 (A-)">
                                A<sup>-</sup>
                            </button>
                            <span class="size-display">${this.currentSize}</span>
                            <button class="toolbar-btn size-increase" data-action="increaseSize" title="增大字体 (A+)">
                                A<sup>+</sup>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 格式化组 -->
                    <div class="toolbar-group">
                        <button class="toolbar-btn bold" data-action="bold" title="加粗 (Ctrl+B)">
                            <strong>B</strong>
                        </button>
                        <button class="toolbar-btn italic" data-action="italic" title="斜体 (Ctrl+I)">
                            <em>I</em>
                        </button>
                        <button class="toolbar-btn underline" data-action="underline" title="下划线 (Ctrl+U)">
                            <u>U</u>
                        </button>
                    </div>
                    
                    <!-- 颜色组 -->
                    <div class="toolbar-group">
                        <button class="toolbar-btn color-btn" data-action="textColor" title="文字颜色">
                            A
                            <div class="color-indicator" style="background-color: ${this.currentTextColor}"></div>
                            <div class="color-picker-panel text-color-panel">
                                <div class="color-grid">
                                    <div class="color-item" style="background: #000000" data-color="#000000"></div>
                                    <div class="color-item" style="background: #333333" data-color="#333333"></div>
                                    <div class="color-item" style="background: #666666" data-color="#666666"></div>
                                    <div class="color-item" style="background: #999999" data-color="#999999"></div>
                                    <div class="color-item" style="background: #cccccc" data-color="#cccccc"></div>
                                    <div class="color-item" style="background: #ffffff" data-color="#ffffff"></div>
                                    <div class="color-item" style="background: #ff0000" data-color="#ff0000"></div>
                                    <div class="color-item" style="background: #00ff00" data-color="#00ff00"></div>
                                    <div class="color-item" style="background: #0000ff" data-color="#0000ff"></div>
                                    <div class="color-item" style="background: #ffff00" data-color="#ffff00"></div>
                                    <div class="color-item" style="background: #ff00ff" data-color="#ff00ff"></div>
                                    <div class="color-item" style="background: #00ffff" data-color="#00ffff"></div>
                                    <div class="color-item" style="background: #ff6b9d" data-color="#ff6b9d"></div>
                                    <div class="color-item" style="background: #c44569" data-color="#c44569"></div>
                                    <div class="color-item" style="background: #f8b500" data-color="#f8b500"></div>
                                    <div class="color-item" style="background: #6c5ce7" data-color="#6c5ce7"></div>
                                </div>
                            </div>
                        </button>
                        
                        <button class="toolbar-btn color-btn" data-action="backgroundColor" title="背景颜色">
                            A
                            <div class="color-indicator" style="background-color: ${this.currentBgColor}"></div>
                            <div class="color-picker-panel bg-color-panel">
                                <div class="color-grid">
                                    <div class="color-item" style="background: transparent; border: 1px solid #ccc" data-color="transparent" title="无背景"></div>
                                    <div class="color-item" style="background: #ffff00" data-color="#ffff00"></div>
                                    <div class="color-item" style="background: #00ff00" data-color="#00ff00"></div>
                                    <div class="color-item" style="background: #00ffff" data-color="#00ffff"></div>
                                    <div class="color-item" style="background: #ff00ff" data-color="#ff00ff"></div>
                                    <div class="color-item" style="background: #0000ff" data-color="#0000ff"></div>
                                    <div class="color-item" style="background: #ff0000" data-color="#ff0000"></div>
                                    <div class="color-item" style="background: #800000" data-color="#800000"></div>
                                    <div class="color-item" style="background: #ffcccc" data-color="#ffcccc"></div>
                                    <div class="color-item" style="background: #ccffcc" data-color="#ccffcc"></div>
                                    <div class="color-item" style="background: #ccccff" data-color="#ccccff"></div>
                                    <div class="color-item" style="background: #ffffcc" data-color="#ffffcc"></div>
                                    <div class="color-item" style="background: #ffccff" data-color="#ffccff"></div>
                                    <div class="color-item" style="background: #ccffff" data-color="#ccffff"></div>
                                    <div class="color-item" style="background: #f0f0f0" data-color="#f0f0f0"></div>
                                    <div class="color-item" style="background: #e0e0e0" data-color="#e0e0e0"></div>
                                </div>
                            </div>
                        </button>
                    </div>
                    
                    <!-- 对齐组 -->
                    <div class="toolbar-group">
                        <button class="toolbar-btn" data-action="justifyLeft" title="左对齐">
                            <i class="fas fa-align-left"></i>
                        </button>
                        <button class="toolbar-btn" data-action="justifyCenter" title="居中对齐">
                            <i class="fas fa-align-center"></i>
                        </button>
                        <button class="toolbar-btn" data-action="justifyRight" title="右对齐">
                            <i class="fas fa-align-right"></i>
                        </button>
                    </div>
                </div>
                
                <div class="word-editor-content" 
                     contenteditable="true" 
                     style="min-height: ${this.options.minHeight}"
                     data-placeholder="${this.options.placeholder}">
                </div>
            </div>
        `;
        
        this.toolbar = this.container.querySelector('.word-toolbar');
        this.editor = this.container.querySelector('.word-editor-content');
    }
    
    bindEvents() {
        // 工具栏按钮事件
        this.toolbar.addEventListener('click', (e) => {
            e.preventDefault();
            const btn = e.target.closest('[data-action]');
            if (btn) {
                this.handleToolbarAction(btn.dataset.action, btn);
            }
        });
        
        // 字体和颜色选择事件
        this.container.addEventListener('click', (e) => {
            e.preventDefault();

            if (e.target.classList.contains('font-option')) {
                e.stopPropagation();
                this.setFont(e.target.dataset.font, e.target.textContent);
                return;
            }

            if (e.target.classList.contains('color-item')) {
                e.stopPropagation();
                const panel = e.target.closest('.color-picker-panel');
                if (panel.classList.contains('text-color-panel')) {
                    this.setTextColor(e.target.dataset.color);
                } else if (panel.classList.contains('bg-color-panel')) {
                    this.setBackgroundColor(e.target.dataset.color);
                }
                return;
            }
        });
        
        // 编辑器事件
        this.editor.addEventListener('keyup', () => this.updateToolbarState());
        this.editor.addEventListener('mouseup', () => this.updateToolbarState());
        this.editor.addEventListener('focus', () => this.updateToolbarState());
        this.editor.addEventListener('input', () => {
            this.updateToolbarState();
        });

        // 监听键盘事件以触发自动格式化
        this.editor.addEventListener('keydown', (e) => {
            // 在回车键或空格键后延迟触发格式化
            if (e.key === 'Enter' || e.key === ' ') {
                setTimeout(() => {
                    this.autoFormatTitles();
                }, 100);
            }
        });

        // 监听粘贴事件以触发格式化
        this.editor.addEventListener('paste', (e) => {
            console.log('检测到粘贴事件，将在粘贴完成后触发格式化');
            // 延迟触发格式化，确保粘贴内容已经插入
            setTimeout(() => {
                console.log('开始对粘贴内容进行格式化');
                this.formatAllTitles();
            }, 200);
        });
        
        // 键盘快捷键
        this.editor.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key.toLowerCase()) {
                    case 'b':
                        e.preventDefault();
                        this.execCommand('bold');
                        break;
                    case 'i':
                        e.preventDefault();
                        this.execCommand('italic');
                        break;
                    case 'u':
                        e.preventDefault();
                        this.execCommand('underline');
                        break;
                }
            }
        });
        
        // 点击其他地方关闭下拉菜单
        document.addEventListener('click', (e) => {
            if (!this.container.contains(e.target)) {
                this.closeAllDropdowns();
            }
        });
    }

    handleToolbarAction(action, btn) {
        // 保存当前选择
        const selection = window.getSelection();
        const savedRange = selection.rangeCount > 0 ? selection.getRangeAt(0).cloneRange() : null;

        switch (action) {
            case 'font':
                this.toggleDropdown(btn.querySelector('.font-options'));
                break;
            case 'increaseSize':
                // 增大字体，不失去选择
                this.changeFontSize(2, savedRange);
                break;
            case 'decreaseSize':
                // 减小字体，不失去选择
                this.changeFontSize(-2, savedRange);
                break;
            case 'textColor':
                this.toggleDropdown(btn.querySelector('.text-color-panel'));
                break;
            case 'backgroundColor':
                this.toggleDropdown(btn.querySelector('.bg-color-panel'));
                break;
            case 'bold':
            case 'italic':
            case 'underline':
            case 'justifyLeft':
            case 'justifyCenter':
            case 'justifyRight':
                this.editor.focus();
                // 恢复选择
                if (savedRange) {
                    selection.removeAllRanges();
                    selection.addRange(savedRange);
                }
                this.execCommand(action);
                break;
        }

        this.updateToolbarState();
    }

    execCommand(command, value = null) {
        try {
            document.execCommand(command, false, value);
        } catch (e) {
            console.warn('execCommand not supported:', command);
        }
    }

    setFont(fontFamily, displayName) {
        console.log('设置字体:', fontFamily, displayName);

        const selection = window.getSelection();

        if (selection.rangeCount > 0) {
            const range = selection.getRangeAt(0);

            if (!range.collapsed) {
                // 有选中文本，保存选择信息
                const selectedText = range.toString();
                console.log('选中的文本:', selectedText);

                // 应用字体到选中文本
                this.applyFontToSelection(range, fontFamily);

                // 重新选择文本以保持选择状态
                setTimeout(() => {
                    try {
                        // 查找刚才应用字体的span元素
                        const spans = this.editor.querySelectorAll('span[style*="font-family"]');
                        let targetSpan = null;

                        for (let span of spans) {
                            if (span.textContent === selectedText &&
                                span.style.fontFamily.includes(fontFamily.replace(/['"]/g, ''))) {
                                targetSpan = span;
                                break;
                            }
                        }

                        if (targetSpan) {
                            const newRange = document.createRange();
                            newRange.selectNodeContents(targetSpan);
                            selection.removeAllRanges();
                            selection.addRange(newRange);
                            console.log('成功重新选择文本，字体已应用');
                        }
                    } catch (e) {
                        console.warn('重新选择文本失败:', e);
                    }
                }, 50);

            } else {
                // 没有选中文本，设置当前字体
                this.currentFont = displayName;
                console.log('设置当前字体为:', displayName);
            }
        }

        // 更新显示
        this.currentFont = displayName;
        const fontDisplay = this.container.querySelector('.font-display');
        if (fontDisplay) {
            fontDisplay.textContent = displayName;
        }

        this.closeAllDropdowns();

        // 保持编辑器聚焦
        this.editor.focus();
    }

    // 应用字体到选中文本
    applyFontToSelection(range, fontFamily) {
        try {
            // 获取选中的文本
            const selectedText = range.toString();

            if (!selectedText.trim()) {
                console.warn('没有选中文本');
                return;
            }

            // 标准化字体名称，确保正确的CSS格式
            let normalizedFontFamily = fontFamily;

            // 为包含空格的字体名称添加引号
            if (fontFamily.includes(' ') && !fontFamily.includes('"') && !fontFamily.includes("'")) {
                normalizedFontFamily = `"${fontFamily}"`;
            }

            // 添加备用字体
            const fontFamilyWithFallback = this.getFontFamilyWithFallback(normalizedFontFamily);

            console.log('原始字体名:', fontFamily);
            console.log('标准化字体名:', normalizedFontFamily);
            console.log('带备用字体:', fontFamilyWithFallback);

            // 删除选中的内容
            range.deleteContents();

            // 创建新的span元素
            const wrapper = document.createElement('span');
            wrapper.style.fontFamily = fontFamilyWithFallback;
            wrapper.textContent = selectedText;

            // 插入新元素
            range.insertNode(wrapper);

            console.log('成功应用字体:', fontFamilyWithFallback);

            // 触发input事件以更新隐藏字段
            const inputEvent = new Event('input', { bubbles: true });
            this.editor.dispatchEvent(inputEvent);

        } catch (e) {
            console.warn('应用字体失败:', e);
        }
    }

    // 获取带备用字体的字体族
    getFontFamilyWithFallback(fontFamily) {
        const fontMap = {
            'Microsoft YaHei': '"Microsoft YaHei", "微软雅黑", sans-serif',
            '"Microsoft YaHei"': '"Microsoft YaHei", "微软雅黑", sans-serif',
            'SimSun': '"SimSun", "宋体", serif',
            '"SimSun"': '"SimSun", "宋体", serif',
            'SimHei': '"SimHei", "黑体", sans-serif',
            '"SimHei"': '"SimHei", "黑体", sans-serif',
            'KaiTi': '"KaiTi", "楷体", serif',
            '"KaiTi"': '"KaiTi", "楷体", serif',
            'Arial': 'Arial, sans-serif',
            '"Arial"': 'Arial, sans-serif',
            'Times New Roman': '"Times New Roman", serif',
            '"Times New Roman"': '"Times New Roman", serif',
            'Helvetica': 'Helvetica, Arial, sans-serif',
            '"Helvetica"': 'Helvetica, Arial, sans-serif'
        };

        return fontMap[fontFamily] || fontFamily;
    }

    // 新的字体大小变化方法（加减方式）
    changeFontSize(delta, savedRange) {
        console.log('改变字体大小:', delta);

        const selection = window.getSelection();

        // 恢复保存的选择
        if (savedRange) {
            selection.removeAllRanges();
            selection.addRange(savedRange);
        }

        if (selection.rangeCount > 0) {
            const range = selection.getRangeAt(0);

            if (!range.collapsed) {
                // 有选中文本，获取当前大小并调整
                const currentSize = this.getCurrentFontSize(range);
                const newSize = Math.max(8, Math.min(72, currentSize + delta)); // 限制在8-72px之间

                console.log('当前大小:', currentSize, '新大小:', newSize);

                // 保存选中的文本内容和位置信息
                const selectedText = range.toString();
                const startContainer = range.startContainer;
                const startOffset = range.startOffset;
                const endContainer = range.endContainer;
                const endOffset = range.endOffset;

                // 应用新大小
                this.applyFontSizeToSelection(range, newSize);

                // 更新显示
                this.currentSize = newSize;
                const sizeDisplay = this.container.querySelector('.size-display');
                if (sizeDisplay) {
                    sizeDisplay.textContent = newSize;
                }

                // 重新选择刚才修改的文本，确保选择保持
                setTimeout(() => {
                    try {
                        // 查找刚才插入的span元素
                        const spans = this.editor.querySelectorAll('span[style*="font-size"]');
                        let targetSpan = null;

                        // 找到包含相同文本且大小匹配的span
                        for (let span of spans) {
                            if (span.textContent === selectedText &&
                                span.style.fontSize === newSize + 'px') {
                                targetSpan = span;
                                break;
                            }
                        }

                        if (targetSpan) {
                            const newRange = document.createRange();
                            newRange.selectNodeContents(targetSpan);
                            selection.removeAllRanges();
                            selection.addRange(newRange);
                            console.log('成功重新选择文本，可以继续调整大小');
                        } else {
                            console.warn('未找到目标span，尝试其他方法重新选择');
                            // 备用方法：选择整个编辑器内容
                            const newRange = document.createRange();
                            newRange.selectNodeContents(this.editor);
                            selection.removeAllRanges();
                            selection.addRange(newRange);
                        }
                    } catch (e) {
                        console.warn('重新选择文本失败:', e);
                    }
                }, 50);

            } else {
                // 没有选中文本，调整当前大小设置
                this.currentSize = Math.max(8, Math.min(72, this.currentSize + delta));
                const sizeDisplay = this.container.querySelector('.size-display');
                if (sizeDisplay) {
                    sizeDisplay.textContent = this.currentSize;
                }
            }
        }

        // 保持编辑器聚焦
        this.editor.focus();
    }

    // 获取选中文本的当前字体大小
    getCurrentFontSize(range) {
        let element = range.commonAncestorContainer;

        // 如果是文本节点，获取其父元素
        if (element.nodeType === Node.TEXT_NODE) {
            element = element.parentElement;
        }

        // 获取计算后的字体大小
        if (element && element.nodeType === Node.ELEMENT_NODE) {
            const computedStyle = window.getComputedStyle(element);
            const fontSize = computedStyle.fontSize;
            return parseInt(fontSize) || this.currentSize;
        }

        return this.currentSize;
    }

    // 应用字体大小到选中文本
    applyFontSizeToSelection(range, size) {
        try {
            // 获取选中的文本
            const selectedText = range.toString();

            if (!selectedText.trim()) {
                console.warn('没有选中文本');
                return;
            }

            // 删除选中的内容
            range.deleteContents();

            // 创建新的span元素
            const wrapper = document.createElement('span');
            wrapper.style.fontSize = size + 'px';
            wrapper.textContent = selectedText;

            // 插入新元素
            range.insertNode(wrapper);

            console.log('成功应用字体大小:', size + 'px');

            // 触发input事件以更新隐藏字段
            const inputEvent = new Event('input', { bubbles: true });
            this.editor.dispatchEvent(inputEvent);

        } catch (e) {
            console.warn('应用字体大小失败:', e);
        }
    }

    setTextColor(color) {
        this.execCommand('foreColor', color);
        this.currentTextColor = color;
        this.container.querySelector('.text-color-panel').closest('.color-btn')
            .querySelector('.color-indicator').style.backgroundColor = color;
        this.closeAllDropdowns();
    }

    setBackgroundColor(color) {
        if (color === 'transparent') {
            this.execCommand('hiliteColor', 'transparent');
            this.execCommand('backColor', 'transparent');
        } else {
            this.execCommand('hiliteColor', color);
        }
        this.currentBgColor = color;
        this.container.querySelector('.bg-color-panel').closest('.color-btn')
            .querySelector('.color-indicator').style.backgroundColor = color;
        this.closeAllDropdowns();
    }

    toggleDropdown(dropdown) {
        this.closeAllDropdowns();
        if (dropdown) {
            dropdown.classList.add('show');
        }
    }

    closeAllDropdowns() {
        const dropdowns = this.container.querySelectorAll('.font-options, .color-picker-panel');
        dropdowns.forEach(dropdown => dropdown.classList.remove('show'));
    }

    updateToolbarState() {
        // 更新按钮活动状态
        const commands = ['bold', 'italic', 'underline', 'justifyLeft', 'justifyCenter', 'justifyRight'];

        commands.forEach(command => {
            const btn = this.container.querySelector(`[data-action="${command}"]`);
            if (btn) {
                try {
                    const isActive = document.queryCommandState(command);
                    btn.classList.toggle('active', isActive);
                } catch (e) {
                    // 某些浏览器可能不支持某些命令
                }
            }
        });

        // 更新字体和大小显示
        this.updateCurrentStyles();
    }

    // 获取编辑器内容
    getContent() {
        return this.editor.innerHTML;
    }

    // 设置编辑器内容
    setContent(html) {
        this.editor.innerHTML = html;
        // 设置内容后触发格式化
        setTimeout(() => {
            this.formatAllTitles();
        }, 100);
    }

    // 获取纯文本内容
    getText() {
        return this.editor.textContent || this.editor.innerText;
    }

    // 清空内容
    clear() {
        this.editor.innerHTML = '';
    }

    // 插入HTML
    insertHTML(html) {
        this.editor.focus();
        this.execCommand('insertHTML', html);
    }

    // 插入文本
    insertText(text) {
        this.editor.focus();
        this.execCommand('insertText', text);
    }

    // 简化的包装选中文本方法
    wrapSelectionWithStyle(range, styleProperty, styleValue) {
        try {
            // 保存选择的文本内容
            const selectedText = range.toString();
            console.log('选中的文本:', selectedText);

            if (!selectedText.trim()) {
                console.warn('没有选中文本');
                return;
            }

            // 删除选中的内容
            range.deleteContents();

            // 创建新的span元素
            const wrapper = document.createElement('span');
            wrapper.style[styleProperty] = styleValue;
            wrapper.textContent = selectedText;

            // 插入新元素
            range.insertNode(wrapper);

            // 重新选择包装后的内容
            const newRange = document.createRange();
            newRange.selectNodeContents(wrapper);
            const selection = window.getSelection();
            selection.removeAllRanges();
            selection.addRange(newRange);

            console.log('成功包装选中文本，样式:', styleProperty, styleValue);

            // 触发input事件以更新隐藏字段
            const inputEvent = new Event('input', { bubbles: true });
            this.editor.dispatchEvent(inputEvent);

        } catch (e) {
            console.warn('包装选中文本失败:', e);
        }
    }

    // 应用样式到选中的文本
    applyStyleToSelection(range, styleProperty, styleValue) {
        try {
            const selection = window.getSelection();

            // 简化的方法：直接使用execCommand + CSS修复
            if (styleProperty === 'fontSize') {
                // 使用execCommand设置字体大小
                document.execCommand('fontSize', false, '7');

                // 立即用CSS覆盖
                setTimeout(() => {
                    const fontElements = this.editor.querySelectorAll('font[size="7"]');
                    fontElements.forEach(el => {
                        const span = document.createElement('span');
                        span.style.fontSize = styleValue;
                        span.innerHTML = el.innerHTML;
                        el.parentNode.replaceChild(span, el);
                    });
                }, 10);

            } else if (styleProperty === 'fontFamily') {
                // 使用execCommand设置字体
                document.execCommand('fontName', false, styleValue);

            } else {
                // 其他样式属性的通用处理
                const contents = range.extractContents();
                const wrapper = document.createElement('span');
                wrapper.style[styleProperty] = styleValue;
                wrapper.appendChild(contents);
                range.insertNode(wrapper);

                // 重新选择包装后的内容
                const newRange = document.createRange();
                newRange.selectNodeContents(wrapper);
                selection.removeAllRanges();
                selection.addRange(newRange);
            }

        } catch (e) {
            console.warn('应用样式失败:', e);
            // 最后的降级处理
            if (styleProperty === 'fontFamily') {
                this.execCommand('fontName', styleValue);
            } else if (styleProperty === 'fontSize') {
                this.execCommand('fontSize', '3'); // 中等大小
            }
        }
    }

    // 改进的字体和大小检测
    updateCurrentStyles() {
        const selection = window.getSelection();
        if (selection.rangeCount > 0) {
            const range = selection.getRangeAt(0);
            let element = range.commonAncestorContainer;

            // 如果是文本节点，获取其父元素
            if (element.nodeType === Node.TEXT_NODE) {
                element = element.parentElement;
            }

            // 获取计算后的样式
            if (element && element.nodeType === Node.ELEMENT_NODE) {
                const computedStyle = window.getComputedStyle(element);

                // 更新字体显示
                const fontFamily = computedStyle.fontFamily;
                if (fontFamily) {
                    // 简化字体名称显示
                    let displayFont = fontFamily.split(',')[0].replace(/['"]/g, '').trim();
                    const fontMap = {
                        'Microsoft YaHei': '微软雅黑',
                        'SimSun': '宋体',
                        'SimHei': '黑体',
                        'KaiTi': '楷体'
                    };
                    displayFont = fontMap[displayFont] || displayFont;

                    const fontDisplay = this.container.querySelector('.font-display');
                    if (fontDisplay) {
                        fontDisplay.textContent = displayFont;
                    }
                }

                // 更新字体大小显示
                const fontSize = computedStyle.fontSize;
                if (fontSize) {
                    const sizeValue = parseInt(fontSize);
                    const sizeDisplay = this.container.querySelector('.size-display');
                    if (sizeDisplay) {
                        sizeDisplay.textContent = sizeValue;
                    }
                }
            }
        }
    }

    // 自动格式化标题
    autoFormatTitles() {
        // 防止在格式化过程中触发递归
        if (this.isFormatting) return;
        this.isFormatting = true;

        try {
            console.log('开始自动格式化检查...');

            // 获取当前光标位置
            const selection = window.getSelection();
            if (selection.rangeCount === 0) return;

            const range = selection.getRangeAt(0);
            const currentNode = range.startContainer;

            // 找到当前行的文本内容
            let currentLine = '';
            let targetElement = null;

            if (currentNode.nodeType === Node.TEXT_NODE) {
                targetElement = currentNode.parentNode;
                currentLine = currentNode.textContent.trim();
            } else {
                targetElement = currentNode;
                currentLine = currentNode.textContent.trim();
            }

            console.log('当前行内容:', currentLine);

            // 检查是否是标题格式
            const majorTitleRegex = /^([一二三四五六七八九十]+)、(.+)$/;
            const minorTitleRegex = /^(\d+\.\d+)\s+(.+)$/;
            const colonTitleRegex = /^(\d+):\s*(.+)$/;

            const majorMatch = currentLine.match(majorTitleRegex);
            const minorMatch = currentLine.match(minorTitleRegex);
            const colonMatch = currentLine.match(colonTitleRegex);

            if (majorMatch || minorMatch || colonMatch) {
                console.log('检测到标题格式:', currentLine);

                // 检查是否已经是粗体
                if (targetElement.tagName === 'STRONG' || targetElement.tagName === 'B') {
                    console.log('已经是粗体，跳过格式化');
                    return;
                }

                // 检查父元素是否是粗体
                let parent = targetElement.parentNode;
                while (parent && parent !== this.editor) {
                    if (parent.tagName === 'STRONG' || parent.tagName === 'B') {
                        console.log('父元素已经是粗体，跳过格式化');
                        return;
                    }
                    parent = parent.parentNode;
                }

                // 应用粗体格式
                this.applyBoldToCurrentLine(targetElement, currentLine);
            }

        } catch (error) {
            console.warn('自动格式化标题时出错:', error);
        } finally {
            this.isFormatting = false;
        }
    }

    // 应用粗体到当前行
    applyBoldToCurrentLine(element, text) {
        try {
            console.log('应用粗体格式到:', text);

            // 保存当前选择
            const selection = window.getSelection();
            const range = selection.getRangeAt(0);

            // 创建strong元素
            const strongElement = document.createElement('strong');
            strongElement.textContent = text;

            // 如果元素是段落或div
            if (element.tagName === 'P' || element.tagName === 'DIV') {
                element.innerHTML = '';
                element.appendChild(strongElement);
            } else if (element.nodeType === Node.TEXT_NODE) {
                // 如果是文本节点，替换父元素的内容
                const parent = element.parentNode;
                if (parent.tagName === 'P' || parent.tagName === 'DIV') {
                    parent.innerHTML = '';
                    parent.appendChild(strongElement);
                } else {
                    parent.replaceChild(strongElement, element);
                }
            } else {
                // 其他情况，直接替换内容
                element.innerHTML = '';
                element.appendChild(strongElement);
            }

            // 将光标移到strong元素的末尾
            const newRange = document.createRange();
            newRange.selectNodeContents(strongElement);
            newRange.collapse(false);
            selection.removeAllRanges();
            selection.addRange(newRange);

            console.log('粗体格式应用成功');

            // 触发input事件以同步隐藏字段
            const inputEvent = new Event('input', { bubbles: true });
            this.editor.dispatchEvent(inputEvent);

        } catch (error) {
            console.warn('应用粗体格式失败:', error);
        }
    }

    // 获取文本偏移量
    getTextOffset(node, offset) {
        let textOffset = 0;
        const walker = document.createTreeWalker(
            this.editor,
            NodeFilter.SHOW_TEXT,
            null,
            false
        );

        let currentNode;
        while (currentNode = walker.nextNode()) {
            if (currentNode === node) {
                return textOffset + offset;
            }
            textOffset += currentNode.textContent.length;
        }

        return textOffset;
    }

    // 恢复光标位置
    restoreCursorPosition(targetOffset) {
        const walker = document.createTreeWalker(
            this.editor,
            NodeFilter.SHOW_TEXT,
            null,
            false
        );

        let currentOffset = 0;
        let currentNode;

        while (currentNode = walker.nextNode()) {
            const nodeLength = currentNode.textContent.length;

            if (currentOffset + nodeLength >= targetOffset) {
                const range = document.createRange();
                const selection = window.getSelection();

                const offsetInNode = targetOffset - currentOffset;
                range.setStart(currentNode, Math.min(offsetInNode, nodeLength));
                range.collapse(true);

                selection.removeAllRanges();
                selection.addRange(range);
                return;
            }

            currentOffset += nodeLength;
        }

        // 如果无法精确恢复，将光标移到末尾
        const range = document.createRange();
        const selection = window.getSelection();
        range.selectNodeContents(this.editor);
        range.collapse(false);
        selection.removeAllRanges();
        selection.addRange(range);
    }

    // 格式化整个文档中的所有标题
    formatAllTitles() {
        if (this.isFormatting) return;
        this.isFormatting = true;

        try {
            console.log('开始格式化整个文档的标题...');

            const content = this.editor.innerHTML;
            let newContent = content;

            // 精确匹配标题格式，只加粗标题部分，不包括后续说明内容
            // 根据用户要求：只对"一、二、三"和"1.1、2.1"这样的标题进行加粗
            // 支持前导空格，无论标题在什么位置都能被识别

            console.log('开始格式化标题，支持前导空格...');

            // 使用统一的方法处理所有情况
            // 1. 处理大标题格式：一、二、三、等（支持前导空格）
            newContent = newContent.replace(/(\s*)([一二三四五六七八九十]+、[^\r\n]*?)(?=\s*(?:\r?\n|<br>|<\/p>|<\/div>|$))/gi, (match, spaces, title) => {
                // 检查是否已经被加粗
                if (match.includes('<strong>') || match.includes('<b>')) {
                    return match;
                }
                console.log('找到大标题:', title);
                return `${spaces}<strong>${title}</strong>`;
            });

            // 2. 处理小标题格式：1.1、2.1、3.2等（有空格的情况，支持前导空格）
            newContent = newContent.replace(/(\s*)(\d+\.\d+\s+[^\r\n]*?)(?=\s*(?:\r?\n|<br>|<\/p>|<\/div>|$))/gi, (match, spaces, title) => {
                // 检查是否已经被加粗
                if (match.includes('<strong>') || match.includes('<b>')) {
                    return match;
                }
                console.log('找到小标题（有空格）:', title);
                return `${spaces}<strong>${title}</strong>`;
            });

            // 3. 处理小标题格式：1.1、2.1、3.2等（没有空格的情况，支持前导空格）
            newContent = newContent.replace(/(\s*)(\d+\.\d+[^\s\r\n<][^\r\n<]*?)(?=\s*(?:\r?\n|<br>|<\/p>|<\/div>|$))/gi, (match, spaces, title) => {
                // 检查是否已经被加粗
                if (match.includes('<strong>') || match.includes('<b>')) {
                    return match;
                }
                console.log('找到小标题（无空格）:', title);
                return `${spaces}<strong>${title}</strong>`;
            });





            // 如果内容发生变化，更新编辑器
            if (newContent !== content) {
                console.log('检测到标题，正在应用格式化...');

                // 保存光标位置
                const selection = window.getSelection();
                let cursorOffset = 0;

                if (selection.rangeCount > 0) {
                    const range = selection.getRangeAt(0);
                    cursorOffset = this.getTextOffset(range.startContainer, range.startOffset);
                }

                // 更新内容
                this.editor.innerHTML = newContent;

                // 恢复光标位置
                this.restoreCursorPosition(cursorOffset);

                console.log('标题格式化完成');

                // 触发input事件以同步隐藏字段
                const inputEvent = new Event('input', { bubbles: true });
                this.editor.dispatchEvent(inputEvent);
            } else {
                console.log('未检测到需要格式化的标题');
            }

        } catch (error) {
            console.warn('格式化整个文档标题时出错:', error);
        } finally {
            this.isFormatting = false;
        }
    }
}
