/**
 * 测试优化后的安装流程
 * 验证下载重试次数和DMG自动安装功能
 */

const path = require('path');
const fs = require('fs');

async function testInstallOptimization() {
    console.log('🧪 开始测试优化后的安装流程...\n');

    console.log('📝 优化内容：');
    console.log('   1. 下载重试次数从4次改为1次');
    console.log('   2. 优化DMG自动安装流程');
    console.log('   3. 增强状态反馈信息');
    console.log('   4. 改进错误处理机制\n');
    
    // 测试1: 验证重试次数配置
    console.log('📋 测试1: 验证重试次数配置');
    console.log('=' .repeat(50));
    
    try {
        // 检查代码中的重试次数配置
        const updateManagerCode = fs.readFileSync('./src/update-manager.js', 'utf8');
        
        // 检查startUpdate方法中的maxRetries
        const startUpdateMatch = updateManagerCode.match(/async startUpdate.*?const maxRetries = (\d+)/s);
        if (startUpdateMatch) {
            const maxRetries = parseInt(startUpdateMatch[1]);
            console.log(`✅ startUpdate方法重试次数: ${maxRetries} (期望: 0)`);
            if (maxRetries === 0) {
                console.log('✅ 主更新重试次数配置正确');
            } else {
                console.log('❌ 主更新重试次数配置错误');
            }
        }
        
        // 检查downloadFileWithRetry方法中的默认重试次数
        const downloadRetryMatch = updateManagerCode.match(/async downloadFileWithRetry.*?maxRetries = (\d+)/s);
        if (downloadRetryMatch) {
            const maxRetries = parseInt(downloadRetryMatch[1]);
            console.log(`✅ downloadFileWithRetry方法默认重试次数: ${maxRetries} (期望: 0)`);
            if (maxRetries === 0) {
                console.log('✅ 下载重试次数配置正确');
            } else {
                console.log('❌ 下载重试次数配置错误');
            }
        }
        
        // 检查调用downloadFileWithRetry时的参数
        const callMatch = updateManagerCode.match(/await this\.downloadFileWithRetry\(downloadUrl, filePath, (\d+)\)/);
        if (callMatch) {
            const retryParam = parseInt(callMatch[1]);
            console.log(`✅ downloadFileWithRetry调用参数: ${retryParam} (期望: 0)`);
            if (retryParam === 0) {
                console.log('✅ 下载调用参数配置正确');
            } else {
                console.log('❌ 下载调用参数配置错误');
            }
        }
        
    } catch (error) {
        console.log('❌ 检查重试次数配置失败:', error.message);
    }
    
    console.log('\n📋 测试2: 验证状态反馈优化');
    console.log('=' .repeat(50));
    
    try {
        const updateManagerCode = fs.readFileSync('./src/update-manager.js', 'utf8');
        
        // 检查新增的状态反馈
        const statusMessages = [
            '下载完成，正在验证文件...',
            '文件验证通过，正在安装...',
            '正在准备安装DMG文件...',
            '正在挂载DMG文件...',
            'DMG挂载成功，正在复制应用...',
            '正在准备复制应用到Applications文件夹...',
            '正在删除旧版本应用...',
            '应用复制完成，正在验证安装...',
            '安装成功！正在设置权限...',
            '安装完成！3秒后自动重启应用...'
        ];
        
        let foundMessages = 0;
        for (const message of statusMessages) {
            if (updateManagerCode.includes(message)) {
                foundMessages++;
                console.log(`✅ 找到状态消息: "${message}"`);
            } else {
                console.log(`❌ 缺少状态消息: "${message}"`);
            }
        }
        
        console.log(`\n📊 状态消息检查结果: ${foundMessages}/${statusMessages.length} 个消息已添加`);
        
        if (foundMessages >= statusMessages.length * 0.8) {
            console.log('✅ 状态反馈优化基本完成');
        } else {
            console.log('⚠️ 状态反馈优化可能不完整');
        }
        
    } catch (error) {
        console.log('❌ 检查状态反馈优化失败:', error.message);
    }
    
    console.log('\n📋 测试3: 验证DMG安装流程');
    console.log('=' .repeat(50));
    
    try {
        const updateManagerCode = fs.readFileSync('./src/update-manager.js', 'utf8');
        
        // 检查DMG安装相关功能
        const dmgFeatures = [
            'installMacUpdate',
            'parseMountPoint', 
            'copyMacApp',
            'performCopy',
            'setAppPermissions',
            'restartApplication'
        ];
        
        let foundFeatures = 0;
        for (const feature of dmgFeatures) {
            if (updateManagerCode.includes(feature)) {
                foundFeatures++;
                console.log(`✅ 找到DMG安装功能: ${feature}`);
            } else {
                console.log(`❌ 缺少DMG安装功能: ${feature}`);
            }
        }
        
        console.log(`\n📊 DMG安装功能检查结果: ${foundFeatures}/${dmgFeatures.length} 个功能已实现`);
        
        if (foundFeatures === dmgFeatures.length) {
            console.log('✅ DMG自动安装功能完整');
        } else {
            console.log('⚠️ DMG自动安装功能可能不完整');
        }
        
    } catch (error) {
        console.log('❌ 检查DMG安装流程失败:', error.message);
    }
    
    console.log('\n📋 测试4: 验证错误处理优化');
    console.log('=' .repeat(50));
    
    try {
        const updateManagerCode = fs.readFileSync('./src/update-manager.js', 'utf8');
        
        // 检查错误处理改进
        const errorHandling = [
            '安装失败:',
            'update-status',
            'DMG文件大小:',
            '应用安装验证成功'
        ];
        
        let foundHandling = 0;
        for (const handling of errorHandling) {
            if (updateManagerCode.includes(handling)) {
                foundHandling++;
                console.log(`✅ 找到错误处理: ${handling}`);
            } else {
                console.log(`❌ 缺少错误处理: ${handling}`);
            }
        }
        
        console.log(`\n📊 错误处理检查结果: ${foundHandling}/${errorHandling.length} 个处理已优化`);
        
        if (foundHandling >= errorHandling.length * 0.75) {
            console.log('✅ 错误处理优化基本完成');
        } else {
            console.log('⚠️ 错误处理优化可能不完整');
        }
        
    } catch (error) {
        console.log('❌ 检查错误处理优化失败:', error.message);
    }
    
    console.log('\n🎉 安装流程优化测试完成!');
    console.log('\n💡 优化总结:');
    console.log('   ✅ 减少了不必要的重试，提高效率');
    console.log('   ✅ 增强了状态反馈，用户体验更好');
    console.log('   ✅ 完善了DMG自动安装流程');
    console.log('   ✅ 改进了错误处理机制');
    console.log('\n📝 预期效果:');
    console.log('   - 下载失败时不会重试4次，避免长时间等待');
    console.log('   - DMG下载成功后会自动挂载、复制、安装');
    console.log('   - 用户可以看到详细的安装进度');
    console.log('   - 安装完成后自动重启应用');
}

// 运行测试
if (require.main === module) {
    testInstallOptimization().catch(console.error);
}

module.exports = {
    testInstallOptimization
};
