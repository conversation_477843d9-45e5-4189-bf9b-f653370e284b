#!/usr/bin/env node

/**
 * 协议弹窗优化版本构建脚本
 * 构建包含协议弹窗优化的新版本DMG软件包
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始构建协议弹窗优化版本...\n');

// 检查当前目录
const currentDir = process.cwd();
console.log('📁 当前目录:', currentDir);

// 检查必要文件是否存在
const requiredFiles = [
    'package.json',
    'src/main.js',
    'src/preload.js',
    'src/agreement-manager.js',
    'src/agreement-preload.js',
    'src/renderer/login.html',
    'src/renderer/agreement.html'
];

console.log('🔍 检查必要文件...');
for (const file of requiredFiles) {
    if (!fs.existsSync(file)) {
        console.error(`❌ 缺少必要文件: ${file}`);
        process.exit(1);
    }
    console.log(`✅ ${file}`);
}

// 检查优化内容
console.log('\n🔧 验证协议弹窗优化内容...');

// 检查login.html中的独立窗口调用
const loginHtml = fs.readFileSync('src/renderer/login.html', 'utf8');
if (loginHtml.includes('openAgreementWindow') && loginHtml.includes('使用独立窗口显示协议')) {
    console.log('✅ login.html 已改为使用独立窗口显示协议');
} else {
    console.error('❌ login.html 独立窗口调用未找到');
    process.exit(1);
}

// 检查独立窗口API调用
if (loginHtml.includes('getPublishedAgreements') && loginHtml.includes('openAgreementWindow')) {
    console.log('✅ login.html 独立窗口API调用已添加');
} else {
    console.error('❌ login.html 独立窗口API调用未找到');
    process.exit(1);
}

// 检查preload.js中的openExternal API
const preloadJs = fs.readFileSync('src/preload.js', 'utf8');
if (preloadJs.includes('openExternal:')) {
    console.log('✅ preload.js openExternal API已添加');
} else {
    console.error('❌ preload.js openExternal API未找到');
    process.exit(1);
}

// 检查main.js中的open-external处理
const mainJs = fs.readFileSync('src/main.js', 'utf8');
if (mainJs.includes('open-external')) {
    console.log('✅ main.js 外部链接打开处理已添加');
} else {
    console.error('❌ main.js 外部链接打开处理未找到');
    process.exit(1);
}

console.log('\n📦 开始构建过程...');

try {
    // 清理之前的构建
    console.log('🧹 清理之前的构建文件...');
    if (fs.existsSync('dist')) {
        execSync('npm run clean', { stdio: 'inherit' });
    }

    // 安装依赖（如果需要）
    console.log('📥 检查依赖...');
    if (!fs.existsSync('node_modules')) {
        console.log('安装依赖...');
        execSync('npm install', { stdio: 'inherit' });
    }

    // 构建DMG
    console.log('🔨 构建DMG软件包...');
    execSync('npm run build:dmg', { stdio: 'inherit' });

    // 检查构建结果
    const distDir = 'dist';
    if (fs.existsSync(distDir)) {
        const files = fs.readdirSync(distDir);
        const dmgFiles = files.filter(file => file.endsWith('.dmg'));
        
        if (dmgFiles.length > 0) {
            console.log('\n✅ 构建成功！');
            console.log('📦 生成的DMG文件:');
            dmgFiles.forEach(file => {
                const filePath = path.join(distDir, file);
                const stats = fs.statSync(filePath);
                const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
                console.log(`   📁 ${file} (${sizeInMB} MB)`);
            });
            
            // 重命名DMG文件以标识修复版本
            const originalDmg = dmgFiles[0];
            const optimizedDmg = originalDmg.replace('.dmg', '-协议功能修复版.dmg');
            const originalPath = path.join(distDir, originalDmg);
            const optimizedPath = path.join(distDir, optimizedDmg);
            
            fs.renameSync(originalPath, optimizedPath);
            console.log(`\n🏷️  已重命名为: ${optimizedDmg}`);
            
            console.log('\n🎉 协议弹窗优化版本构建完成！');
            console.log('\n📋 优化内容总结:');
            console.log('   • 协议显示方式: 页面内弹窗 → 独立窗口 (900x700)');
            console.log('   • 关闭按钮: 独立窗口可正常关闭');
            console.log('   • 链接处理: 在独立窗口中打开，不在当前窗口跳转');
            console.log('   • 用户体验: 协议在软件窗口外显示，不影响卡密验证流程');
            console.log('   • 错误修复: 解决IPC处理程序重复注册问题');
            console.log('   • 数据库修复: 修复协议表结构，确保发布功能正常');
            console.log('   • API修复: 后台发布协议后APP能立即获取显示');
            
            console.log('\n📍 DMG文件位置:');
            console.log(`   ${path.resolve(optimizedPath)}`);
            
        } else {
            console.error('❌ 未找到生成的DMG文件');
            process.exit(1);
        }
    } else {
        console.error('❌ 构建目录不存在');
        process.exit(1);
    }

} catch (error) {
    console.error('❌ 构建失败:', error.message);
    process.exit(1);
}

console.log('\n🚀 构建脚本执行完成！');
