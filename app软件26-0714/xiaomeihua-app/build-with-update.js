/**
 * 包含更新功能的打包脚本
 * 确保所有新功能都被正确打包
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始打包小梅花AI智能客服 v2.0.0 (包含自动更新功能)');

// 检查必要文件是否存在
const requiredFiles = [
    'src/main.js',
    'src/update-manager.js',
    'src/app-settings-api.js',
    'src/popup-manager.js',
    'src/agreement-manager.js',
    'package.json'
];

console.log('🔍 检查必要文件...');
for (const file of requiredFiles) {
    if (!fs.existsSync(file)) {
        console.error(`❌ 缺少必要文件: ${file}`);
        process.exit(1);
    }
    console.log(`✅ ${file}`);
}

// 清理之前的构建
console.log('🧹 清理之前的构建...');
try {
    execSync('npm run clean', { stdio: 'inherit' });
} catch (error) {
    console.warn('⚠️ 清理失败，继续构建...');
}

// 检查依赖
console.log('📦 检查依赖...');
try {
    execSync('npm install', { stdio: 'inherit' });
    console.log('✅ 依赖检查完成');
} catch (error) {
    console.error('❌ 依赖安装失败:', error.message);
    process.exit(1);
}

// 获取平台参数
const platform = process.argv[2] || 'all';

console.log(`🔧 开始构建平台: ${platform}`);

try {
    switch (platform.toLowerCase()) {
        case 'windows':
        case 'win':
            console.log('🪟 构建Windows版本...');
            execSync('npm run build:win', { stdio: 'inherit' });
            break;
            
        case 'macos':
        case 'mac':
            console.log('🍎 构建macOS版本...');
            execSync('npm run build:mac', { stdio: 'inherit' });
            break;
            
        case 'dmg':
            console.log('💿 构建DMG版本...');
            execSync('npm run build:dmg', { stdio: 'inherit' });
            break;
            
        case 'all':
        default:
            console.log('🌍 构建所有平台版本...');
            execSync('npm run build:all', { stdio: 'inherit' });
            break;
    }
    
    console.log('✅ 构建完成！');
    
    // 显示构建结果
    console.log('\n📋 构建结果:');
    const distDir = path.join(__dirname, 'dist');
    if (fs.existsSync(distDir)) {
        const files = fs.readdirSync(distDir);
        files.forEach(file => {
            const filePath = path.join(distDir, file);
            const stats = fs.statSync(filePath);
            if (stats.isFile()) {
                const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
                console.log(`  📄 ${file} (${sizeInMB} MB)`);
            }
        });
    }
    
    console.log('\n🎉 打包完成！新版本包含以下功能:');
    console.log('  ✅ 自动更新检查');
    console.log('  ✅ 强制更新机制');
    console.log('  ✅ 在线下载安装');
    console.log('  ✅ 无感更新体验');
    console.log('  ✅ 协议弹窗管理');
    console.log('  ✅ 多店铺支持');
    console.log('  ✅ Cookie管理优化');
    
} catch (error) {
    console.error('❌ 构建失败:', error.message);
    process.exit(1);
}

// 创建构建报告
const buildReport = {
    version: '2.0.0',
    buildTime: new Date().toISOString(),
    platform: platform,
    features: [
        '自动更新检查',
        '强制更新机制', 
        '在线下载安装',
        '无感更新体验',
        '协议弹窗管理',
        '多店铺支持',
        'Cookie管理优化'
    ],
    files: []
};

// 记录构建的文件
const distDir = path.join(__dirname, 'dist');
if (fs.existsSync(distDir)) {
    const files = fs.readdirSync(distDir);
    files.forEach(file => {
        const filePath = path.join(distDir, file);
        const stats = fs.statSync(filePath);
        if (stats.isFile()) {
            buildReport.files.push({
                name: file,
                size: stats.size,
                sizeInMB: (stats.size / (1024 * 1024)).toFixed(2)
            });
        }
    });
}

// 保存构建报告
fs.writeFileSync(
    path.join(__dirname, 'build-report.json'),
    JSON.stringify(buildReport, null, 2)
);

console.log('\n📊 构建报告已保存到 build-report.json');
console.log('\n🚀 可以开始测试更新功能了！');
console.log('💡 提示: 在后台创建版本号为 3.0.0 的测试版本来测试更新功能');
