const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('准备安装程序资源文件...');

// 路径定义
const sourceIcon = path.join(__dirname, '../../小梅花ai图标logo.png');
const buildDir = path.join(__dirname, '.');
const iconIco = path.join(buildDir, 'icon.ico');
const iconBmp = path.join(buildDir, 'icon.bmp');
const iconPng = path.join(buildDir, 'icon.png');

// 检查源图标是否存在
if (!fs.existsSync(sourceIcon)) {
  console.error(`✗ 源图标不存在: ${sourceIcon}`);
  process.exit(1);
}

console.log(`✓ 找到源图标: ${sourceIcon}`);

// 确保build目录存在
if (!fs.existsSync(buildDir)) {
  fs.mkdirSync(buildDir, { recursive: true });
}

try {
  // 检查ImageMagick是否可用
  execSync('magick -version', { stdio: 'ignore' });
  console.log('✓ ImageMagick可用');
  
  // 生成不同格式的图标
  console.log('生成安装程序图标文件...');
  
  // 生成ICO文件 (多尺寸)
  console.log('- 生成icon.ico...');
  execSync(`magick "${sourceIcon}" -resize 256x256 -define icon:auto-resize=256,128,64,48,32,16 "${iconIco}"`);
  
  // 生成BMP文件 (用于安装界面)
  console.log('- 生成icon.bmp...');
  execSync(`magick "${sourceIcon}" -resize 164x314 -background white -gravity center -extent 164x314 "${iconBmp}"`);
  
  // 复制PNG文件
  console.log('- 复制icon.png...');
  fs.copyFileSync(sourceIcon, iconPng);
  
  console.log('✓ 所有图标文件生成完成');
  
} catch (error) {
  console.log('ImageMagick不可用，尝试使用备用方法...');
  
  try {
    // 使用convert命令 (旧版ImageMagick)
    execSync('convert -version', { stdio: 'ignore' });
    console.log('✓ Convert命令可用');
    
    // 生成ICO文件
    console.log('- 生成icon.ico...');
    const tempDir = path.join(buildDir, 'temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir);
    }
    
    // 生成多个尺寸的PNG
    const sizes = [16, 24, 32, 48, 64, 128, 256];
    sizes.forEach(size => {
      execSync(`convert "${sourceIcon}" -resize ${size}x${size} "${path.join(tempDir, `icon-${size}.png`)}"`);
    });
    
    // 合并为ICO
    execSync(`convert "${path.join(tempDir, 'icon-*.png')}" "${iconIco}"`);
    
    // 生成BMP文件
    console.log('- 生成icon.bmp...');
    execSync(`convert "${sourceIcon}" -resize 164x314 -background white -gravity center -extent 164x314 "${iconBmp}"`);
    
    // 复制PNG文件
    console.log('- 复制icon.png...');
    fs.copyFileSync(sourceIcon, iconPng);
    
    // 清理临时文件
    sizes.forEach(size => {
      const tempFile = path.join(tempDir, `icon-${size}.png`);
      if (fs.existsSync(tempFile)) {
        fs.unlinkSync(tempFile);
      }
    });
    fs.rmdirSync(tempDir);
    
    console.log('✓ 所有图标文件生成完成');
    
  } catch (convertError) {
    console.log('Convert命令也不可用，使用简单复制...');
    
    // 简单复制PNG文件
    fs.copyFileSync(sourceIcon, iconPng);
    
    // 尝试创建简单的ICO文件（如果有其他工具）
    console.log('⚠ 无法生成ICO和BMP文件，仅复制PNG文件');
    console.log('建议安装ImageMagick以获得完整功能');
  }
}

// 验证生成的文件
console.log('\n验证生成的文件:');
[iconIco, iconBmp, iconPng].forEach(file => {
  if (fs.existsSync(file)) {
    const stats = fs.statSync(file);
    console.log(`✓ ${path.basename(file)}: ${Math.round(stats.size / 1024)}KB`);
  } else {
    console.log(`✗ ${path.basename(file)}: 未生成`);
  }
});

console.log('\n安装程序资源准备完成!');
