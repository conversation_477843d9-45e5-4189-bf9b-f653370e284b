; 小梅花AI智能客服自定义安装界面
; 支持Win10/Win11，自动获取管理员权限，默认安装到D盘

; 设置默认安装目录为D盘
!macro customInit
  ; 检查D盘是否存在，设置默认安装路径
  ${If} ${FileExists} "D:\"
    StrCpy $INSTDIR "D:\小梅花AI智能客服"
  ${Else}
    StrCpy $INSTDIR "C:\Program Files\小梅花AI智能客服"
  ${EndIf}
!macroend

; 安装后的系统设置
!macro customInstallMode
  ; 设置为所有用户安装
  SetShellVarContext all
!macroend

; 安装完成后的设置
!macro customFinish
  ; 设置目录权限
  nsExec::ExecToLog 'icacls "$INSTDIR" /grant "Everyone:(OI)(CI)F" /T'

  ; 创建用户数据目录并设置权限
  CreateDirectory "$APPDATA\小梅花AI智能客服"
  nsExec::ExecToLog 'icacls "$APPDATA\小梅花AI智能客服" /grant "Everyone:(OI)(CI)F" /T'

  CreateDirectory "$LOCALAPPDATA\小梅花AI智能客服"
  nsExec::ExecToLog 'icacls "$LOCALAPPDATA\小梅花AI智能客服" /grant "Everyone:(OI)(CI)F" /T'

  ; 创建防火墙例外
  nsExec::ExecToLog 'netsh advfirewall firewall add rule name="小梅花AI智能客服" dir=in action=allow program="$INSTDIR\小梅花AI智能客服.exe"'
  nsExec::ExecToLog 'netsh advfirewall firewall add rule name="小梅花AI智能客服" dir=out action=allow program="$INSTDIR\小梅花AI智能客服.exe"'
!macroend

; 自定义界面文本
!macro customHeader
  !define MUI_WELCOMEPAGE_TITLE "欢迎使用小梅花AI智能客服安装向导"
  !define MUI_WELCOMEPAGE_TEXT "安装向导将引导您完成小梅花AI智能客服的安装过程。$\r$\n$\r$\n本软件支持Windows 10和Windows 11系统。$\r$\n安装程序将自动选择最佳安装路径（优先D盘）。$\r$\n$\r$\n点击 [下一步] 继续。"

  !define MUI_FINISHPAGE_TITLE "完成小梅花AI智能客服安装向导"
  !define MUI_FINISHPAGE_TEXT "小梅花AI智能客服已安装在您的计算机上。$\r$\n$\r$\n安装路径：$INSTDIR$\r$\n$\r$\n点击 [完成] 关闭此向导。"
  !define MUI_FINISHPAGE_RUN "$INSTDIR\小梅花AI智能客服.exe"
  !define MUI_FINISHPAGE_RUN_TEXT "立即运行小梅花AI智能客服"
!macroend
