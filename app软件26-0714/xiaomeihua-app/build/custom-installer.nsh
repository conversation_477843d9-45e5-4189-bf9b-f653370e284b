; 小梅花AI智能客服完全自定义安装界面
; 模仿HTML设计的现代化安装程序

!include "MUI2.nsh"
!include "nsDialogs.nsh"
!include "LogicLib.nsh"
!include "WinMessages.nsh"

; 常量定义
!define PRODUCT_NAME "小梅花AI智能客服"
!define PRODUCT_VERSION "1.0.0"
!define PRODUCT_PUBLISHER "小梅花AI科技"

; 窗口控件ID
!define IDC_WELCOME_LOGO 1200
!define IDC_WELCOME_TITLE 1201
!define IDC_WELCOME_DESC 1202
!define IDC_INSTALL_PATH 1203
!define IDC_BROWSE_BTN 1204
!define IDC_INSTALL_BTN 1205
!define IDC_PROGRESS_BAR 1206
!define IDC_PROGRESS_TEXT 1207
!define IDC_COMPLETE_ICON 1208
!define IDC_COMPLETE_TITLE 1209
!define IDC_COMPLETE_DESC 1210
!define IDC_LAUNCH_BTN 1211

; 页面变量
Var Dialog
Var WelcomeLogo
Var WelcomeTitle
Var WelcomeDesc
Var InstallPath
Var BrowseBtn
Var InstallBtn
Var ProgressBar
Var ProgressText
Var CompleteIcon
Var CompleteTitle
Var CompleteDesc
Var LaunchBtn
Var InstallationStep

; 自定义欢迎页面
Function CustomWelcomePage
  nsDialogs::Create 1018
  Pop $Dialog
  
  ${If} $Dialog == error
    Abort
  ${EndIf}
  
  ; 设置对话框背景为白色
  SetCtlColors $Dialog 0x333333 0xFFFFFF
  
  ; 创建Logo (80x80像素，居中)
  ${NSD_CreateBitmap} 210 30 80 80 ""
  Pop $WelcomeLogo
  ${NSD_SetImage} $WelcomeLogo "$EXEDIR\icon.bmp"
  
  ; 创建主标题 (20px字体，粗体)
  ${NSD_CreateLabel} 100 130 300 30 "安装准备"
  Pop $WelcomeTitle
  SetCtlColors $WelcomeTitle 0x1F2937 0xFFFFFF
  CreateFont $0 "Microsoft YaHei UI" 20 700
  SendMessage $WelcomeTitle ${WM_SETFONT} $0 0
  ${NSD_AddStyle} $WelcomeTitle ${SS_CENTER}
  
  ; 创建描述文本 (14px字体，普通)
  ${NSD_CreateLabel} 50 165 400 40 "请选择安装目录，开始安装小梅花AI智能客服系统"
  Pop $WelcomeDesc
  SetCtlColors $WelcomeDesc 0x6B7280 0xFFFFFF
  CreateFont $1 "Microsoft YaHei UI" 14 400
  SendMessage $WelcomeDesc ${WM_SETFONT} $1 0
  ${NSD_AddStyle} $WelcomeDesc ${SS_CENTER}
  
  ; 创建安装路径输入框
  ${NSD_CreateText} 75 220 270 25 "$INSTDIR"
  Pop $InstallPath
  SetCtlColors $InstallPath 0x333333 0xFFFFFF
  CreateFont $2 "Microsoft YaHei UI" 12 400
  SendMessage $InstallPath ${WM_SETFONT} $2 0
  
  ; 创建浏览按钮
  ${NSD_CreateButton} 355 220 70 25 "浏览..."
  Pop $BrowseBtn
  SetCtlColors $BrowseBtn 0x374151 0xF3F4F6
  CreateFont $3 "Microsoft YaHei UI" 12 400
  SendMessage $BrowseBtn ${WM_SETFONT} $3 0
  ${NSD_OnClick} $BrowseBtn BrowseForFolder
  
  ; 创建安装按钮 (现代化蓝色按钮)
  ${NSD_CreateButton} 125 270 250 40 "开始安装"
  Pop $InstallBtn
  SetCtlColors $InstallBtn 0xFFFFFF 0x3B82F6
  CreateFont $4 "Microsoft YaHei UI" 16 500
  SendMessage $InstallBtn ${WM_SETFONT} $4 0
  ${NSD_OnClick} $InstallBtn StartInstallation
  
  ; 设置当前步骤
  StrCpy $InstallationStep "welcome"
  
  nsDialogs::Show
FunctionEnd

; 浏览文件夹
Function BrowseForFolder
  nsDialogs::SelectFolderDialog "选择安装目录" "$INSTDIR"
  Pop $0
  ${If} $0 != error
    ${NSD_SetText} $InstallPath $0
    StrCpy $INSTDIR $0
  ${EndIf}
FunctionEnd

; 开始安装
Function StartInstallation
  ; 获取安装路径
  ${NSD_GetText} $InstallPath $INSTDIR
  
  ; 隐藏欢迎界面元素
  ShowWindow $WelcomeTitle ${SW_HIDE}
  ShowWindow $WelcomeDesc ${SW_HIDE}
  ShowWindow $InstallPath ${SW_HIDE}
  ShowWindow $BrowseBtn ${SW_HIDE}
  ShowWindow $InstallBtn ${SW_HIDE}
  
  ; 显示进度界面
  Call ShowProgressPage
  
  ; 执行实际安装
  Call DoInstallation
FunctionEnd

; 显示进度页面
Function ShowProgressPage
  ; 创建进度标题
  ${NSD_CreateLabel} 100 130 300 30 "正在安装..."
  Pop $WelcomeTitle
  SetCtlColors $WelcomeTitle 0x1F2937 0xFFFFFF
  CreateFont $0 "Microsoft YaHei UI" 20 700
  SendMessage $WelcomeTitle ${WM_SETFONT} $0 0
  ${NSD_AddStyle} $WelcomeTitle ${SS_CENTER}
  
  ; 创建进度描述
  ${NSD_CreateLabel} 50 165 400 40 "小梅花AI智能客服正在安装中，请稍候"
  Pop $WelcomeDesc
  SetCtlColors $WelcomeDesc 0x6B7280 0xFFFFFF
  CreateFont $1 "Microsoft YaHei UI" 14 400
  SendMessage $WelcomeDesc ${WM_SETFONT} $1 0
  ${NSD_AddStyle} $WelcomeDesc ${SS_CENTER}
  
  ; 创建进度条 (蓝色渐变)
  ${NSD_CreateProgressBar} 100 220 300 6 ""
  Pop $ProgressBar
  SendMessage $ProgressBar ${PBM_SETRANGE} 0 100
  
  ; 创建进度百分比文本
  ${NSD_CreateLabel} 225 240 50 25 "0%"
  Pop $ProgressText
  SetCtlColors $ProgressText 0x1F2937 0xFFFFFF
  CreateFont $2 "Microsoft YaHei UI" 18 500
  SendMessage $ProgressText ${WM_SETFONT} $2 0
  ${NSD_AddStyle} $ProgressText ${SS_CENTER}
  
  ; 设置当前步骤
  StrCpy $InstallationStep "progress"
FunctionEnd

; 执行安装
Function DoInstallation
  ; 模拟安装进度
  ${For} $R0 0 100
    SendMessage $ProgressBar ${PBM_SETPOS} $R0 0
    ${NSD_SetText} $ProgressText "$R0%"
    Sleep 30
  ${Next}
  
  ; 安装完成，显示完成页面
  Call ShowCompletePage
FunctionEnd

; 显示完成页面
Function ShowCompletePage
  ; 隐藏进度界面元素
  ShowWindow $WelcomeTitle ${SW_HIDE}
  ShowWindow $WelcomeDesc ${SW_HIDE}
  ShowWindow $ProgressBar ${SW_HIDE}
  ShowWindow $ProgressText ${SW_HIDE}
  
  ; 创建成功图标 (绿色圆形背景的对勾)
  ${NSD_CreateLabel} 225 120 50 50 "✓"
  Pop $CompleteIcon
  SetCtlColors $CompleteIcon 0xFFFFFF 0x10B981
  CreateFont $0 "Microsoft YaHei UI" 32 700
  SendMessage $CompleteIcon ${WM_SETFONT} $0 0
  ${NSD_AddStyle} $CompleteIcon ${SS_CENTER}
  
  ; 创建完成标题
  ${NSD_CreateLabel} 100 185 300 30 "安装完成"
  Pop $CompleteTitle
  SetCtlColors $CompleteTitle 0x1F2937 0xFFFFFF
  CreateFont $1 "Microsoft YaHei UI" 20 700
  SendMessage $CompleteTitle ${WM_SETFONT} $1 0
  ${NSD_AddStyle} $CompleteTitle ${SS_CENTER}
  
  ; 创建完成描述
  ${NSD_CreateLabel} 50 220 400 40 "小梅花AI智能客服已成功安装"
  Pop $CompleteDesc
  SetCtlColors $CompleteDesc 0x6B7280 0xFFFFFF
  CreateFont $2 "Microsoft YaHei UI" 14 400
  SendMessage $CompleteDesc ${WM_SETFONT} $2 0
  ${NSD_AddStyle} $CompleteDesc ${SS_CENTER}
  
  ; 创建启动按钮
  ${NSD_CreateButton} 125 270 250 40 "立即启动"
  Pop $LaunchBtn
  SetCtlColors $LaunchBtn 0xFFFFFF 0x3B82F6
  CreateFont $3 "Microsoft YaHei UI" 16 500
  SendMessage $LaunchBtn ${WM_SETFONT} $3 0
  ${NSD_OnClick} $LaunchBtn LaunchApplication
  
  ; 设置当前步骤
  StrCpy $InstallationStep "complete"
FunctionEnd

; 启动应用程序
Function LaunchApplication
  ; 启动应用
  Exec "$INSTDIR\${PRODUCT_NAME}.exe"
  ; 关闭安装程序
  Quit
FunctionEnd

; 页面离开函数
Function CustomWelcomePageLeave
  ; 清理资源
FunctionEnd
