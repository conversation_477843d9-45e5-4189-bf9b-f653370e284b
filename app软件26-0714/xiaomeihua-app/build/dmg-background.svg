<?xml version="1.0" encoding="UTF-8"?>
<svg width="650" height="400" viewBox="0 0 650 400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e9ecef;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ff6b9d;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ff8fab;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- 背景 -->
  <rect width="650" height="400" fill="url(#backgroundGradient)"/>

  <!-- 顶部装饰条 -->
  <rect x="0" y="0" width="650" height="4" fill="url(#accentGradient)"/>

  <!-- 标题区域 - 贴顶部 -->
  <rect x="0" y="4" width="650" height="60" rx="0" fill="white" stroke="#dee2e6" stroke-width="1"/>

  <!-- 修复已损坏文件区域的虚线矩形框 - 根据红色框位置 -->
  <rect x="110" y="160" width="250" height="180" rx="8" fill="none" stroke="#6c757d" stroke-width="1" stroke-dasharray="5,5" opacity="0.6"/>

  <!-- 小梅花客服应用的虚线矩形框 -->
  <rect x="140" y="430" width="140" height="120" rx="6" fill="none" stroke="#6c757d" stroke-width="1" stroke-dasharray="5,5" opacity="0.6"/>

  <!-- 箭头指示 - 调整到不被遮挡的位置 -->
  <path d="M 290 430 Q 380 410 470 430" stroke="#ff6b9d" stroke-width="2" fill="none" marker-end="url(#arrowhead)" opacity="0.9"/>

  <!-- 箭头标记 -->
  <defs>
    <marker id="arrowhead" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
      <polygon points="0 0, 8 3, 0 6" fill="#ff6b9d"/>
    </marker>
  </defs>

  <!-- 弧形文字 - 沿着箭头路径 -->
  <defs>
    <path id="textPath" d="M 290 430 Q 380 410 470 430"/>
  </defs>
  <text font-family="PingFang SC, Arial, sans-serif" font-size="12" fill="#6c757d" opacity="0.8">
    <textPath href="#textPath" startOffset="15%">将应用拖拽到Applications文件夹完成安装</textPath>
  </text>

  <!-- 标题文字 - 只保留欢迎语 -->
  <text x="325" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#495057">您好～欢迎使用小梅花AI智能客服</text>

  <!-- 右侧安装说明文本区域 - 向上移动 -->
  <rect x="320" y="80" width="300" height="140" rx="8" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>

  <!-- 安装说明标题 -->
  <text x="330" y="100" font-family="PingFang SC, Arial, sans-serif" font-size="14" font-weight="bold" fill="#495057">📋 安装说明</text>

  <!-- 步骤1 -->
  <text x="330" y="120" font-family="PingFang SC, Arial, sans-serif" font-size="12" fill="#495057">1: 先把软件拖入到Application安装</text>

  <!-- 步骤2 - 红色加粗 -->
  <text x="330" y="140" font-family="PingFang SC, Arial, sans-serif" font-size="12" font-weight="bold" fill="#dc3545">2: 安装后打开修复软件，输入电脑密码</text>
  <text x="330" y="155" font-family="PingFang SC, Arial, sans-serif" font-size="12" font-weight="bold" fill="#dc3545">   即可安装成功</text>

  <!-- 注意事项 -->
  <text x="330" y="180" font-family="PingFang SC, Arial, sans-serif" font-size="11" font-weight="bold" fill="#dc3545">⚠️ 注意</text>
  <text x="330" y="195" font-family="PingFang SC, Arial, sans-serif" font-size="11" fill="#6c757d">这是MAC电脑安全识别，文件会报错损坏</text>
  <text x="330" y="210" font-family="PingFang SC, Arial, sans-serif" font-size="11" fill="#6c757d">请放心使用</text>
</svg>