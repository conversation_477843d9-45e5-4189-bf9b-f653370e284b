const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('开始后处理构建文件...');

const distPath = path.join(__dirname, '..', 'dist');
const winUnpackedPath = path.join(distPath, 'win-unpacked');
const exePath = path.join(winUnpackedPath, '小梅花AI智能客服.exe');
const iconPath = path.join(__dirname, 'icon.ico');

// 检查文件是否存在
if (!fs.existsSync(exePath)) {
    console.log('exe文件不存在，跳过图标修复');
    return;
}

if (!fs.existsSync(iconPath)) {
    console.error('错误：找不到图标文件:', iconPath);
    return;
}

console.log('exe文件路径:', exePath);
console.log('图标文件路径:', iconPath);

// 复制图标文件到win-unpacked目录
const targetIconPath = path.join(winUnpackedPath, 'icon.ico');
fs.copyFileSync(iconPath, targetIconPath);
console.log('图标文件已复制到:', targetIconPath);

// 检查exe文件大小
const stats = fs.statSync(exePath);
console.log('exe文件大小:', (stats.size / 1024 / 1024).toFixed(2), 'MB');

console.log('后处理完成！');
console.log('');
console.log('重要提示：');
console.log('如果exe文件图标仍然显示为Electron默认图标，这是一个已知的electron-builder问题。');
console.log('解决方案：');
console.log('1. 在Windows系统上使用ResourceHacker工具手动替换exe文件的图标');
console.log('2. 或者使用rcedit工具：npm install -g rcedit');
console.log('3. 然后运行：rcedit "小梅花AI智能客服.exe" --set-icon "icon.ico"');
console.log('');
