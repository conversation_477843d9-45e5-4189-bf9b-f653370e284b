<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小梅花AI智能客服 - 安装说明</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header h1 {
            color: #ff6b9d;
            margin: 0;
        }
        .section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .step {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #ff6b9d;
        }
        .step-number {
            background: #ff6b9d;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: bold;
        }
        .feature {
            display: flex;
            align-items: center;
            margin: 10px 0;
        }
        .feature::before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            margin-right: 10px;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .warning::before {
            content: "⚠️ ";
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>小梅花AI智能客服</h1>
        <p>macOS安装指南</p>
    </div>

    <div class="section">
        <h2>📋 系统要求</h2>
        <div class="feature">macOS 10.15 (Catalina) 或更高版本</div>
        <div class="feature">Intel 或 Apple Silicon (M1/M2/M3) 处理器</div>
        <div class="feature">至少 200MB 可用磁盘空间</div>
        <div class="feature">网络连接（用于API通信和更新）</div>
    </div>

    <div class="section">
        <h2>🚀 安装步骤</h2>
        <div class="step">
            <div class="step-number">1</div>
            <div>双击打开下载的DMG文件</div>
        </div>
        <div class="step">
            <div class="step-number">2</div>
            <div>将应用程序图标拖拽到Applications文件夹</div>
        </div>
        <div class="step">
            <div class="step-number">3</div>
            <div>在Launchpad或Applications文件夹中找到并启动应用</div>
        </div>
        <div class="step">
            <div class="step-number">4</div>
            <div>首次运行时按照提示完成初始设置</div>
        </div>
    </div>

    <div class="section">
        <h2>🔒 安全设置</h2>
        <div class="warning">
            首次运行时，macOS可能显示安全警告。这是正常现象，请按以下步骤操作：
        </div>
        <div class="step">
            <div class="step-number">1</div>
            <div>打开"系统偏好设置" → "安全性与隐私"</div>
        </div>
        <div class="step">
            <div class="step-number">2</div>
            <div>在"通用"标签页中点击"仍要打开"</div>
        </div>
        <div class="step">
            <div class="step-number">3</div>
            <div>或在终端中运行：<code>sudo xattr -rd com.apple.quarantine /Applications/小梅花AI智能客服.app</code></div>
        </div>
    </div>

    <div class="section">
        <h2>✨ 主要功能</h2>
        <div class="feature">AI智能客服系统</div>
        <div class="feature">多店铺管理</div>
        <div class="feature">自动化脚本执行</div>
        <div class="feature">数据同步和备份</div>
        <div class="feature">自动更新功能</div>
        <div class="feature">原生macOS体验</div>
    </div>

    <div class="section">
        <h2>🔧 故障排除</h2>
        <h3>应用无法启动</h3>
        <ul>
            <li>确保系统版本符合要求</li>
            <li>检查是否有足够的磁盘空间</li>
            <li>尝试重新安装应用</li>
            <li>检查安全设置是否正确</li>
        </ul>
        
        <h3>权限问题</h3>
        <ul>
            <li>在"系统偏好设置" → "安全性与隐私" → "隐私"中授予必要权限</li>
            <li>网络访问权限用于API通信</li>
            <li>文件访问权限用于保存配置</li>
        </ul>
    </div>

    <div class="section">
        <h2>📞 技术支持</h2>
        <p>如遇问题，请联系技术支持团队获取帮助。</p>
        <p><strong>版本：</strong>1.0.0</p>
        <p><strong>构建时间：</strong>2025/7/30 09:30:12</p>
    </div>
</body>
</html>