const fs = require('fs');
const path = require('path');

// 这个脚本用于在构建后强制设置exe文件的图标
// 主要是为了解决electron-builder在某些情况下不能正确嵌入图标的问题

console.log('开始修复exe文件图标...');

const distPath = path.join(__dirname, '..', 'dist');
const winUnpackedPath = path.join(distPath, 'win-unpacked');
const exePath = path.join(winUnpackedPath, '小梅花AI智能客服.exe');
const iconPath = path.join(__dirname, 'icon.ico');

// 检查文件是否存在
if (!fs.existsSync(exePath)) {
    console.error('错误：找不到exe文件:', exePath);
    process.exit(1);
}

if (!fs.existsSync(iconPath)) {
    console.error('错误：找不到图标文件:', iconPath);
    process.exit(1);
}

console.log('exe文件路径:', exePath);
console.log('图标文件路径:', iconPath);

// 复制图标文件到win-unpacked目录，确保它在正确的位置
const targetIconPath = path.join(winUnpackedPath, 'icon.ico');
fs.copyFileSync(iconPath, targetIconPath);
console.log('图标文件已复制到:', targetIconPath);

console.log('图标修复完成！');
console.log('注意：如果exe文件图标仍然不正确，可能需要在Windows系统上使用ResourceHacker等工具手动嵌入图标。');
