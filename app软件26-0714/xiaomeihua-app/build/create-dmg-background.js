#!/usr/bin/env node

/**
 * 创建DMG背景图片脚本
 * 使用Canvas API生成PNG背景图片
 */

const fs = require('fs');
const path = require('path');

// 创建简单的HTML Canvas背景图片
function createDMGBackgroundHTML() {
    const html = `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>DMG Background Generator</title>
    <style>
        body { margin: 0; padding: 20px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; }
        canvas { border: 1px solid #ccc; }
        .controls { margin-top: 20px; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>DMG背景图片生成器</h1>
    <canvas id="dmgCanvas" width="650" height="400"></canvas>
    <div class="controls">
        <button onclick="generateBackground()">生成背景</button>
        <button onclick="downloadImage()">下载PNG</button>
    </div>

    <script>
        const canvas = document.getElementById('dmgCanvas');
        const ctx = canvas.getContext('2d');

        function generateBackground() {
            // 清除画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 设置背景渐变
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 顶部装饰条
            const accentGradient = ctx.createLinearGradient(0, 0, canvas.width, 0);
            accentGradient.addColorStop(0, '#ff6b9d');
            accentGradient.addColorStop(1, '#ff8fab');
            ctx.fillStyle = accentGradient;
            ctx.fillRect(0, 0, canvas.width, 4);
            
            // 标题区域
            ctx.fillStyle = 'white';
            ctx.strokeStyle = '#dee2e6';
            ctx.lineWidth = 1;
            roundRect(ctx, 20, 20, 610, 60, 10);
            ctx.fill();
            ctx.stroke();
            
            // 标题文字
            ctx.fillStyle = '#495057';
            ctx.font = 'bold 18px -apple-system, BlinkMacSystemFont, sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText('小梅花AI智能客服', 325, 45);
            
            ctx.font = '14px -apple-system, BlinkMacSystemFont, sans-serif';
            ctx.fillStyle = '#6c757d';
            ctx.fillText('将应用拖拽到Applications文件夹完成安装', 325, 65);
            
            // 修复文件位置指示
            ctx.fillStyle = '#fff3cd';
            ctx.strokeStyle = '#ffc107';
            ctx.lineWidth = 2;
            roundRect(ctx, 100, 90, 100, 60, 8);
            ctx.fill();
            ctx.stroke();
            
            ctx.fillStyle = '#856404';
            ctx.font = 'bold 11px -apple-system, BlinkMacSystemFont, sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText('修复文件', 150, 115);
            ctx.font = '9px -apple-system, BlinkMacSystemFont, sans-serif';
            ctx.fillText('安装后运行', 150, 130);
            
            // 应用图标位置指示
            ctx.strokeStyle = '#6c757d';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 5]);
            ctx.beginPath();
            ctx.arc(150, 280, 40, 0, 2 * Math.PI);
            ctx.stroke();
            ctx.setLineDash([]);
            
            ctx.fillStyle = '#6c757d';
            ctx.font = '12px -apple-system, BlinkMacSystemFont, sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText('拖拽应用', 150, 285);
            
            // Applications文件夹指示
            ctx.strokeStyle = '#6c757d';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 5]);
            roundRect(ctx, 400, 230, 100, 100, 10);
            ctx.stroke();
            ctx.setLineDash([]);
            
            ctx.fillStyle = '#6c757d';
            ctx.font = '12px -apple-system, BlinkMacSystemFont, sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText('Applications', 450, 285);
            
            // 右侧安装说明文本区域
            ctx.fillStyle = '#f8f9fa';
            ctx.strokeStyle = '#dee2e6';
            ctx.lineWidth = 1;
            roundRect(ctx, 320, 120, 300, 140, 8);
            ctx.fill();
            ctx.stroke();
            
            // 安装说明文字
            ctx.fillStyle = '#495057';
            ctx.font = 'bold 14px -apple-system, BlinkMacSystemFont, sans-serif';
            ctx.textAlign = 'left';
            ctx.fillText('📋 安装说明', 330, 140);
            
            ctx.font = '12px -apple-system, BlinkMacSystemFont, sans-serif';
            ctx.fillText('1: 先把软件拖入到Application安装', 330, 160);
            ctx.fillText('2: 安装后打开修复软件，输入电脑密码', 330, 180);
            ctx.fillText('   即可安装成功', 340, 195);
            
            ctx.font = 'bold 11px -apple-system, BlinkMacSystemFont, sans-serif';
            ctx.fillStyle = '#dc3545';
            ctx.fillText('⚠️ 注意：', 330, 220);
            
            ctx.font = '11px -apple-system, BlinkMacSystemFont, sans-serif';
            ctx.fillStyle = '#6c757d';
            ctx.fillText('这是MAC电脑安全识别，文件会报错损坏，', 330, 235);
            ctx.fillText('请放心使用', 330, 250);
            
            // 箭头指示
            drawArrow(ctx, 190, 280, 400, 280, '#ff6b9d', 3);
            
            // 底部信息
            ctx.fillStyle = '#adb5bd';
            ctx.font = '11px -apple-system, BlinkMacSystemFont, sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText('© 2025 小梅花AI科技 - 智能客服解决方案', 325, 370);
        }
        
        function roundRect(ctx, x, y, width, height, radius) {
            ctx.beginPath();
            ctx.moveTo(x + radius, y);
            ctx.lineTo(x + width - radius, y);
            ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
            ctx.lineTo(x + width, y + height - radius);
            ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
            ctx.lineTo(x + radius, y + height);
            ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
            ctx.lineTo(x, y + radius);
            ctx.quadraticCurveTo(x, y, x + radius, y);
            ctx.closePath();
        }
        
        function drawArrow(ctx, fromX, fromY, toX, toY, color, width) {
            ctx.strokeStyle = color;
            ctx.lineWidth = width;
            ctx.setLineDash([]);
            
            // 绘制弯曲箭头
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            const cpX = (fromX + toX) / 2;
            const cpY = fromY - 20;
            ctx.quadraticCurveTo(cpX, cpY, toX, toY);
            ctx.stroke();
            
            // 绘制箭头头部
            const angle = Math.atan2(toY - cpY, toX - cpX);
            const arrowLength = 10;
            
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - arrowLength * Math.cos(angle - Math.PI / 6), toY - arrowLength * Math.sin(angle - Math.PI / 6));
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - arrowLength * Math.cos(angle + Math.PI / 6), toY - arrowLength * Math.sin(angle + Math.PI / 6));
            ctx.stroke();
        }
        
        function downloadImage() {
            const link = document.createElement('a');
            link.download = 'dmg-background.png';
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // 自动生成背景
        generateBackground();
    </script>
</body>
</html>`;

    return html;
}

// 主函数
function main() {
    const buildDir = path.join(__dirname);
    
    // 创建HTML背景生成器
    const htmlContent = createDMGBackgroundHTML();
    const htmlPath = path.join(buildDir, 'dmg-background-generator.html');
    fs.writeFileSync(htmlPath, htmlContent, 'utf8');
    
    console.log('✅ 已生成DMG背景生成器:', htmlPath);
    console.log('');
    console.log('📝 使用说明:');
    console.log('1. 在浏览器中打开 dmg-background-generator.html');
    console.log('2. 点击"生成背景"按钮');
    console.log('3. 点击"下载PNG"按钮保存为 dmg-background.png');
    console.log('4. 将下载的文件放到 build/ 目录下');
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = { createDMGBackgroundHTML };
