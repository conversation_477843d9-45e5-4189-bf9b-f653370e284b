; 小梅花AI智能客服完全自定义安装界面
; 基于HTML设计的现代化安装界面

!include "MUI2.nsh"
!include "WinMessages.nsh"
!include "LogicLib.nsh"
!include "FileFunc.nsh"

; 常量定义
!define TEMP_DIR "$PLUGINSDIR"
!define CUSTOM_PAGE_INI "${TEMP_DIR}\custom_page.ini"

; 窗口控件ID
!define IDC_LOGO 1200
!define IDC_TITLE 1201
!define IDC_SUBTITLE 1202
!define IDC_INSTALL_PATH 1203
!define IDC_BROWSE_BTN 1204
!define IDC_INSTALL_BTN 1205
!define IDC_PROGRESS 1206
!define IDC_PROGRESS_TEXT 1207
!define IDC_COMPLETE_ICON 1208
!define IDC_COMPLETE_TEXT 1209
!define IDC_LAUNCH_BTN 1210

; 页面变量
Var Dialog
Var LogoControl
Var TitleControl
Var SubtitleControl
Var InstallPathControl
Var BrowseButton
Var InstallButton
Var ProgressControl
Var ProgressText
Var CompleteIcon
Var CompleteText
Var LaunchButton
Var CurrentStep

; 自定义页面函数
Function CustomWelcomePage
  ; 创建自定义对话框
  nsDialogs::Create 1018
  Pop $Dialog
  
  ${If} $Dialog == error
    Abort
  ${EndIf}
  
  ; 设置背景色为白色
  SetCtlColors $Dialog 0x333333 0xFFFFFF
  
  ; 创建Logo图片控件
  ${NSD_CreateBitmap} 160 20 80 80 ""
  Pop $LogoControl
  ${NSD_SetImage} $LogoControl "$EXEDIR\小梅花ai图标logo.png"
  
  ; 创建标题
  ${NSD_CreateLabel} 50 120 300 30 "安装准备"
  Pop $TitleControl
  SetCtlColors $TitleControl 0x1F2937 0xFFFFFF
  CreateFont $0 "Microsoft YaHei" 20 600
  SendMessage $TitleControl ${WM_SETFONT} $0 0
  
  ; 创建副标题
  ${NSD_CreateLabel} 50 155 300 40 "请选择安装目录，开始安装小梅花AI智能客服系统"
  Pop $SubtitleControl
  SetCtlColors $SubtitleControl 0x6B7280 0xFFFFFF
  CreateFont $1 "Microsoft YaHei" 14 400
  SendMessage $SubtitleControl ${WM_SETFONT} $1 0
  
  ; 创建安装路径输入框
  ${NSD_CreateText} 50 210 250 25 "$INSTDIR"
  Pop $InstallPathControl
  SetCtlColors $InstallPathControl 0x333333 0xFFFFFF
  
  ; 创建浏览按钮
  ${NSD_CreateButton} 310 210 40 25 "浏览"
  Pop $BrowseButton
  SetCtlColors $BrowseButton 0x374151 0xF3F4F6
  ${NSD_OnClick} $BrowseButton BrowseButtonClick
  
  ; 创建安装按钮
  ${NSD_CreateButton} 50 260 300 40 "开始安装"
  Pop $InstallButton
  SetCtlColors $InstallButton 0xFFFFFF 0x3B82F6
  CreateFont $2 "Microsoft YaHei" 16 500
  SendMessage $InstallButton ${WM_SETFONT} $2 0
  ${NSD_OnClick} $InstallButton InstallButtonClick
  
  ; 设置当前步骤
  StrCpy $CurrentStep "welcome"
  
  nsDialogs::Show
FunctionEnd

Function BrowseButtonClick
  nsDialogs::SelectFolderDialog "选择安装目录" "$INSTDIR"
  Pop $0
  ${If} $0 != error
    ${NSD_SetText} $InstallPathControl $0
    StrCpy $INSTDIR $0
  ${EndIf}
FunctionEnd

Function InstallButtonClick
  ; 获取安装路径
  ${NSD_GetText} $InstallPathControl $INSTDIR
  
  ; 隐藏欢迎界面控件
  ShowWindow $TitleControl ${SW_HIDE}
  ShowWindow $SubtitleControl ${SW_HIDE}
  ShowWindow $InstallPathControl ${SW_HIDE}
  ShowWindow $BrowseButton ${SW_HIDE}
  ShowWindow $InstallButton ${SW_HIDE}
  
  ; 显示进度界面
  ShowProgressPage
  
  ; 开始安装
  Call StartInstallation
FunctionEnd

Function ShowProgressPage
  ; 创建进度标题
  ${NSD_CreateLabel} 50 120 300 30 "正在安装..."
  Pop $TitleControl
  SetCtlColors $TitleControl 0x1F2937 0xFFFFFF
  CreateFont $0 "Microsoft YaHei" 20 600
  SendMessage $TitleControl ${WM_SETFONT} $0 0
  
  ; 创建进度副标题
  ${NSD_CreateLabel} 50 155 300 40 "小梅花AI智能客服正在安装中，请稍候"
  Pop $SubtitleControl
  SetCtlColors $SubtitleControl 0x6B7280 0xFFFFFF
  CreateFont $1 "Microsoft YaHei" 14 400
  SendMessage $SubtitleControl ${WM_SETFONT} $1 0
  
  ; 创建进度条
  ${NSD_CreateProgressBar} 50 210 300 6 ""
  Pop $ProgressControl
  
  ; 创建进度文本
  ${NSD_CreateLabel} 175 230 50 20 "0%"
  Pop $ProgressText
  SetCtlColors $ProgressText 0x1F2937 0xFFFFFF
  CreateFont $2 "Microsoft YaHei" 18 500
  SendMessage $ProgressText ${WM_SETFONT} $2 0
  
  ; 设置当前步骤
  StrCpy $CurrentStep "progress"
FunctionEnd

Function StartInstallation
  ; 模拟安装进度
  ${For} $R0 0 100
    SendMessage $ProgressControl ${PBM_SETPOS} $R0 0
    ${NSD_SetText} $ProgressText "$R0%"
    Sleep 50
  ${Next}
  
  ; 安装完成，显示完成页面
  Call ShowCompletePage
FunctionEnd

Function ShowCompletePage
  ; 隐藏进度界面控件
  ShowWindow $TitleControl ${SW_HIDE}
  ShowWindow $SubtitleControl ${SW_HIDE}
  ShowWindow $ProgressControl ${SW_HIDE}
  ShowWindow $ProgressText ${SW_HIDE}
  
  ; 创建完成图标
  ${NSD_CreateLabel} 175 120 50 50 "✓"
  Pop $CompleteIcon
  SetCtlColors $CompleteIcon 0xFFFFFF 0x10B981
  CreateFont $0 "Microsoft YaHei" 32 700
  SendMessage $CompleteIcon ${WM_SETFONT} $0 0
  
  ; 创建完成标题
  ${NSD_CreateLabel} 50 180 300 30 "安装完成"
  Pop $TitleControl
  SetCtlColors $TitleControl 0x1F2937 0xFFFFFF
  CreateFont $1 "Microsoft YaHei" 20 600
  SendMessage $TitleControl ${WM_SETFONT} $1 0
  
  ; 创建完成副标题
  ${NSD_CreateLabel} 50 215 300 40 "小梅花AI智能客服已成功安装"
  Pop $SubtitleControl
  SetCtlColors $SubtitleControl 0x6B7280 0xFFFFFF
  CreateFont $2 "Microsoft YaHei" 14 400
  SendMessage $SubtitleControl ${WM_SETFONT} $2 0
  
  ; 创建启动按钮
  ${NSD_CreateButton} 50 270 300 40 "立即启动"
  Pop $LaunchButton
  SetCtlColors $LaunchButton 0xFFFFFF 0x3B82F6
  CreateFont $3 "Microsoft YaHei" 16 500
  SendMessage $LaunchButton ${WM_SETFONT} $3 0
  ${NSD_OnClick} $LaunchButton LaunchButtonClick
  
  ; 设置当前步骤
  StrCpy $CurrentStep "complete"
FunctionEnd

Function LaunchButtonClick
  ; 启动应用程序
  Exec "$INSTDIR\${PRODUCT_NAME}.exe"
  ; 关闭安装程序
  Quit
FunctionEnd

; 页面离开函数
Function CustomWelcomePageLeave
  ; 清理资源
FunctionEnd
