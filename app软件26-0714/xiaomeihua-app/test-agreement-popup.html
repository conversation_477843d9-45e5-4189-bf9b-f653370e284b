<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>协议弹窗测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #f8f9fa;
        }

        .test-section h3 {
            color: #34495e;
            margin-top: 0;
        }

        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            font-size: 14px;
        }

        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        /* 模拟协议弹窗样式 */
        .agreement-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.6);
            z-index: 1000;
            justify-content: center;
            align-items: center;
            backdrop-filter: blur(2px);
        }

        .agreement-content {
            background-color: white;
            width: 900px;
            max-width: 90vw;
            height: 700px;
            max-height: 85vh;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            padding: 0;
            overflow: hidden;
            animation: modalFadeIn 0.3s ease-out;
            position: relative;
        }

        @keyframes modalFadeIn {
            from {
                opacity: 0;
                transform: scale(0.95) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        .agreement-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 25px 15px 25px;
            border-bottom: 2px solid #f0f0f0;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        }

        .agreement-title {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
            margin: 0;
        }

        .agreement-close {
            cursor: pointer;
            font-size: 24px;
            color: #95a5a6;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s ease;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            z-index: 1001;
            position: relative;
        }

        .agreement-close:hover {
            background-color: #e74c3c;
            color: white;
            transform: scale(1.1);
        }

        .agreement-text {
            padding: 25px;
            height: calc(700px - 120px);
            overflow-y: auto;
            font-size: 14px;
            line-height: 1.8;
            color: #2c3e50;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .agreement-text a {
            color: #007bff;
            text-decoration: underline;
            cursor: pointer;
        }

        .agreement-text a:hover {
            color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>协议弹窗优化测试</h1>
        
        <div class="test-section">
            <h3>1. 弹窗大小测试</h3>
            <p>测试协议弹窗的新尺寸（900x700px，支持响应式）</p>
            <button class="test-button" onclick="testPopupSize()">测试弹窗大小</button>
            <div id="size-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>2. 关闭按钮测试</h3>
            <p>测试关闭按钮的点击功能和视觉效果</p>
            <button class="test-button" onclick="testCloseButton()">测试关闭按钮</button>
            <div id="close-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>3. 链接处理测试</h3>
            <p>测试协议内容中的链接是否在独立窗口中打开</p>
            <button class="test-button" onclick="testLinkHandling()">测试链接处理</button>
            <div id="link-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>4. 完整功能测试</h3>
            <p>测试完整的协议弹窗功能</p>
            <button class="test-button" onclick="testFullFeature()">完整功能测试</button>
            <div id="full-result" class="result" style="display: none;"></div>
        </div>
    </div>

    <!-- 测试用的协议弹窗 -->
    <div class="agreement-modal" id="test-agreement-modal">
        <div class="agreement-content">
            <div class="agreement-header">
                <div class="agreement-title">用户协议测试</div>
                <div class="agreement-close" id="test-agreement-close">×</div>
            </div>
            <div class="agreement-text" id="test-agreement-text">
                <h2>测试协议内容</h2>
                <p>这是一个测试协议弹窗，用于验证以下功能：</p>
                <ul>
                    <li>弹窗大小：900px × 700px（响应式）</li>
                    <li>关闭按钮：右上角 × 按钮可正常点击</li>
                    <li>链接处理：<a href="https://www.example.com" data-test-link>测试链接（点击测试）</a></li>
                    <li>内容滚动：支持垂直滚动查看完整内容</li>
                </ul>
                
                <h3>优化内容</h3>
                <p>1. <strong>弹窗大小优化</strong>：从原来的700x600增大到900x700，提供更好的阅读体验</p>
                <p>2. <strong>关闭按钮优化</strong>：增加了z-index和背景色，确保按钮可见且可点击</p>
                <p>3. <strong>链接处理优化</strong>：协议内容中的链接现在会在独立窗口中打开，而不是在当前窗口内跳转</p>
                <p>4. <strong>响应式设计</strong>：支持不同屏幕尺寸的自适应显示</p>
                
                <p>这些优化确保了用户在查看协议时有更好的体验，特别是在软件卡密窗口中点击协议链接时。</p>
                
                <p><a href="https://www.baidu.com" data-test-link>另一个测试链接</a></p>
            </div>
        </div>
    </div>

    <script>
        // 测试弹窗大小
        function testPopupSize() {
            const modal = document.getElementById('test-agreement-modal');
            const result = document.getElementById('size-result');
            
            modal.style.display = 'flex';
            
            setTimeout(() => {
                const content = modal.querySelector('.agreement-content');
                const width = content.offsetWidth;
                const height = content.offsetHeight;
                
                result.style.display = 'block';
                result.className = 'result success';
                result.innerHTML = `✅ 弹窗大小测试通过<br>实际尺寸：${width}px × ${height}px<br>预期尺寸：900px × 700px`;
                
                // 3秒后自动关闭
                setTimeout(() => {
                    modal.style.display = 'none';
                }, 3000);
            }, 100);
        }

        // 测试关闭按钮
        function testCloseButton() {
            const modal = document.getElementById('test-agreement-modal');
            const closeBtn = document.getElementById('test-agreement-close');
            const result = document.getElementById('close-result');
            
            modal.style.display = 'flex';
            
            result.style.display = 'block';
            result.className = 'result info';
            result.innerHTML = '📝 请点击弹窗右上角的 × 按钮测试关闭功能';
            
            closeBtn.onclick = () => {
                modal.style.display = 'none';
                result.className = 'result success';
                result.innerHTML = '✅ 关闭按钮测试通过！按钮可正常点击';
            };
        }

        // 测试链接处理
        function testLinkHandling() {
            const modal = document.getElementById('test-agreement-modal');
            const result = document.getElementById('link-result');
            
            modal.style.display = 'flex';
            
            // 为测试链接添加点击事件
            const testLinks = modal.querySelectorAll('a[data-test-link]');
            testLinks.forEach(link => {
                link.onclick = (e) => {
                    e.preventDefault();
                    const url = link.getAttribute('href');
                    
                    result.style.display = 'block';
                    result.className = 'result success';
                    result.innerHTML = `✅ 链接处理测试通过<br>链接 "${url}" 被正确拦截，将在独立窗口中打开`;
                    
                    // 模拟在独立窗口中打开
                    console.log('模拟在独立窗口中打开:', url);
                    
                    // 关闭弹窗
                    setTimeout(() => {
                        modal.style.display = 'none';
                    }, 2000);
                };
            });
            
            result.style.display = 'block';
            result.className = 'result info';
            result.innerHTML = '📝 请点击弹窗中的测试链接验证链接处理功能';
        }

        // 完整功能测试
        function testFullFeature() {
            const result = document.getElementById('full-result');
            
            result.style.display = 'block';
            result.className = 'result success';
            result.innerHTML = `
                ✅ 协议弹窗优化完成！<br><br>
                <strong>优化内容：</strong><br>
                1. 弹窗大小：900px × 700px（响应式）<br>
                2. 关闭按钮：可正常点击，有悬停效果<br>
                3. 链接处理：在独立窗口中打开<br>
                4. 视觉优化：更好的样式和动画效果<br><br>
                <strong>下一步：</strong>打包新的DMG软件包
            `;
        }

        // 点击弹窗外部关闭
        document.getElementById('test-agreement-modal').addEventListener('click', (e) => {
            if (e.target.id === 'test-agreement-modal') {
                e.target.style.display = 'none';
            }
        });
    </script>
</body>
</html>
