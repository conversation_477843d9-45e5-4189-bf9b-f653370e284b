/**
 * 验证打包结果
 * 检查所有新功能是否正确包含在构建中
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 验证小梅花AI智能客服 v2.0.0 构建结果...\n');

// 检查必要文件是否存在
const requiredFiles = [
    'dist/小梅花AI智能客服-2.0.0-arm64.dmg',
    'src/update-manager.js',
    'src/app-settings-api.js',
    'src/popup-manager.js',
    'src/agreement-manager.js',
    'package.json'
];

console.log('📁 检查必要文件:');
let allFilesExist = true;

for (const file of requiredFiles) {
    if (fs.existsSync(file)) {
        console.log(`✅ ${file}`);
    } else {
        console.log(`❌ ${file} - 文件不存在`);
        allFilesExist = false;
    }
}

// 检查package.json版本
console.log('\n📦 检查版本信息:');
try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    if (packageJson.version === '2.0.0') {
        console.log(`✅ 版本号: ${packageJson.version}`);
    } else {
        console.log(`❌ 版本号错误: ${packageJson.version} (期望: 2.0.0)`);
        allFilesExist = false;
    }
    
    if (packageJson.description.includes('自动更新')) {
        console.log(`✅ 描述包含更新功能: ${packageJson.description}`);
    } else {
        console.log(`⚠️ 描述未提及更新功能: ${packageJson.description}`);
    }
} catch (error) {
    console.log(`❌ 无法读取package.json: ${error.message}`);
    allFilesExist = false;
}

// 检查更新管理器代码
console.log('\n🔄 检查更新管理器功能:');
try {
    const updateManagerCode = fs.readFileSync('src/update-manager.js', 'utf8');
    
    const features = [
        { name: '检查更新', pattern: /checkForUpdates/ },
        { name: '下载文件', pattern: /downloadFile/ },
        { name: '安装更新', pattern: /installUpdate/ },
        { name: '重启应用', pattern: /restartApp/ },
        { name: '强制更新窗口', pattern: /createForceUpdateWindow/ },
        { name: 'Windows安装', pattern: /installWindowsUpdate/ },
        { name: 'Mac安装', pattern: /installMacUpdate/ }
    ];
    
    for (const feature of features) {
        if (feature.pattern.test(updateManagerCode)) {
            console.log(`✅ ${feature.name}`);
        } else {
            console.log(`❌ ${feature.name} - 功能缺失`);
            allFilesExist = false;
        }
    }
} catch (error) {
    console.log(`❌ 无法读取更新管理器代码: ${error.message}`);
    allFilesExist = false;
}

// 检查主进程集成
console.log('\n🔧 检查主进程集成:');
try {
    const mainCode = fs.readFileSync('src/main.js', 'utf8');
    
    const integrations = [
        { name: '更新管理器导入', pattern: /require.*update-manager/ },
        { name: '更新管理器初始化', pattern: /updateManager.*=.*new.*UpdateManager/ },
        { name: '启动时检查更新', pattern: /checkForUpdates/ },
        { name: 'IPC处理器', pattern: /ipcMain\.handle.*check-for-updates/ }
    ];
    
    for (const integration of integrations) {
        if (integration.pattern.test(mainCode)) {
            console.log(`✅ ${integration.name}`);
        } else {
            console.log(`❌ ${integration.name} - 集成缺失`);
            allFilesExist = false;
        }
    }
} catch (error) {
    console.log(`❌ 无法读取主进程代码: ${error.message}`);
    allFilesExist = false;
}

// 检查构建文件大小
console.log('\n📊 检查构建文件:');
const distDir = 'dist';
if (fs.existsSync(distDir)) {
    const files = fs.readdirSync(distDir);
    
    for (const file of files) {
        const filePath = path.join(distDir, file);
        const stats = fs.statSync(filePath);
        
        if (stats.isFile()) {
            const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
            
            if (file.endsWith('.dmg')) {
                if (stats.size > 50 * 1024 * 1024) { // 大于50MB
                    console.log(`✅ ${file}: ${sizeInMB} MB (合理大小)`);
                } else {
                    console.log(`⚠️ ${file}: ${sizeInMB} MB (可能太小)`);
                }
            } else if (file.endsWith('.exe')) {
                if (stats.size > 40 * 1024 * 1024) { // 大于40MB
                    console.log(`✅ ${file}: ${sizeInMB} MB (合理大小)`);
                } else {
                    console.log(`⚠️ ${file}: ${sizeInMB} MB (可能太小)`);
                }
            } else {
                console.log(`📄 ${file}: ${sizeInMB} MB`);
            }
        }
    }
} else {
    console.log('❌ dist目录不存在');
    allFilesExist = false;
}

// 生成验证报告
console.log('\n📋 验证总结:');
if (allFilesExist) {
    console.log('🎉 所有检查通过！构建包含完整的更新功能。');
    console.log('\n✅ 可以进行的测试:');
    console.log('  1. 安装应用并启动');
    console.log('  2. 在后台创建测试版本 3.0.0');
    console.log('  3. 重启应用测试自动更新');
    console.log('  4. 验证强制更新流程');
    console.log('  5. 测试下载和安装功能');
} else {
    console.log('❌ 发现问题，请检查上述错误并重新构建。');
}

// 保存验证报告
const verificationReport = {
    timestamp: new Date().toISOString(),
    version: '2.0.0',
    status: allFilesExist ? 'PASS' : 'FAIL',
    filesChecked: requiredFiles.length,
    buildSize: {
        dmg: fs.existsSync('dist/小梅花AI智能客服-2.0.0-arm64.dmg') ? 
             fs.statSync('dist/小梅花AI智能客服-2.0.0-arm64.dmg').size : 0
    }
};

fs.writeFileSync('verification-report.json', JSON.stringify(verificationReport, null, 2));
console.log('\n📊 验证报告已保存到 verification-report.json');

process.exit(allFilesExist ? 0 : 1);
