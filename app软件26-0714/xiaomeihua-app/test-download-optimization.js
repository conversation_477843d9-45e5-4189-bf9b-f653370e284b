/**
 * 测试全新自动下载功能
 * 基于用户脚本的核心逻辑，测试自动下载功能
 */

const { UpdateManager } = require('./src/update-manager');
const path = require('path');

// 测试用的蓝奏云链接（请替换为真实链接进行测试）
const testUrls = [
    'https://wwke.lanzoue.com/itest123',  // 替换为实际的测试链接
    'https://lanzoue.com/itest456'  // 替换为实际的测试链接
];

async function testAutoDownload() {
    console.log('🧪 开始测试全新自动下载功能...\n');
    console.log('📝 基于用户脚本的核心逻辑：');
    console.log('   - 使用隐藏浏览器窗口加载蓝奏云页面');
    console.log('   - 注入自动下载脚本');
    console.log('   - 定时器检查 #tourl a[href] 元素');
    console.log('   - 找到后立即触发下载\n');

    const updateManager = new UpdateManager();

    for (let i = 0; i < testUrls.length; i++) {
        const url = testUrls[i];
        console.log(`\n📋 测试链接 ${i + 1}: ${url}`);
        console.log('=' .repeat(60));

        try {
            console.log('🚀 启动自动下载测试...');

            // 测试新的自动下载功能
            const downloadUrl = await updateManager.parseLanzouUrl(url);

            if (downloadUrl) {
                console.log('✅ 自动下载成功!');
                console.log('🔗 获取到的下载链接:', downloadUrl);

                // 验证链接格式
                if (downloadUrl.includes('developer-oss.lanrar.com') ||
                    downloadUrl.includes('/file/') ||
                    downloadUrl.startsWith('http')) {
                    console.log('✅ 链接格式正确');
                } else {
                    console.log('⚠️ 链接格式可能不正确');
                }

                // 检查链接长度（通常真实下载链接会比较长）
                if (downloadUrl.length > 50) {
                    console.log('✅ 链接长度正常 (' + downloadUrl.length + ' 字符)');
                } else {
                    console.log('⚠️ 链接长度较短，可能不是最终下载链接');
                }
            } else {
                console.log('❌ 自动下载失败 - 未获取到下载链接');
            }

        } catch (error) {
            console.log('❌ 自动下载失败:', error.message);

            // 分析错误类型
            if (error.message.includes('超时')) {
                console.log('💡 提示: 可能是页面加载时间过长或网络问题');
            } else if (error.message.includes('脚本注入')) {
                console.log('� 提示: 可能是页面结构变化导致脚本注入失败');
            } else if (error.message.includes('下载链接')) {
                console.log('💡 提示: 可能是页面中没有找到 #tourl a[href] 元素');
            }
        }

        // 等待一段时间避免请求过于频繁
        if (i < testUrls.length - 1) {
            console.log('\n⏳ 等待 5 秒后继续下一个测试...');
            await new Promise(resolve => setTimeout(resolve, 5000));
        }
    }

    console.log('\n🎉 自动下载测试完成!');
}

// 模拟页面内容测试
function testParameterExtraction() {
    console.log('\n🔍 测试直接下载链接识别功能...\n');

    // 模拟用户提供的新页面内容（包含直接下载链接）
    const mockPageContent = `
        <a href="https://developer-oss.lanrar.com/file/?BGJSbAEwAjNSWwI6BTAGagc4VGxX7VbkB48D412jAYUA6ALfWrYOGwUaBrZRnAe8V+UAhlS2A+RW/wOnVe9QzQSJUigBbQJ4UjQCegVhBioHPFQjV2VWYgc0AytdZQFtAGcCc1phDjMFPwY1UVoHb1dpADhUOQM1VmYDNFU4UGgENFI8AWsCcFJiAicFbAY2B21UaFc/VmEHMwMwXTcBOAAmAiVadw5nBWEGYFE3BzNXIgA1VDwDLlZjAzxVJlAwBDJSYQFlAmJSYQJkBWEGMwdqVGdXPlY1BzgDZl03AWMANQJgWmEOPAVjBmFRPAc3V2sAZFRqAzhWMgNnVWxQfwRgUmgBOwJwUnECJwU0BnUHNFQ1VzVWYAc2AzNdOQEyADkCZFohDi4FOgY9UWAHYFcwADRUPAM0VmIDPVU4UGQEMlIwAW8CeFIqAnIFNwZrBypUbFc5VmMHNQM2XTkBMQAzAmFaMg5iBXUGJVF1B3FXMAA0VDwDNFZiAzJVPFBnBD1SMAFsAnBScQI9BSEGOgdsVGRXO1Z6BzMDMF0vATIAMgJnWikObgVrBn5RIwdiV2IAclRlA15WMgNuVTRQYQQqUiMBKAIvUnQCMQUOBnIHPFRsVzg=" target="_blank" rel="noreferrer">下载</a>

        <div class="load" id="tourl">
            <a href="https://developer-oss.lanrar.com/file/?BGJSbAEwAjNSWwI6BTAGagc4VGxX7VbkB48D412jAYUA6ALfWrYOGwUaBrZRnAe8V+UAhlS2A+RW/wOnVe9QzQSJUigBbQJ4UjQCegVhBioHPFQjV2VWYgc0AytdZQFtAGcCc1phDjMFPwY1UVoHb1dpADhUOQM1VmYDNFU4UGgENFI8AWsCcFJiAicFbAY2B21UaFc/VmEHMwMwXTcBOAAmAiVadw5nBWEGYFE3BzNXIgA1VDwDLlZjAzxVJlAwBDJSYQFlAmJSYQJkBWEGMwdqVGdXPlY1BzgDZl03AWMANQJgWmEOPAVjBmFRPAc3V2sAZFRqAzhWMgNnVWxQfwRgUmgBOwJwUnECJwU0BnUHNFQ1VzVWYAc2AzNdOQEyADkCZFohDi4FOgY9UWAHYFcwADRUPAM0VmIDPVU4UGQEMlIwAW8CeFIqAnIFNwZrBypUbFc5VmMHNQM2XTkBMQAzAmFaMg5iBXUGJVF1B3FXMAA0VDwDNFZiAzJVPFBnBD1SMAFsAnBScQI9BSEGOgdsVGRXO1Z6BzMDMF0vATIAMgJnWikObgVrBn5RIwdiV2IAclRlA15WMgNuVTRQYQQqUiMBKAIvUnQCMQUOBnIHPFRsVzg=" target="_blank" rel="noreferrer">下载</a>
        </div>

        <!-- 旧的iframe方式（作为备用） -->
        <iframe class="n_downlink" name="1753813643" src="/fn?VTMCaFo1AmIBYAViBmNWbgFrV2tSOldzB3QGPQFsAzUIPVo_bAGVSN1M2BmYAYFVnAHwPfABiAzBWdwZ3Bz5WNVUmAjxaYAI4ATwFNgYrVmoBA1cGUltXbQcmBncBPANkCFdafQA_bUmlTawZtAGRVcgAzD3kAAAN2VjAGZgc_aVjhVOAI0Wm4COAFqBTsGJlYjAStXIVI3V3oHLwZ3ATwDZAgmWnoAMlJpU38GPwA7VTsAdA9pADADbFZ_b" frameborder="0" scrolling="no"></iframe>
    `;
    
    // 测试新的直接下载链接识别
    console.log('📋 测试直接下载链接识别:');

    // 测试优先级最高的developer-oss.lanrar.com链接
    const directLinkPatterns = [
        // 最优先：匹配tourl div中的developer-oss.lanrar.com链接
        /<div[^>]*id="tourl"[^>]*>[\s\S]*?href="(https:\/\/developer-oss\.lanrar\.com\/file\/\?[^"]*)"[^>]*>下载<\/a>/,
        // 次优先：匹配任何developer-oss.lanrar.com链接
        /href="(https:\/\/developer-oss\.lanrar\.com\/file\/\?[^"]*)"/,
        // 备用：匹配tourl div中的任何下载链接
        /<div[^>]*id="tourl"[^>]*>[\s\S]*?href="([^"]*)"[^>]*>下载<\/a>/
    ];

    let foundDirectLink = null;
    for (let i = 0; i < directLinkPatterns.length; i++) {
        const match = mockPageContent.match(directLinkPatterns[i]);
        if (match) {
            foundDirectLink = match[1];
            console.log(`✅ 方式${i + 1}找到直接下载链接:`, foundDirectLink);
            break;
        }
    }

    if (foundDirectLink) {
        if (foundDirectLink.includes('developer-oss.lanrar.com')) {
            console.log('🎯 检测到developer-oss.lanrar.com链接，这是最佳的直接下载链接！');
            console.log('📏 链接长度:', foundDirectLink.length);
            console.log('🔗 链接预览:', foundDirectLink.substring(0, 80) + '...');
        }
    } else {
        console.log('❌ 未找到直接下载链接');
    }

    // 测试iframe链接提取（作为备用）
    const iframePattern = /class="n_downlink"[^>]*src="([^"]*)"/;
    const iframeMatch = mockPageContent.match(iframePattern);
    if (iframeMatch) {
        console.log('📋 备用iframe链接:', iframeMatch[1]);
    }
}

// 运行测试
async function runTests() {
    console.log('🚀 开始运行全新自动下载功能测试\n');

    // 先测试用户脚本逻辑验证
    testParameterExtraction();

    // 再测试实际自动下载（如果有测试链接）
    if (testUrls.some(url => !url.includes('itest'))) {
        await testAutoDownload();
    } else {
        console.log('\n⚠️ 跳过实际自动下载测试（需要真实的蓝奏云链接）');
        console.log('💡 请将 testUrls 中的链接替换为真实的蓝奏云下载链接进行测试');
        console.log('📝 新的自动下载功能特点：');
        console.log('   ✅ 完全基于用户脚本的核心逻辑');
        console.log('   ✅ 自动检查 #tourl a[href] 元素');
        console.log('   ✅ 无需复杂的iframe和AJAX处理');
        console.log('   ✅ 支持30秒超时保护');
        console.log('   ✅ 真正的"进入页面即自动下载"');
    }
}

// 如果直接运行此文件
if (require.main === module) {
    runTests().catch(console.error);
}

module.exports = {
    testAutoDownload,
    testParameterExtraction,
    runTests
};
