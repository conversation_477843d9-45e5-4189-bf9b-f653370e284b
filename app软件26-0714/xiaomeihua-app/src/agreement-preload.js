/**
 * 协议预加载脚本
 * 为协议窗口提供API通信接口
 */

const { contextBridge, ipc<PERSON>enderer } = require('electron');

// 向渲染进程暴露API
contextBridge.exposeInMainWorld('agreementAPI', {
    // 获取当前协议数据
    getCurrentAgreement: () => ipcRenderer.invoke('get-current-agreement'),

    // 同意协议
    acceptAgreement: (agreementId) => ipcRenderer.invoke('agreement-accept', agreementId),

    // 拒绝协议
    declineAgreement: (agreementId) => ipcRenderer.invoke('agreement-decline', agreementId),

    // 关闭协议窗口
    closeWindow: () => ipcRenderer.invoke('close-agreement-window'),

    // 打开外部链接
    openExternal: (url) => ipcRenderer.invoke('open-external', url)
});

// 监听DOM加载完成
document.addEventListener('DOMContentLoaded', async () => {
    try {
        // 获取协议数据并渲染
        const agreement = await window.agreementAPI.getCurrentAgreement();
        if (agreement) {
            renderAgreement(agreement);
        }
    } catch (error) {
        console.error('加载协议数据失败:', error);
    }
});

/**
 * 渲染协议内容
 * @param {Object} agreement - 协议数据
 */
function renderAgreement(agreement) {
    // 设置标题
    const titleElement = document.getElementById('agreement-title');
    if (titleElement) {
        titleElement.textContent = agreement.title || '用户协议';
    }

    // 设置内容
    const contentElement = document.getElementById('agreement-content');
    if (contentElement) {
        contentElement.innerHTML = agreement.content || '<p>协议内容加载中...</p>';
    }
    
    // 绑定同意按钮事件
    const acceptButton = document.getElementById('accept-button');
    if (acceptButton) {
        acceptButton.addEventListener('click', async () => {
            await window.agreementAPI.acceptAgreement(agreement.id);
        });
    }
    
    // 绑定拒绝按钮事件
    const declineButton = document.getElementById('decline-button');
    if (declineButton) {
        declineButton.addEventListener('click', async () => {
            await window.agreementAPI.declineAgreement(agreement.id);
        });
    }
    
    // 处理内容中的链接点击
    const links = contentElement.querySelectorAll('a[href]');
    links.forEach(link => {
        link.addEventListener('click', async (e) => {
            e.preventDefault();
            const url = link.getAttribute('href');
            await window.agreementAPI.openExternal(url);
        });
    });
}

// 添加一些样式优化
document.addEventListener('DOMContentLoaded', () => {
    // 添加动画效果
    const container = document.getElementById('agreement-container');
    if (container) {
        container.style.opacity = '0';
        container.style.transform = 'scale(0.95)';
        
        // 渐入动画
        setTimeout(() => {
            container.style.transition = 'all 0.3s ease-out';
            container.style.opacity = '1';
            container.style.transform = 'scale(1)';
        }, 100);
    }
});