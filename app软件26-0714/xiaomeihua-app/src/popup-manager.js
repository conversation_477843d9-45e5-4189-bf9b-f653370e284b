/**
 * APP弹窗管理器
 * 处理弹窗的显示、定时、记录等功能
 */

const { BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const Store = require('electron-store');
const AppSettingsAPI = require('./app-settings-api');

class PopupManager {
    constructor() {
        this.store = new Store({
            name: 'popup-manager',
            encryptionKey: 'xiaomeihua-popup-2025'
        });
        
        this.api = new AppSettingsAPI();
        this.popupWindow = null;
        this.currentPopup = null;
        this.checkInterval = null;
        
        // 弹窗检查间隔（毫秒）
        this.CHECK_INTERVAL = 30 * 60 * 1000; // 30分钟检查一次
        
        this.initializeIPC();
    }
    
    /**
     * 初始化IPC通信
     */
    initializeIPC() {
        // 处理弹窗关闭
        ipcMain.handle('popup-close', async (event, popupId) => {
            if (this.currentPopup && this.currentPopup.id === popupId) {
                await this.api.logPopupAction(popupId, 'close');
                this.updatePopupHistory(popupId, 'close');
            }
            this.closePopup();
        });
        
        // 处理弹窗点击
        ipcMain.handle('popup-click', async (event, popupId, action) => {
            if (this.currentPopup && this.currentPopup.id === popupId) {
                await this.api.logPopupAction(popupId, action);
                this.updatePopupHistory(popupId, action);
            }
        });
        
        // 获取当前弹窗数据
        ipcMain.handle('get-current-popup', () => {
            console.log('🔍 IPC请求获取当前弹窗数据:', this.currentPopup ? this.currentPopup.title : 'null');
            return this.currentPopup;
        });

        // 注释掉重复的open-external处理器，main.js中已经有了
        // ipcMain.handle('open-external', async (event, url) => {
        //     try {
        //         const { shell } = require('electron');
        //         await shell.openExternal(url);
        //         console.log('🔗 成功打开外部链接:', url);
        //     } catch (error) {
        //         console.error('❌ 打开外部链接失败:', error);
        //     }
        // });
    }
    
    /**
     * 启动弹窗管理器
     * @param {Object} options - 启动选项
     * @param {boolean} options.clearHistory - 是否清除历史记录
     */
    start(options = {}) {
        console.log('🚀 弹窗管理器启动');

        // 如果指定清除历史记录，则清除
        if (options.clearHistory) {
            console.log('🧹 启动时清除历史记录');
            this.clearAllHistory();
        }

        // 自动清除过期历史记录
        this.cleanupExpiredHistory();

        // 立即检查一次
        this.checkForPopup();

        // 设置定时检查
        this.checkInterval = setInterval(() => {
            this.checkForPopup();
        }, this.CHECK_INTERVAL);

        console.log(`⏰ 弹窗检查间隔: ${this.CHECK_INTERVAL / 1000 / 60} 分钟`);
    }
    
    /**
     * 停止弹窗管理器
     */
    stop() {
        console.log('弹窗管理器停止');
        
        if (this.checkInterval) {
            clearInterval(this.checkInterval);
            this.checkInterval = null;
        }
        
        this.closePopup();
    }
    
    /**
     * 检查是否有需要显示的弹窗
     */
    async checkForPopup() {
        try {
            console.log('🔍 检查弹窗...');
            const result = await this.api.getActivePopup();

            if (result.success && result.popup) {
                const popup = result.popup;
                console.log('📋 获取到弹窗数据:', popup.title);

                // 检查是否应该显示此弹窗
                if (this.shouldShowPopup(popup)) {
                    console.log('✅ 显示弹窗:', popup.title);
                    await this.showPopup(popup);
                } else {
                    console.log('⏭️ 跳过弹窗:', popup.title, '(已显示过或不符合条件)');
                }
            } else {
                console.log('📭 没有活跃的弹窗');
            }
        } catch (error) {
            console.error('❌ 检查弹窗失败:', error);
            // 如果API调用失败，尝试显示本地缓存的弹窗
            this.showFallbackPopup();
        }
    }
    
    /**
     * 判断是否应该强制显示弹窗
     * @param {Object} popup - 弹窗数据
     * @returns {boolean} 是否应该强制显示
     */
    shouldForceShow(popup) {
        // 处理type字段，支持数组和字符串格式
        let popupTypes = [];
        if (Array.isArray(popup.type)) {
            popupTypes = popup.type;
        } else if (typeof popup.type === 'string') {
            popupTypes = popup.type.split(',').map(t => t.trim());
        }

        // 强制显示条件：
        // 1. 弹窗状态为active
        const isActive = popup.status === 'active';

        // 2. 弹窗类型包含 'always' - 只有always类型才强制显示
        const hasAlwaysType = popupTypes.includes('always');

        console.log(`🔍 强制显示检查 ${popup.id}:`);
        console.log(`  - 状态活跃: ${isActive} (状态: ${popup.status})`);
        console.log(`  - Always类型: ${hasAlwaysType} (类型: ${JSON.stringify(popupTypes)})`);

        // 只有always类型才强制显示，确保once类型不会被强制显示
        const shouldForce = hasAlwaysType && isActive;

        if (shouldForce) {
            console.log(`🚀 弹窗 ${popup.id} 满足强制显示条件 (always类型)`);
        } else {
            console.log(`❌ 弹窗 ${popup.id} 不满足强制显示条件`);
        }

        return shouldForce;
    }

    /**
     * 清除指定弹窗的历史记录
     * @param {string} popupId - 弹窗ID
     */
    clearPopupHistory(popupId) {
        const popupHistory = this.store.get('popupHistory', {});
        if (popupHistory[popupId]) {
            delete popupHistory[popupId];
            this.store.set('popupHistory', popupHistory);
            console.log(`🧹 已清除弹窗 ${popupId} 的历史记录`);
        }
    }

    /**
     * 清除过期的历史记录
     */
    cleanupExpiredHistory() {
        const popupHistory = this.store.get('popupHistory', {});
        const now = Date.now();
        const maxAge = 30 * 24 * 60 * 60 * 1000; // 30天
        let cleanedCount = 0;

        for (const [popupId, history] of Object.entries(popupHistory)) {
            if (history.lastShown && (now - history.lastShown) > maxAge) {
                delete popupHistory[popupId];
                cleanedCount++;
            }
        }

        if (cleanedCount > 0) {
            this.store.set('popupHistory', popupHistory);
            console.log(`🧹 清除了 ${cleanedCount} 个过期的弹窗历史记录`);
        }
    }

    /**
     * 强制清除所有历史记录
     */
    clearAllHistory() {
        this.store.delete('popupHistory');
        console.log(`🧹 已清除所有弹窗历史记录`);
    }

    /**
     * 判断是否应该显示弹窗
     * @param {Object} popup - 弹窗数据
     * @returns {boolean} 是否应该显示
     */
    shouldShowPopup(popup) {
        const popupHistory = this.store.get('popupHistory', {});
        const popupId = popup.id.toString();
        const now = Date.now();

        console.log(`🔍 检查弹窗 ${popupId} 是否应该显示`);

        // 如果已经有弹窗在显示，不显示新的
        if (this.popupWindow && !this.popupWindow.isDestroyed()) {
            console.log(`❌ 已有弹窗在显示，跳过弹窗 ${popupId}`);
            return false;
        }

        // 处理type字段，支持数组和字符串格式
        let popupTypes = [];
        if (Array.isArray(popup.type)) {
            popupTypes = popup.type;
        } else if (typeof popup.type === 'string') {
            // 支持逗号分隔的字符串
            popupTypes = popup.type.split(',').map(t => t.trim());
        } else {
            popupTypes = ['once']; // 默认值
        }

        console.log(`📋 弹窗 ${popupId} 类型:`, popupTypes);

        // 🚀 强制显示逻辑：只对always类型或高优先级弹窗强制显示
        if (this.shouldForceShow(popup)) {
            console.log(`🚀 弹窗 ${popupId} 满足强制显示条件`);
            // 只有always类型才清除历史记录，其他类型保持历史记录
            if (popupTypes.includes('always')) {
                console.log(`🚀 Always类型弹窗，清除历史记录确保显示`);
                this.clearPopupHistory(popupId);
            }
            return true;
        }



        // 🧹 自动清除过期历史记录
        this.cleanupExpiredHistory();

        // 获取历史记录
        const history = popupHistory[popupId];
        if (!history) {
            console.log(`✅ 弹窗 ${popupId} 没有历史记录，可以显示`);
            return true;
        }

        const lastShown = history.lastShown || 0;
        const timeDiff = now - lastShown;
        console.log(`📅 弹窗 ${popupId} 上次显示: ${new Date(lastShown).toLocaleString()}`);
        console.log(`⏰ 距离上次显示: ${Math.floor(timeDiff / (1000 * 60 * 60 * 24))} 天`);

        // 检查各种类型
        for (const type of popupTypes) {
            switch (type) {
                case 'once':
                    console.log(`❌ 弹窗 ${popupId} 包含 once 类型，已显示过，不再显示`);
                    return false;

                case 'daily_7':
                    // 7天内每天显示一次
                    const oneDay = 24 * 60 * 60 * 1000;
                    const sevenDays = 7 * 24 * 60 * 60 * 1000;
                    const createdAt = new Date(popup.created_at).getTime();
                    const daysSinceCreated = Math.floor((now - createdAt) / oneDay);

                    // 如果超过7天，不再显示
                    if (daysSinceCreated >= 7) {
                        console.log(`❌ 弹窗 ${popupId} daily_7 类型，已超过7天，不再显示`);
                        return false;
                    }

                    // 如果距离上次显示超过1天，可以显示
                    if (timeDiff > oneDay) {
                        console.log(`✅ 弹窗 ${popupId} daily_7 类型，可以显示 (第${daysSinceCreated + 1}天)`);
                        return true;
                    }
                    console.log(`❌ 弹窗 ${popupId} daily_7 类型，今天已显示过`);
                    break;

                case 'weekly':
                    const oneWeek = 7 * 24 * 60 * 60 * 1000;
                    if (timeDiff > oneWeek) {
                        console.log(`✅ 弹窗 ${popupId} weekly 类型，可以显示`);
                        return true;
                    }
                    break;

                case 'always':
                    console.log(`✅ 弹窗 ${popupId} always 类型，总是显示`);
                    return true;

                case 'daily':
                    const oneDayDaily = 24 * 60 * 60 * 1000;
                    if (timeDiff > oneDayDaily) {
                        console.log(`✅ 弹窗 ${popupId} daily 类型，可以显示`);
                        return true;
                    }
                    break;

                case 'custom':
                    if (popup.custom_days) {
                        const oneDay = 24 * 60 * 60 * 1000;
                        const createdAt = new Date(popup.created_at).getTime();
                        const daysSinceCreated = Math.floor((now - createdAt) / oneDay);

                        // 如果超过自定义天数，不再显示
                        if (daysSinceCreated >= popup.custom_days) {
                            console.log(`❌ 弹窗 ${popupId} custom 类型，已超过${popup.custom_days}天，不再显示`);
                            return false;
                        }

                        // 如果距离上次显示超过1天，可以显示
                        if (timeDiff > oneDay) {
                            console.log(`✅ 弹窗 ${popupId} custom 类型，可以显示 (第${daysSinceCreated + 1}天，共${popup.custom_days}天)`);
                            return true;
                        }
                        console.log(`❌ 弹窗 ${popupId} custom 类型，今天已显示过`);
                    }
                    break;
            }
        }

        console.log(`❌ 弹窗 ${popupId} 不符合显示条件`);
        return false;
    }

    /**
     * 显示备用弹窗（当API调用失败时）
     */
    showFallbackPopup() {
        console.log('🔄 API调用失败，尝试显示备用弹窗');

        // 检查是否有缓存的弹窗数据
        const cachedPopup = this.store.get('lastPopup');
        if (cachedPopup && this.shouldShowPopup(cachedPopup)) {
            console.log('📋 使用缓存的弹窗数据');
            this.showPopup(cachedPopup);
            return;
        }

        // 如果没有缓存，显示默认弹窗
        const defaultPopup = {
            id: 'default',
            title: '小梅花AI助手',
            content: `
                <div class="popup-content-body">
                    <p class="popup-description">欢迎使用小梅花AI助手！</p>
                    <div class="feature-list">
                        <div class="feature-item">
                            <span class="check-icon">✓</span>
                            <span class="feature-text">智能客服功能</span>
                        </div>
                        <div class="feature-item">
                            <span class="check-icon">✓</span>
                            <span class="feature-text">AI知识库管理</span>
                        </div>
                        <div class="feature-item">
                            <span class="check-icon">✓</span>
                            <span class="feature-text">多店铺支持</span>
                        </div>
                    </div>
                </div>
            `,
            type: ['once'],
            status: 'active'
        };

        console.log('📋 显示默认弹窗');
        this.showPopup(defaultPopup);
    }

    /**
     * 显示弹窗
     * @param {Object} popup - 弹窗数据
     */
    async showPopup(popup) {
        try {
            console.log(`🎯 开始显示弹窗: ${popup.title}`);

            // 如果已经有弹窗在显示，先关闭
            if (this.popupWindow && !this.popupWindow.isDestroyed()) {
                console.log('🔄 关闭现有弹窗');
                this.popupWindow.close();
            }

            this.currentPopup = popup;

            // 获取主屏幕尺寸
            const { screen } = require('electron');
            const primaryDisplay = screen.getPrimaryDisplay();
            const { width: screenWidth, height: screenHeight } = primaryDisplay.workAreaSize;

            // 计算居中位置
            const windowWidth = 520;
            const windowHeight = 450;
            const x = Math.round((screenWidth - windowWidth) / 2);
            const y = Math.round((screenHeight - windowHeight) / 2);

            // 创建弹窗窗口
            this.popupWindow = new BrowserWindow({
                width: windowWidth,
                height: windowHeight,
                x: x,
                y: y,
                resizable: false,
                maximizable: false,
                minimizable: false,
                alwaysOnTop: true,
                modal: false,
                frame: false,
                show: false,
                transparent: true,
                backgroundColor: '#00000000',
                webPreferences: {
                    nodeIntegration: false,
                    contextIsolation: true,
                    preload: path.join(__dirname, 'popup-preload.js')
                }
            });

            // 加载弹窗HTML
            await this.popupWindow.loadFile(path.join(__dirname, 'renderer', 'popup.html'));

            // 窗口准备好后显示
            this.popupWindow.once('ready-to-show', () => {
                console.log('✅ 弹窗窗口准备完成，开始显示');
                this.popupWindow.show();
                // 不再调用 center()，因为已经在创建时设置了正确的位置
                console.log(`🎯 弹窗位置: x=${x}, y=${y}, 屏幕尺寸: ${screenWidth}x${screenHeight}`);
            });

            // 处理窗口关闭
            this.popupWindow.on('closed', () => {
                console.log('🔒 弹窗窗口已关闭');
                this.popupWindow = null;
                this.currentPopup = null;
            });

            // 记录弹窗显示
            try {
                await this.api.logPopupAction(popup.id, 'show');
                console.log('📊 弹窗显示日志记录成功');
            } catch (error) {
                console.warn('⚠️ 记录弹窗日志失败:', error.message);
            }

            // 更新本地历史记录
            this.updatePopupHistory(popup.id, 'show');

            // 缓存弹窗数据
            this.store.set('lastPopup', popup);

            console.log('🎉 弹窗显示完成');

        } catch (error) {
            console.error('❌ 显示弹窗失败:', error);
        }
    }
    
    /**
     * 关闭弹窗
     */
    closePopup() {
        if (this.popupWindow && !this.popupWindow.isDestroyed()) {
            this.popupWindow.close();
        }
    }
    
    /**
     * 更新弹窗历史记录
     * @param {number} popupId - 弹窗ID
     * @param {string} action - 操作类型
     */
    updatePopupHistory(popupId, action) {
        const popupHistory = this.store.get('popupHistory', {});
        const popupIdStr = popupId.toString();
        
        if (!popupHistory[popupIdStr]) {
            popupHistory[popupIdStr] = {};
        }
        
        popupHistory[popupIdStr].lastShown = Date.now();
        popupHistory[popupIdStr].lastAction = action;
        popupHistory[popupIdStr].showCount = (popupHistory[popupIdStr].showCount || 0) + 1;
        
        this.store.set('popupHistory', popupHistory);
    }
    
    /**
     * 手动触发弹窗检查（用于测试）
     */
    async forceCheck() {
        await this.checkForPopup();
    }
    
    /**
     * 清除弹窗历史记录
     */
    clearHistory() {
        this.store.delete('popupHistory');
        console.log('弹窗历史记录已清除');
    }
}

module.exports = PopupManager;