/**
 * App更新管理器
 * 负责检查更新、下载、安装等功能
 */

const { app, dialog, shell, BrowserWindow, ipcMain } = require('electron');
const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');
const os = require('os');
const http = require('http');
const https = require('https');
const { URL } = require('url');

class UpdateManager {
    constructor() {
        this.currentVersion = app.getVersion();
        this.platform = this.getPlatform();
        this.updateInfo = null;
        this.downloadProgress = 0;
        this.isDownloading = false;
        this.isInstalling = false;
        this.updateWindow = null;
        this.forceUpdate = false;
        
        // API配置 - 使用线上服务器
        this.apiConfig = {
            baseURL: 'https://xiaomeihuakefu.cn',
            endpoints: {
                check: '/api/app_update_new.php?action=check',
                download: '/api/app_update_new.php?action=download'
            }
        };

        // 备用API配置
        this.fallbackApiConfig = {
            baseURL: 'https://api.xiaomeihuakefu.cn',
            endpoints: {
                check: '/app_update_standalone.php?action=check',
                download: '/app_update_standalone.php?action=download'
            }
        };
        
        console.log(`🔧 更新管理器初始化 - 当前版本: ${this.currentVersion}, 平台: ${this.platform}`);
    }
    
    /**
     * 获取当前平台
     */
    getPlatform() {
        switch (process.platform) {
            case 'win32':
                return 'windows';
            case 'darwin':
                return 'macos';
            case 'linux':
                return 'linux';
            default:
                return 'unknown';
        }
    }

    /**
     * HTTP请求方法
     */
    httpRequest(url, options = {}) {
        return new Promise((resolve, reject) => {
            const urlObj = new URL(url);
            const client = urlObj.protocol === 'https:' ? https : http;

            const requestOptions = {
                hostname: urlObj.hostname,
                port: urlObj.port,
                path: urlObj.pathname + urlObj.search,
                method: options.method || 'GET',
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    ...options.headers
                },
                timeout: options.timeout || 10000
            };

            const req = client.request(requestOptions, (res) => {
                let data = '';

                res.on('data', (chunk) => {
                    data += chunk;
                });

                res.on('end', () => {
                    try {
                        const jsonData = JSON.parse(data);
                        resolve({ data: jsonData, status: res.statusCode });
                    } catch (error) {
                        resolve({ data: data, status: res.statusCode });
                    }
                });
            });

            req.on('error', (error) => {
                reject(error);
            });

            req.on('timeout', () => {
                req.destroy();
                reject(new Error('请求超时'));
            });

            if (options.data) {
                req.write(options.data);
            }

            req.end();
        });
    }

    /**
     * 解析蓝奏云真实下载链接 - 全新自动下载版本
     * 基于用户脚本的核心逻辑：进入页面后自动检查并触发下载
     */
    async parseLanzouUrl(lanzouUrl) {
        try {
            console.log('🔗 开始自动下载蓝奏云文件:', lanzouUrl);

            // 更新状态显示
            if (this.updateWindow) {
                this.updateWindow.webContents.send('update-status', '正在打开蓝奏云下载页面...');
            }

            // 使用全新的自动下载方式：直接在浏览器中执行自动下载脚本
            return await this.autoDownloadFromLanzhou(lanzouUrl);

        } catch (error) {
            console.error('❌ 蓝奏云自动下载失败:', error);
            throw new Error(`蓝奏云自动下载失败: ${error.message}`);
        }
    }

    /**
     * 全新的自动下载方法 - 基于用户脚本的核心逻辑
     * 直接在浏览器中执行自动下载脚本，模拟用户脚本的行为
     */
    async autoDownloadFromLanzhou(lanzouUrl) {
        return new Promise((resolve, reject) => {
            console.log('🚀 启动蓝奏云自动下载...');

            // 更新状态
            if (this.updateWindow) {
                this.updateWindow.webContents.send('update-status', '正在启动自动下载...');
            }

            // 创建一个隐藏的浏览器窗口来执行自动下载
            const { BrowserWindow } = require('electron');

            const downloadWindow = new BrowserWindow({
                width: 1200,
                height: 800,
                show: false, // 隐藏窗口
                webPreferences: {
                    nodeIntegration: false,
                    contextIsolation: true,
                    webSecurity: true
                }
            });

            let downloadStarted = false;
            let timeoutId = null;

            // 监听下载开始事件
            downloadWindow.webContents.session.on('will-download', (event, item, webContents) => {
                if (downloadStarted) return;
                downloadStarted = true;

                console.log('✅ 检测到下载开始:', item.getFilename());

                // 清除超时定时器
                if (timeoutId) {
                    clearTimeout(timeoutId);
                }

                // 更新状态
                if (this.updateWindow) {
                    this.updateWindow.webContents.send('update-status', '下载已开始...');
                }

                // 获取下载URL
                const downloadUrl = item.getURL();
                console.log('🔗 下载URL:', downloadUrl);

                // 关闭下载窗口
                downloadWindow.close();

                // 返回下载URL
                resolve(downloadUrl);
            });

            // 页面加载完成后注入自动下载脚本
            downloadWindow.webContents.once('dom-ready', () => {
                console.log('📄 页面加载完成，注入自动下载脚本...');

                // 更新状态
                if (this.updateWindow) {
                    this.updateWindow.webContents.send('update-status', '正在搜索下载链接...');
                }

                // 注入基于用户脚本的自动下载代码
                const autoDownloadScript = `
                    (function() {
                        'use strict';

                        console.log('【蓝奏云自动下载脚本】已启动，开始在后台搜索下载链接...');

                        // 设置一个定时器，持续检查下载链接是否已生成
                        const searchInterval = setInterval(() => {

                            // 查找下载按钮。选择器 '#tourl a[href]' 确保我们找到的是一个带链接的<a>元素
                            const downloadLink = document.querySelector('#tourl a[href]');

                            // 如果找到了有效的下载链接
                            if (downloadLink) {
                                console.log('【蓝奏云自动下载脚本】已找到目标链接，准备触发下载:', downloadLink.href);

                                // **关键步骤**：清除定时器。
                                // 找到链接后必须立即停止搜索，否则会无限次触发下载。
                                clearInterval(searchInterval);

                                // 触发下载。使用 location.href 是最可靠的方式。
                                window.location.href = downloadLink.href;
                            }
                        }, 250); // 每 250 毫秒（0.25秒）检查一次，这个频率足够快且不会造成负担

                        // 添加一个"保险丝"：如果页面加载后30秒还没找到链接，就自动停止脚本，避免无限运行。
                        setTimeout(() => {
                            // 如果30秒后，我们的定时器还在运行（说明还没找到链接），就强行停止它。
                            if (searchInterval) {
                                clearInterval(searchInterval);
                                console.warn('【蓝奏云自动下载脚本】超时：30秒内未找到下载链接。页面可能已更改或加载失败。');
                            }
                        }, 30000); // 30秒超时

                    })();
                `;

                // 执行自动下载脚本
                downloadWindow.webContents.executeJavaScript(autoDownloadScript)
                    .then(() => {
                        console.log('✅ 自动下载脚本注入成功');
                    })
                    .catch((error) => {
                        console.error('❌ 自动下载脚本注入失败:', error);
                        downloadWindow.close();
                        reject(new Error(`脚本注入失败: ${error.message}`));
                    });
            });

            // 设置总体超时（35秒）
            timeoutId = setTimeout(() => {
                if (!downloadStarted) {
                    console.warn('⚠️ 自动下载超时');
                    downloadWindow.close();
                    reject(new Error('自动下载超时，未能在35秒内找到下载链接'));
                }
            }, 35000);

            // 处理窗口关闭事件
            downloadWindow.on('closed', () => {
                if (!downloadStarted && timeoutId) {
                    clearTimeout(timeoutId);
                    reject(new Error('下载窗口被意外关闭'));
                }
            });

            // 加载蓝奏云页面
            console.log('🌐 加载蓝奏云页面:', lanzouUrl);
            downloadWindow.loadURL(lanzouUrl)
                .catch((error) => {
                    console.error('❌ 加载页面失败:', error);
                    downloadWindow.close();
                    reject(new Error(`加载页面失败: ${error.message}`));
                });
        });
    }





    /**
     * 检查更新
     * @param {boolean} silent - 是否静默检查
     * @returns {Promise<Object>} 更新信息
     */
    async checkForUpdates(silent = false) {
        try {
            console.log(`🔍 检查更新... 当前版本: ${this.currentVersion}`);

            // 首先尝试主API
            let response;
            try {
                const url = `${this.apiConfig.baseURL}${this.apiConfig.endpoints.check}&version=${this.currentVersion}&platform=${this.platform}`;
                console.log('🔗 请求URL:', url);
                response = await this.httpRequest(url, { timeout: 10000 });
            } catch (mainApiError) {
                console.warn('⚠️ 主API失败，尝试备用API:', mainApiError.message);

                // 尝试备用API
                const fallbackUrl = `${this.fallbackApiConfig.baseURL}${this.fallbackApiConfig.endpoints.check}&version=${this.currentVersion}&platform=${this.platform}`;
                console.log('🔗 备用URL:', fallbackUrl);
                response = await this.httpRequest(fallbackUrl, { timeout: 10000 });
            }

            console.log('📡 更新检查响应:', response.data);
            console.log('🔍 当前版本:', this.currentVersion);
            console.log('🔍 平台:', this.platform);

            if (response.data.success) {
                const data = response.data.data || response.data;
                console.log('📊 解析后的数据:', data);

                if (data.has_update && data.update_info) {
                    this.updateInfo = data.update_info;
                    this.updateInfo.version = data.latest_version; // 确保版本号正确
                    this.forceUpdate = data.update_info.force_update;

                    console.log(`✅ 发现新版本: ${data.latest_version}, 强制更新: ${this.forceUpdate}`);

                    if (!silent) {
                        await this.showUpdateDialog();
                    }

                    return {
                        hasUpdate: true,
                        updateInfo: this.updateInfo,
                        forceUpdate: this.forceUpdate
                    };
                } else {
                    console.log('✅ 已是最新版本');

                    if (!silent) {
                        dialog.showMessageBox({
                            type: 'info',
                            title: '检查更新',
                            message: '当前已是最新版本',
                            buttons: ['确定']
                        });
                    }

                    return { hasUpdate: false };
                }
            } else {
                throw new Error(response.data.message || '检查更新失败');
            }
        } catch (error) {
            console.error('❌ 检查更新失败:', error.message);

            if (!silent) {
                dialog.showErrorBox('检查更新失败', error.message);
            }

            return { hasUpdate: false, error: error.message };
        }
    }
    
    /**
     * 显示更新对话框
     */
    async showUpdateDialog() {
        if (!this.updateInfo) return;

        // 如果是强制更新，创建模态窗口
        if (this.forceUpdate) {
            await this.createForceUpdateWindow();
        } else {
            // 可选更新，显示对话框
            const result = await dialog.showMessageBox({
                type: 'question',
                title: '发现新版本',
                message: `发现新版本 ${this.updateInfo.version}`,
                detail: this.updateInfo.description || '建议您更新到最新版本以获得更好的体验。',
                buttons: ['立即更新', '稍后提醒'],
                defaultId: 0,
                cancelId: 1
            });

            if (result.response === 0) {
                await this.startUpdate();
            }
        }
    }


    
    /**
     * 创建强制更新窗口
     */
    async createForceUpdateWindow() {
        if (this.updateWindow) {
            this.updateWindow.focus();
            return;
        }

        // 获取当前活动窗口作为父窗口
        const parentWindow = this.getActiveWindow();

        const windowOptions = {
            width: 420,
            height: 500,
            resizable: false,
            minimizable: false,
            maximizable: false,
            closable: true, // 允许关闭
            alwaysOnTop: true,
            modal: false, // 完全非模态，不显示任何背景
            frame: false, // 无边框
            transparent: true, // 透明背景
            skipTaskbar: true, // 不在任务栏显示
            backgroundColor: 'rgba(0,0,0,0)', // 完全透明背景
            show: false, // 初始不显示，等待定位完成
            webPreferences: {
                nodeIntegration: true,
                contextIsolation: false,
                backgroundThrottling: false
            }
        };

        // 不设置parent属性，避免任何模态背景
        // 手动计算相对于父窗口的居中位置

        this.updateWindow = new BrowserWindow(windowOptions);
        
        // 加载更新页面
        const updateHtml = this.generateUpdateHTML();
        this.updateWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(updateHtml)}`);

        // 确保窗口正确定位并显示
        this.updateWindow.once('ready-to-show', () => {
            // 始终相对于父窗口居中，如果没有父窗口则屏幕居中
            if (parentWindow && !parentWindow.isDestroyed()) {
                this.centerRelativeToParent(this.updateWindow, parentWindow);
                console.log('🎯 更新弹窗相对于登录窗口居中显示');
            } else {
                this.updateWindow.center();
                console.log('🎯 更新弹窗屏幕居中显示');
            }
            this.updateWindow.show();
        });

        // 处理窗口关闭事件
        this.updateWindow.on('close', (event) => {
            if (this.forceUpdate) {
                console.log('🚪 用户关闭强制更新窗口，退出应用');
                app.quit();
            }
        });

        this.updateWindow.on('closed', () => {
            this.updateWindow = null;
        });

        // 监听IPC事件
        ipcMain.on('close-update-window', () => {
            if (this.updateWindow && !this.updateWindow.isDestroyed()) {
                this.updateWindow.close();
            }
        });

        // 监听网页下载事件
        ipcMain.on('open-web-download', (event, url) => {
            console.log('🌐 打开网页下载:', url);
            require('electron').shell.openExternal(url);
        });

        // 自动开始更新
        setTimeout(() => {
            this.startUpdate();
        }, 2000);
    }
    
    /**
     * 生成更新特性列表
     */
    generateUpdateFeatures() {
        // 只显示后台配置的特性列表，如果没有配置则不显示任何特性
        if (this.updateInfo?.features && Array.isArray(this.updateInfo.features) && this.updateInfo.features.length > 0) {
            return `
                <ul>
                    ${this.updateInfo.features.map(feature => `<li>${feature}</li>`).join('')}
                </ul>
            `;
        } else {
            // 如果后台没有配置特性列表，则不显示任何内容
            return '';
        }
    }

    /**
     * 生成更新页面HTML
     */
    generateUpdateHTML() {
        return `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>系统更新</title>
            <style>
                body {
                    margin: 0;
                    padding: 0;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    background: transparent !important;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    height: 100vh;
                    -webkit-app-region: no-drag;
                    overflow: hidden;
                }

                html {
                    background: transparent !important;
                }

                .update-container {
                    background: #ffffff;
                    border-radius: 20px;
                    padding: 0;
                    width: 400px;
                    box-shadow: none;
                    overflow: hidden;
                    position: relative;
                    border: none;
                }
                
                .close-button {
                    position: absolute;
                    top: 15px;
                    right: 15px;
                    width: 30px;
                    height: 30px;
                    border: none;
                    background: rgba(0, 0, 0, 0.1);
                    border-radius: 50%;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 16px;
                    color: white;
                    transition: all 0.3s ease;
                    z-index: 10;
                    box-shadow: none;
                }

                .close-button:hover {
                    background: rgba(0, 0, 0, 0.2);
                    transform: scale(1.1);
                    box-shadow: none;
                }

                .update-header {
                    background: linear-gradient(135deg, #ff6b9d 0%, #ff8fab 100%);
                    padding: 20px 30px;
                    text-align: center;
                    color: white;
                    position: relative;
                }
                
                .update-title {
                    font-size: 20px;
                    font-weight: 500;
                    margin: 0;
                    letter-spacing: 1px;
                }
                
                .update-body {
                    padding: 30px 40px;
                    text-align: center;
                }
                
                .update-description {
                    font-size: 16px;
                    line-height: 1.8;
                    margin-bottom: 30px;
                    color: #333333;
                }
                
                .update-features {
                    margin-bottom: 30px;
                }
                
                .update-features ul {
                    list-style: none;
                    padding: 0;
                    margin: 0;
                    text-align: left;
                    max-width: 300px;
                    margin: 0 auto;
                }
                
                .update-features li {
                    padding: 12px 0;
                    color: #333333;
                    font-size: 16px;
                    position: relative;
                    padding-left: 40px;
                    line-height: 1.5;
                }
                
                .update-features li:before {
                    content: '✓';
                    position: absolute;
                    left: 0;
                    top: 50%;
                    transform: translateY(-50%);
                    background: #4CAF50;
                    color: white;
                    width: 24px;
                    height: 24px;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 14px;
                    font-weight: bold;
                }
                
                .progress-container {
                    margin: 20px 0;
                    padding: 20px;
                    background: #f8f9fa;
                    border-radius: 12px;
                    border: 1px solid #e9ecef;
                }

                .progress-bar {
                    width: 100%;
                    height: 12px;
                    background: #e0e0e0;
                    border-radius: 6px;
                    overflow: hidden;
                    position: relative;
                    box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
                }

                .progress-fill {
                    height: 100%;
                    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
                    width: 0%;
                    transition: width 0.3s ease;
                    position: relative;
                    border-radius: 6px;
                }

                .progress-fill::after {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
                    animation: shimmer 2s infinite;
                }

                @keyframes shimmer {
                    0% { transform: translateX(-100%); }
                    100% { transform: translateX(100%); }
                }

                .progress-text {
                    margin-top: 12px;
                    font-size: 14px;
                    color: #495057;
                    font-weight: 500;
                    text-align: center;
                }

                .progress-details {
                    margin-top: 8px;
                    font-size: 12px;
                    color: #6c757d;
                    text-align: center;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }

                .download-speed {
                    color: #28a745;
                    font-weight: 500;
                }

                .file-size {
                    color: #6c757d;
                }
                
                .update-button {
                    background: linear-gradient(135deg, #7c4dff 0%, #9c27b0 100%);
                    color: white;
                    border: none;
                    padding: 15px 40px;
                    border-radius: 25px;
                    font-size: 16px;
                    font-weight: 500;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    min-width: 120px;
                }
                
                .update-button:hover {
                    background: linear-gradient(135deg, #6a3de8 0%, #8e24aa 100%);
                    transform: translateY(-2px);
                    box-shadow: none;
                }
                
                .update-button:disabled {
                    opacity: 0.6;
                    cursor: not-allowed;
                    transform: none;
                }
                
                .status-text {
                    margin: 15px 0;
                    font-size: 14px;
                    color: #666;
                }
            </style>
        </head>
        <body>
            <div class="update-container">
                <button class="close-button" onclick="closeUpdate()" title="关闭并退出应用">×</button>
                <div class="update-header">
                    <h3 class="update-title">${this.updateInfo?.title || '系统更新'}</h3>
                </div>
                <div class="update-body">
                    <div class="update-description">
                        ${this.updateInfo?.description || `发现新版本 ${this.updateInfo?.version || ''}，为了您的使用体验，请立即更新。`}
                    </div>
                    <div class="update-features">
                        ${this.generateUpdateFeatures()}
                    </div>
                    <div class="progress-container" id="progressContainer" style="display: none;">
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill"></div>
                        </div>
                        <div class="progress-text" id="progressText">准备下载...</div>
                        <div class="progress-details" id="progressDetails" style="display: none;">
                            <span class="download-speed" id="downloadSpeed">0 KB/s</span>
                            <span class="file-size" id="fileSize">0 MB / 0 MB</span>
                        </div>
                    </div>
                    <div class="status-text" id="statusText"></div>
                    <button class="update-button" id="updateButton" onclick="startUpdate()">
                        🚀 立即更新
                    </button>
                </div>
            </div>
            
            <script>
                const { ipcRenderer } = require('electron');
                let isUpdating = false;
                let startTime = null;

                function startUpdate() {
                    if (isUpdating) return;

                    isUpdating = true;
                    startTime = Date.now();

                    const updateButton = document.getElementById('updateButton');
                    const progressContainer = document.getElementById('progressContainer');
                    const statusText = document.getElementById('statusText');

                    updateButton.disabled = true;
                    updateButton.textContent = '⏳ 准备中...';
                    progressContainer.style.display = 'block';
                    statusText.textContent = '正在准备更新，请稍候...';

                    // 通知主进程开始更新
                    ipcRenderer.send('start-update');
                }

                function closeUpdate() {
                    if (isUpdating) {
                        const confirmed = confirm('更新正在进行中，确定要关闭吗？这可能会导致应用无法正常使用。');
                        if (!confirmed) return;
                    }
                    // 通知主进程关闭更新窗口
                    ipcRenderer.send('close-update-window');
                }

                function formatBytes(bytes) {
                    if (bytes === 0) return '0 B';
                    const k = 1024;
                    const sizes = ['B', 'KB', 'MB', 'GB'];
                    const i = Math.floor(Math.log(bytes) / Math.log(k));
                    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
                }

                function formatSpeed(bytesPerSecond) {
                    return formatBytes(bytesPerSecond) + '/s';
                }

                function formatTime(seconds) {
                    if (seconds < 60) return Math.round(seconds) + '秒';
                    const minutes = Math.floor(seconds / 60);
                    const remainingSeconds = Math.round(seconds % 60);
                    return minutes + '分' + (remainingSeconds > 0 ? remainingSeconds + '秒' : '');
                }

                // 监听更新进度
                ipcRenderer.on('update-progress', (event, progress) => {
                    const progressFill = document.getElementById('progressFill');
                    const progressText = document.getElementById('progressText');
                    const progressDetails = document.getElementById('progressDetails');
                    const downloadSpeed = document.getElementById('downloadSpeed');
                    const fileSize = document.getElementById('fileSize');

                    const percent = Math.round(progress.percent || 0);
                    progressFill.style.width = percent + '%';

                    if (progress.transferred && progress.total) {
                        const transferred = formatBytes(progress.transferred);
                        const total = formatBytes(progress.total);
                        fileSize.textContent = \`\${transferred} / \${total}\`;

                        // 计算下载速度
                        if (startTime && progress.transferred > 0) {
                            const elapsed = (Date.now() - startTime) / 1000;
                            const speed = progress.transferred / elapsed;
                            downloadSpeed.textContent = formatSpeed(speed);

                            // 计算剩余时间
                            const remaining = (progress.total - progress.transferred) / speed;
                            const eta = formatTime(remaining);
                            progressText.textContent = \`下载中... \${percent}% (剩余 \${eta})\`;
                        } else {
                            progressText.textContent = \`下载中... \${percent}%\`;
                        }

                        progressDetails.style.display = 'flex';
                    } else {
                        progressText.textContent = \`下载中... \${percent}%\`;
                        progressDetails.style.display = 'none';
                    }
                });

                // 监听更新状态
                ipcRenderer.on('update-status', (event, status) => {
                    const statusText = document.getElementById('statusText');
                    const updateButton = document.getElementById('updateButton');

                    statusText.textContent = status;

                    // 根据状态更新按钮文本
                    if (status.includes('解析')) {
                        updateButton.textContent = '🔍 解析中...';
                    } else if (status.includes('下载')) {
                        updateButton.textContent = '📥 下载中...';
                    } else if (status.includes('安装')) {
                        updateButton.textContent = '⚙️ 安装中...';
                    }
                });

                // 监听更新完成
                ipcRenderer.on('update-complete', (event) => {
                    const statusText = document.getElementById('statusText');
                    const updateButton = document.getElementById('updateButton');
                    const progressText = document.getElementById('progressText');

                    statusText.textContent = '✅ 更新完成，正在重启应用...';
                    updateButton.textContent = '🎉 更新完成';
                    progressText.textContent = '安装完成 100%';

                    // 3秒后自动重启
                    setTimeout(() => {
                        statusText.textContent = '正在重启应用...';
                    }, 3000);
                    document.getElementById('updateButton').disabled = false;
                    document.getElementById('updateButton').onclick = () => {
                        ipcRenderer.send('restart-app');
                    };
                });
                
                // 监听更新错误
                ipcRenderer.on('update-error', (event, error) => {
                    isUpdating = false;
                    const statusText = document.getElementById('statusText');
                    const updateButton = document.getElementById('updateButton');
                    const progressContainer = document.getElementById('progressContainer');

                    statusText.innerHTML = \`❌ 更新失败: \${error}<br><small style="color: #666; margin-top: 8px; display: block;">请检查网络连接后重试</small>\`;
                    updateButton.textContent = '🔄 重试更新';
                    updateButton.disabled = false;
                    updateButton.onclick = startUpdate;

                    // 隐藏进度条
                    progressContainer.style.display = 'none';
                });

                // 监听更新错误（带网页下载选项）
                ipcRenderer.on('update-error-with-web', (event, data) => {
                    isUpdating = false;
                    const statusText = document.getElementById('statusText');
                    const updateButton = document.getElementById('updateButton');
                    const progressContainer = document.getElementById('progressContainer');

                    statusText.innerHTML =
                        \`❌ 自动更新失败: \${data.error}<br><br>\` +
                        \`<div style="margin: 15px 0;">\` +
                        \`<button onclick="openWebDownload('\${data.webUrl}')" style="background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%); color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; margin-right: 10px; font-size: 14px; font-weight: 500; transition: all 0.3s ease;">🌐 网页下载</button>\` +
                        \`<button onclick="startUpdate()" style="background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%); color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-size: 14px; font-weight: 500; transition: all 0.3s ease;">🔄 重试</button>\` +
                        \`</div>\` +
                        \`<small style="color: #666; display: block; margin-top: 10px;">建议使用网页下载获得更稳定的下载体验</small>\`;

                    updateButton.style.display = 'none';
                    progressContainer.style.display = 'none';
                });

                // 打开网页下载
                function openWebDownload(url) {
                    console.log('打开网页下载:', url);
                    ipcRenderer.send('open-web-download', url);

                    // 显示提示信息
                    const statusText = document.getElementById('statusText');
                    statusText.innerHTML += \`<br><div style="color: #28a745; margin-top: 10px;">✅ 已在浏览器中打开下载页面</div>\`;
                }

                // 自动开始更新（延迟2秒让用户看到界面）
                setTimeout(() => {
                    if (!isUpdating) {
                        startUpdate();
                    }
                }, 2000);
            </script>
        </body>
        </html>
        `;
    }

    /**
     * 开始更新 - 增强版（带重试机制）
     */
    async startUpdate(retryCount = 0) {
        const maxRetries = 0; // 只尝试一次，不重试

        if (!this.updateInfo) {
            console.error('❌ 没有更新信息');
            return;
        }

        try {
            this.isDownloading = true;

            // 显示重试信息
            if (retryCount > 0) {
                console.log(`🔄 第 ${retryCount + 1} 次尝试更新...`);
                if (this.updateWindow) {
                    this.updateWindow.webContents.send('update-status', `正在重试更新... (${retryCount + 1}/${maxRetries + 1})`);
                }
            }

            // 获取下载链接
            let downloadUrl = this.getDownloadUrl();
            if (!downloadUrl) {
                throw new Error('没有找到适合当前平台的下载链接');
            }

            console.log(`📥 开始下载更新: ${downloadUrl}`);

            // 如果是蓝奏云链接，解析真实下载地址
            if (downloadUrl.includes('lanzoue.com') || downloadUrl.includes('lanzou')) {
                console.log('🔗 检测到蓝奏云链接，正在解析真实下载地址...');
                if (this.updateWindow) {
                    this.updateWindow.webContents.send('update-status', '正在解析下载链接...');
                }
                downloadUrl = await this.parseLanzouUrl(downloadUrl);
            }

            // 创建临时目录
            const tempDir = path.join(os.tmpdir(), 'xiaomeihua-update');
            if (!fs.existsSync(tempDir)) {
                fs.mkdirSync(tempDir, { recursive: true });
            }

            // 确定文件扩展名
            const fileExt = this.platform === 'windows' ? '.exe' : '.dmg';
            const fileName = `xiaomeihua-${this.updateInfo.version}${fileExt}`;
            const filePath = path.join(tempDir, fileName);

            // 下载文件（只尝试一次）
            await this.downloadFileWithRetry(downloadUrl, filePath, 0);

            console.log(`✅ 下载完成: ${filePath}`);

            // 更新状态：下载完成
            if (this.updateWindow) {
                this.updateWindow.webContents.send('update-status', '下载完成，正在验证文件...');
            }

            // 验证下载的文件
            if (!this.validateDownloadedFile(filePath)) {
                throw new Error('下载的文件验证失败，可能已损坏');
            }

            console.log('✅ 文件验证通过，开始安装...');

            // 更新状态：开始安装
            if (this.updateWindow) {
                this.updateWindow.webContents.send('update-status', '文件验证通过，正在安装...');
            }

            // 开始安装
            await this.installUpdate(filePath);

        } catch (error) {
            console.error(`❌ 更新失败 (尝试 ${retryCount + 1}/${maxRetries + 1}):`, error.message);
            this.isDownloading = false;

            // 如果还有重试次数，则自动重试
            if (retryCount < maxRetries) {
                console.log(`⏳ ${3}秒后自动重试...`);
                if (this.updateWindow) {
                    this.updateWindow.webContents.send('update-status', `更新失败，${3}秒后自动重试... (${retryCount + 1}/${maxRetries + 1})`);
                }

                setTimeout(() => {
                    this.startUpdate(retryCount + 1);
                }, 3000);
                return;
            }

            // 所有重试都失败了，显示最终错误
            console.error('❌ 所有重试都失败了，更新终止');

            // 尝试提供网页下载选项
            const webUrl = this.getWebDownloadUrl();
            if (webUrl && this.updateWindow) {
                this.updateWindow.webContents.send('update-error-with-web', {
                    error: `更新失败 (已重试${maxRetries}次): ${error.message}`,
                    webUrl: webUrl
                });
            } else if (this.updateWindow) {
                this.updateWindow.webContents.send('update-error', `更新失败 (已重试${maxRetries}次): ${error.message}`);
            }
        }
    }

    /**
     * 获取下载链接
     */
    getDownloadUrl() {
        if (!this.updateInfo || !this.updateInfo.download_urls) {
            return null;
        }

        const urls = this.updateInfo.download_urls;

        switch (this.platform) {
            case 'windows':
                return urls.windows || urls.windows_direct;
            case 'macos':
                return urls.macos || urls.macos_direct;
            default:
                return null;
        }
    }

    /**
     * 获取网页下载链接
     */
    getWebDownloadUrl() {
        if (!this.updateInfo || !this.updateInfo.download_urls) {
            return null;
        }

        const urls = this.updateInfo.download_urls;

        switch (this.platform) {
            case 'windows':
                return urls.windows_web || urls.windows;
            case 'macos':
                return urls.macos_web || urls.macos;
            default:
                return null;
        }
    }

    /**
     * 带重试的下载文件
     */
    async downloadFileWithRetry(url, filePath, maxRetries = 0) { // 只尝试一次，不重试
        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                if (attempt > 0) {
                    console.log(`🔄 下载重试 ${attempt}/${maxRetries}...`);
                    if (this.updateWindow) {
                        this.updateWindow.webContents.send('update-status', `下载重试中... (${attempt}/${maxRetries})`);
                    }
                }

                await this.downloadFile(url, filePath);
                return; // 下载成功，退出重试循环
            } catch (error) {
                console.warn(`⚠️ 下载尝试 ${attempt + 1} 失败:`, error.message);

                if (attempt === maxRetries) {
                    throw error; // 最后一次尝试失败，抛出错误
                }

                // 删除可能的部分下载文件
                if (fs.existsSync(filePath)) {
                    try {
                        fs.unlinkSync(filePath);
                    } catch (deleteError) {
                        console.warn('⚠️ 删除部分下载文件失败:', deleteError.message);
                    }
                }

                // 等待一段时间再重试
                await new Promise(resolve => setTimeout(resolve, 2000 * (attempt + 1)));
            }
        }
    }

    /**
     * 验证下载的文件
     */
    validateDownloadedFile(filePath) {
        try {
            if (!fs.existsSync(filePath)) {
                console.error('❌ 文件不存在:', filePath);
                return false;
            }

            const stats = fs.statSync(filePath);
            const fileSizeMB = stats.size / (1024 * 1024);

            console.log(`📊 文件大小: ${fileSizeMB.toFixed(2)} MB`);

            // 检查文件大小（至少应该大于1MB）
            if (stats.size < 1024 * 1024) {
                console.error('❌ 文件大小异常，可能下载不完整');
                return false;
            }

            // 检查文件扩展名
            const expectedExt = this.platform === 'windows' ? '.exe' : '.dmg';
            if (!filePath.endsWith(expectedExt)) {
                console.error(`❌ 文件扩展名不正确，期望: ${expectedExt}`);
                return false;
            }

            console.log('✅ 文件验证通过');
            return true;
        } catch (error) {
            console.error('❌ 文件验证失败:', error.message);
            return false;
        }
    }

    /**
     * 下载文件
     */
    async downloadFile(url, filePath, maxRedirects = 5) {
        return new Promise((resolve, reject) => {
            const writer = fs.createWriteStream(filePath);
            const urlObj = new URL(url);
            const client = urlObj.protocol === 'https:' ? https : http;

            const options = {
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                }
            };

            const req = client.get(url, options, (res) => {
                // 处理重定向
                if (res.statusCode >= 300 && res.statusCode < 400 && res.headers.location) {
                    if (maxRedirects > 0) {
                        console.log(`🔄 重定向到: ${res.headers.location}`);
                        writer.destroy();
                        return this.downloadFile(res.headers.location, filePath, maxRedirects - 1)
                            .then(resolve)
                            .catch(reject);
                    } else {
                        writer.destroy();
                        return reject(new Error('重定向次数过多'));
                    }
                }

                if (res.statusCode !== 200) {
                    writer.destroy();
                    return reject(new Error(`下载失败，状态码: ${res.statusCode}`));
                }

                const totalSize = parseInt(res.headers['content-length'], 10);
                let downloadedSize = 0;

                res.on('data', (chunk) => {
                    downloadedSize += chunk.length;
                    const percent = totalSize > 0 ? Math.round((downloadedSize * 100) / totalSize) : 0;
                    this.downloadProgress = percent;

                    console.log(`📥 下载进度: ${percent}%`);

                    if (this.updateWindow) {
                        this.updateWindow.webContents.send('update-progress', {
                            percent: percent,
                            loaded: downloadedSize,
                            total: totalSize
                        });
                    }
                });

                res.pipe(writer);

                writer.on('finish', () => {
                    console.log('📥 文件下载完成');
                    resolve();
                });

                writer.on('error', (error) => {
                    console.error('❌ 文件写入失败:', error);
                    reject(error);
                });

            }).on('error', (error) => {
                console.error('❌ 下载请求失败:', error);
                writer.destroy();
                reject(error);
            });

            req.setTimeout(30000, () => {
                req.destroy();
                writer.destroy();
                reject(new Error('下载超时'));
            });
        });
    }

    /**
     * 安装更新
     */
    async installUpdate(filePath) {
        try {
            this.isInstalling = true;

            if (this.updateWindow) {
                this.updateWindow.webContents.send('update-status', '正在安装更新...');
            }

            console.log(`🔧 开始安装更新: ${filePath}`);

            if (this.platform === 'windows') {
                await this.installWindowsUpdate(filePath);
            } else if (this.platform === 'macos') {
                await this.installMacUpdate(filePath);
            } else {
                throw new Error('不支持的平台');
            }

            console.log('✅ 安装完成');

            if (this.updateWindow) {
                this.updateWindow.webContents.send('update-complete');
            }

        } catch (error) {
            console.error('❌ 安装失败:', error.message);
            this.isInstalling = false;

            // 更新状态：安装失败
            if (this.updateWindow) {
                this.updateWindow.webContents.send('update-status', `安装失败: ${error.message}`);
            }

            throw error;
        }
    }

    /**
     * 安装Windows更新 - 增强版
     */
    async installWindowsUpdate(filePath) {
        return new Promise((resolve, reject) => {
            console.log('🔧 开始Windows安装...');

            // 更新状态
            if (this.updateWindow) {
                this.updateWindow.webContents.send('update-status', '正在安装Windows更新包...');
            }

            // 尝试多种安装参数
            const installParams = [
                ['/S', '/SILENT', '/VERYSILENT'],  // 静默安装
                ['/S'],                            // 基本静默
                ['/SILENT'],                       // 另一种静默方式
                []                                 // 默认安装
            ];

            let currentAttempt = 0;

            const tryInstall = () => {
                if (currentAttempt >= installParams.length) {
                    reject(new Error('所有安装方式都失败了'));
                    return;
                }

                const params = installParams[currentAttempt];
                console.log(`🔧 尝试安装方式 ${currentAttempt + 1}:`, params);

                const installer = spawn(filePath, params, {
                    detached: true,
                    stdio: 'pipe'
                });

                let installOutput = '';
                let errorOutput = '';

                installer.stdout?.on('data', (data) => {
                    installOutput += data.toString();
                });

                installer.stderr?.on('data', (data) => {
                    errorOutput += data.toString();
                });

                installer.on('close', (code) => {
                    console.log(`🔧 安装结果: 退出代码=${code}`);
                    if (installOutput) console.log('📄 安装输出:', installOutput);
                    if (errorOutput) console.log('📄 错误输出:', errorOutput);

                    if (code === 0) {
                        console.log('✅ Windows安装完成');

                        // 更新状态
                        if (this.updateWindow) {
                            this.updateWindow.webContents.send('update-status', '安装完成，准备重启应用...');
                        }

                        // 延迟3秒后自动重启应用
                        setTimeout(() => {
                            this.restartApplication();
                        }, 3000);

                        resolve();
                    } else {
                        console.warn(`⚠️ 安装方式 ${currentAttempt + 1} 失败，退出代码: ${code}`);
                        currentAttempt++;
                        tryInstall();
                    }
                });

                installer.on('error', (error) => {
                    console.warn(`⚠️ 安装方式 ${currentAttempt + 1} 启动失败:`, error.message);
                    currentAttempt++;
                    tryInstall();
                });

                // 设置超时
                setTimeout(() => {
                    installer.kill();
                    console.warn(`⚠️ 安装方式 ${currentAttempt + 1} 超时`);
                    currentAttempt++;
                    tryInstall();
                }, 60000); // 60秒超时

                // 分离进程，避免阻塞
                installer.unref();
            };

            tryInstall();
        });
    }

    /**
     * 安装Mac更新
     */
    async installMacUpdate(filePath) {
        return new Promise((resolve, reject) => {
            console.log('🔧 开始挂载DMG文件...');

            // 更新状态：准备安装
            if (this.updateWindow) {
                this.updateWindow.webContents.send('update-status', '正在准备安装DMG文件...');
            }

            // 检查文件是否存在
            if (!fs.existsSync(filePath)) {
                return reject(new Error('安装文件不存在'));
            }

            // 检查文件大小
            const stats = fs.statSync(filePath);
            console.log(`📊 DMG文件大小: ${(stats.size / 1024 / 1024).toFixed(2)}MB`);
            if (stats.size < 1024 * 1024) { // 小于1MB可能是错误文件
                return reject(new Error('安装文件大小异常，可能下载不完整'));
            }

            // 更新状态：挂载DMG
            if (this.updateWindow) {
                this.updateWindow.webContents.send('update-status', '正在挂载DMG文件...');
            }

            // 挂载DMG文件
            const mountProcess = spawn('hdiutil', ['attach', filePath, '-nobrowse', '-quiet'], {
                stdio: ['ignore', 'pipe', 'pipe']
            });

            let mountOutput = '';
            let errorOutput = '';

            mountProcess.stdout.on('data', (data) => {
                mountOutput += data.toString();
            });

            mountProcess.stderr.on('data', (data) => {
                errorOutput += data.toString();
            });

            mountProcess.on('close', (code) => {
                console.log(`🔧 DMG挂载结果: 退出代码=${code}`);
                if (errorOutput) {
                    console.log(`🔧 错误输出: ${errorOutput}`);
                }

                if (code === 0) {
                    // 解析挂载点
                    const mountPoint = this.parseMountPoint(mountOutput);
                    if (mountPoint) {
                        console.log(`✅ DMG挂载成功: ${mountPoint}`);

                        // 更新状态：DMG挂载成功
                        if (this.updateWindow) {
                            this.updateWindow.webContents.send('update-status', 'DMG挂载成功，正在复制应用...');
                        }

                        this.copyMacApp(mountPoint).then(resolve).catch(reject);
                    } else {
                        reject(new Error('无法解析DMG挂载点'));
                    }
                } else {
                    reject(new Error(`挂载DMG失败，退出代码: ${code}，错误: ${errorOutput}`));
                }
            });

            // 设置超时
            setTimeout(() => {
                mountProcess.kill();
                reject(new Error('DMG挂载超时'));
            }, 30000);
        });
    }

    /**
     * 解析Mac DMG挂载点
     */
    parseMountPoint(output) {
        const lines = output.split('\n');
        for (const line of lines) {
            if (line.includes('/Volumes/')) {
                const match = line.match(/\/Volumes\/[^\s]+/);
                if (match) {
                    return match[0];
                }
            }
        }
        return null;
    }

    /**
     * 复制Mac应用
     */
    async copyMacApp(mountPoint) {
        return new Promise((resolve, reject) => {
            try {
                console.log(`🔍 查找应用文件在: ${mountPoint}`);

                // 查找.app文件
                const files = fs.readdirSync(mountPoint);
                console.log(`📁 挂载点文件列表: ${files.join(', ')}`);

                const appFiles = files.filter(file => file.endsWith('.app'));

                if (appFiles.length === 0) {
                    // 卸载DMG
                    spawn('hdiutil', ['detach', mountPoint], { stdio: 'ignore' });
                    reject(new Error('DMG中没有找到.app文件'));
                    return;
                }

                const appName = appFiles[0];
                const sourcePath = path.join(mountPoint, appName);
                const targetPath = path.join('/Applications', appName);

                console.log(`📋 准备复制: ${sourcePath} -> ${targetPath}`);

                // 更新状态：准备复制应用
                if (this.updateWindow) {
                    this.updateWindow.webContents.send('update-status', '正在准备复制应用到Applications文件夹...');
                }

                // 如果目标已存在，先删除
                if (fs.existsSync(targetPath)) {
                    console.log('🗑️ 删除旧版本应用...');

                    // 更新状态：删除旧版本
                    if (this.updateWindow) {
                        this.updateWindow.webContents.send('update-status', '正在删除旧版本应用...');
                    }

                    const removeProcess = spawn('rm', ['-rf', targetPath], { stdio: 'pipe' });

                    removeProcess.on('close', (removeCode) => {
                        if (removeCode === 0) {
                            this.performCopy(sourcePath, targetPath, mountPoint, resolve, reject);
                        } else {
                            // 卸载DMG
                            spawn('hdiutil', ['detach', mountPoint], { stdio: 'ignore' });
                            reject(new Error(`删除旧版本失败，退出代码: ${removeCode}`));
                        }
                    });
                } else {
                    this.performCopy(sourcePath, targetPath, mountPoint, resolve, reject);
                }

            } catch (error) {
                // 卸载DMG
                spawn('hdiutil', ['detach', mountPoint], { stdio: 'ignore' });
                reject(error);
            }
        });
    }

    /**
     * 执行复制操作 - 增强版
     */
    performCopy(sourcePath, targetPath, mountPoint, resolve, reject) {
        console.log('📋 开始复制应用...');

        // 更新状态
        if (this.updateWindow) {
            this.updateWindow.webContents.send('update-status', '正在安装新版本应用...');
        }

        // 复制应用
        const copyProcess = spawn('cp', ['-R', sourcePath, '/Applications'], {
            stdio: ['ignore', 'pipe', 'pipe']
        });

        let copyOutput = '';
        let errorOutput = '';

        copyProcess.stdout?.on('data', (data) => {
            copyOutput += data.toString();
        });

        copyProcess.stderr.on('data', (data) => {
            errorOutput += data.toString();
        });

        copyProcess.on('close', (code) => {
            // 卸载DMG
            console.log('🔄 卸载DMG...');
            const detachProcess = spawn('hdiutil', ['detach', mountPoint], { stdio: 'ignore' });

            detachProcess.on('close', () => {
                console.log('✅ DMG已卸载');
            });

            if (code === 0) {
                console.log('✅ Mac应用复制完成');
                console.log('📄 复制输出:', copyOutput);

                // 更新状态：复制完成
                if (this.updateWindow) {
                    this.updateWindow.webContents.send('update-status', '应用复制完成，正在验证安装...');
                }

                // 验证安装是否成功
                if (fs.existsSync(targetPath)) {
                    console.log('✅ 应用安装验证成功');

                    // 更新状态：安装成功
                    if (this.updateWindow) {
                        this.updateWindow.webContents.send('update-status', '安装成功！正在设置权限...');
                    }

                    // 设置应用权限
                    this.setAppPermissions(targetPath).then(() => {
                        // 更新状态：准备重启
                        if (this.updateWindow) {
                            this.updateWindow.webContents.send('update-status', '安装完成！3秒后自动重启应用...');
                        }

                        // 延迟3秒后自动重启应用
                        setTimeout(() => {
                            this.restartApplication();
                        }, 3000);

                        resolve();
                    }).catch((permError) => {
                        console.warn('⚠️ 设置应用权限失败:', permError.message);
                        // 即使权限设置失败，也继续重启
                        setTimeout(() => {
                            this.restartApplication();
                        }, 3000);

                        resolve();
                    });
                } else {
                    reject(new Error('应用安装验证失败：目标文件不存在'));
                }
            } else {
                console.error('❌ 复制失败，错误输出:', errorOutput);
                reject(new Error(`复制应用失败，退出代码: ${code}，错误: ${errorOutput}`));
            }
        });

        copyProcess.on('error', (error) => {
            // 卸载DMG
            spawn('hdiutil', ['detach', mountPoint], { stdio: 'ignore' });
            reject(new Error(`启动复制进程失败: ${error.message}`));
        });

        // 设置超时
        setTimeout(() => {
            copyProcess.kill();
            spawn('hdiutil', ['detach', mountPoint], { stdio: 'ignore' });
            reject(new Error('复制应用超时'));
        }, 60000);
    }

    /**
     * 设置应用权限
     */
    async setAppPermissions(appPath) {
        return new Promise((resolve, reject) => {
            console.log('🔐 设置应用权限...');

            const chmodProcess = spawn('chmod', ['-R', '755', appPath], {
                stdio: 'pipe'
            });

            let errorOutput = '';
            chmodProcess.stderr.on('data', (data) => {
                errorOutput += data.toString();
            });

            chmodProcess.on('close', (code) => {
                if (code === 0) {
                    console.log('✅ 应用权限设置完成');
                    resolve();
                } else {
                    reject(new Error(`设置权限失败，退出代码: ${code}，错误: ${errorOutput}`));
                }
            });

            chmodProcess.on('error', (error) => {
                reject(new Error(`启动权限设置进程失败: ${error.message}`));
            });

            // 设置超时
            setTimeout(() => {
                chmodProcess.kill();
                reject(new Error('设置权限超时'));
            }, 10000);
        });
    }

    /**
     * 重启应用 - 增强版
     */
    restartApp() {
        console.log('🔄 重启应用...');

        // 关闭更新窗口
        if (this.updateWindow) {
            this.updateWindow.close();
        }

        // 重启应用
        app.relaunch();
        app.exit(0);
    }

    /**
     * 重启应用（新方法名）
     */
    restartApplication() {
        console.log('🔄 准备重启应用...');

        // 更新状态
        if (this.updateWindow) {
            this.updateWindow.webContents.send('update-status', '正在重启应用...');
        }

        // 清理临时文件
        this.cleanupTempFiles().then(() => {
            console.log('✅ 临时文件清理完成');
        }).catch((error) => {
            console.warn('⚠️ 清理临时文件失败:', error.message);
        }).finally(() => {
            // 延迟1秒后重启，确保用户能看到完成状态
            setTimeout(() => {
                this.restartApp();
            }, 1000);
        });
    }

    /**
     * 清理临时文件（同步版本）
     */
    cleanup() {
        try {
            const tempDir = path.join(os.tmpdir(), 'xiaomeihua-update');
            if (fs.existsSync(tempDir)) {
                fs.rmSync(tempDir, { recursive: true, force: true });
                console.log('🧹 临时文件清理完成');
            }
        } catch (error) {
            console.error('❌ 清理临时文件失败:', error.message);
        }
    }

    /**
     * 清理临时文件（异步版本）
     */
    async cleanupTempFiles() {
        return new Promise((resolve, reject) => {
            try {
                const tempDir = path.join(os.tmpdir(), 'xiaomeihua-update');

                if (!fs.existsSync(tempDir)) {
                    console.log('📁 临时目录不存在，无需清理');
                    resolve();
                    return;
                }

                console.log('🧹 开始清理临时文件...');

                // 获取目录中的所有文件
                const files = fs.readdirSync(tempDir);
                console.log(`📁 找到 ${files.length} 个临时文件`);

                // 删除所有文件
                for (const file of files) {
                    const filePath = path.join(tempDir, file);
                    try {
                        const stats = fs.statSync(filePath);
                        if (stats.isDirectory()) {
                            fs.rmSync(filePath, { recursive: true, force: true });
                        } else {
                            fs.unlinkSync(filePath);
                        }
                        console.log(`🗑️ 已删除: ${file}`);
                    } catch (fileError) {
                        console.warn(`⚠️ 删除文件失败: ${file}`, fileError.message);
                    }
                }

                // 删除临时目录
                try {
                    fs.rmdirSync(tempDir);
                    console.log('✅ 临时目录清理完成');
                } catch (dirError) {
                    console.warn('⚠️ 删除临时目录失败:', dirError.message);
                }

                resolve();
            } catch (error) {
                console.error('❌ 清理临时文件失败:', error.message);
                reject(error);
            }
        });
    }

    /**
     * 获取当前活动窗口
     */
    getActiveWindow() {
        const allWindows = BrowserWindow.getAllWindows();

        // 优先返回可见且聚焦的窗口
        for (const window of allWindows) {
            if (window.isVisible() && window.isFocused()) {
                return window;
            }
        }

        // 如果没有聚焦的窗口，返回第一个可见窗口
        for (const window of allWindows) {
            if (window.isVisible()) {
                return window;
            }
        }

        // 如果都不可见，返回第一个窗口
        return allWindows.length > 0 ? allWindows[0] : null;
    }

    /**
     * 相对于父窗口居中定位子窗口
     */
    centerRelativeToParent(childWindow, parentWindow) {
        try {
            const parentBounds = parentWindow.getBounds();
            const childBounds = childWindow.getBounds();

            // 计算居中位置
            const x = Math.round(parentBounds.x + (parentBounds.width - childBounds.width) / 2);
            const y = Math.round(parentBounds.y + (parentBounds.height - childBounds.height) / 2);

            // 确保窗口不会超出屏幕边界
            const { screen } = require('electron');
            const display = screen.getDisplayNearestPoint({ x: parentBounds.x, y: parentBounds.y });
            const workArea = display.workArea;

            const finalX = Math.max(workArea.x, Math.min(x, workArea.x + workArea.width - childBounds.width));
            const finalY = Math.max(workArea.y, Math.min(y, workArea.y + workArea.height - childBounds.height));

            childWindow.setPosition(finalX, finalY);
            console.log(`🎯 更新弹窗相对于父窗口居中: x=${finalX}, y=${finalY}`);
            console.log(`📐 父窗口位置: x=${parentBounds.x}, y=${parentBounds.y}, w=${parentBounds.width}, h=${parentBounds.height}`);
            console.log(`📐 子窗口尺寸: w=${childBounds.width}, h=${childBounds.height}`);
        } catch (error) {
            console.error('❌ 相对于父窗口居中失败:', error.message);
            // 失败时回退到屏幕居中
            childWindow.center();
        }
    }
}

module.exports = UpdateManager;
