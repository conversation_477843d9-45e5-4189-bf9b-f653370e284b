/**
 * APP协议管理器
 * 处理用户协议的显示和管理
 */

const { BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const Store = require('electron-store');
const AppSettingsAPI = require('./app-settings-api');

class AgreementManager {
    constructor() {
        this.store = new Store({
            name: 'agreement-manager',
            encryptionKey: 'xiaomeihua-agreement-2025'
        });
        
        this.api = new AppSettingsAPI();
        this.agreementWindow = null;
        this.currentAgreement = null;
        
        this.initializeIPC();
    }
    
    /**
     * 初始化IPC通信
     */
    initializeIPC() {
        // 显示协议窗口
        ipcMain.handle('show-agreement', async () => {
            return await this.showAgreement();
        });
        
        // 获取当前协议
        ipcMain.handle('get-current-agreement', () => {
            return this.currentAgreement;
        });
        
        // 协议同意
        ipcMain.handle('agreement-accept', async (event, agreementId) => {
            this.recordAgreementAction(agreementId, 'accept');
            this.closeAgreement();
            return true;
        });
        
        // 协议拒绝
        ipcMain.handle('agreement-decline', async (event, agreementId) => {
            this.recordAgreementAction(agreementId, 'decline');
            this.closeAgreement();
            return false;
        });

        // 关闭协议窗口
        ipcMain.handle('close-agreement-window', () => {
            this.closeAgreement();
            return true;
        });
        
        // 检查是否需要显示协议
        ipcMain.handle('check-agreement-required', async () => {
            return await this.isAgreementRequired();
        });
    }
    
    /**
     * 检查是否需要显示协议
     * @returns {Promise<boolean>} 是否需要显示协议
     */
    async isAgreementRequired() {
        try {
            // 使用更可靠的getAgreement方法，它有备用机制
            const result = await this.api.getAgreement('privacy');
            if (!result.success || !result.agreement) {
                return false;
            }

            const agreement = result.agreement;
            const agreementHistory = this.store.get('agreementHistory', {});
            const agreementId = agreement.id.toString();
            
            // 检查用户是否已经同意过此版本的协议
            const userAgreement = agreementHistory[agreementId];
            if (userAgreement && userAgreement.status === 'accepted') {
                return false;
            }
            
            return true;
        } catch (error) {
            console.error('检查协议要求失败:', error);
            return false;
        }
    }
    
    /**
     * 显示协议窗口
     * @returns {Promise<boolean>} 用户是否同意协议
     */
    async showAgreement() {
        try {
            // 使用更可靠的getAgreement方法，它有备用机制
            const result = await this.api.getAgreement('privacy');
            if (!result.success || !result.agreement) {
                console.log('没有需要显示的协议');
                return true; // 没有协议时默认同意
            }

            this.currentAgreement = result.agreement;
            
            // 如果已经有协议窗口在显示，先关闭
            if (this.agreementWindow && !this.agreementWindow.isDestroyed()) {
                this.agreementWindow.close();
            }
            
            // 创建协议窗口 - 调整为更大的尺寸
            this.agreementWindow = new BrowserWindow({
                width: 900,
                height: 700,
                resizable: true,
                maximizable: true,
                minimizable: true,
                modal: false,
                frame: false,
                show: false,
                title: '用户协议',
                webPreferences: {
                    nodeIntegration: false,
                    contextIsolation: true,
                    preload: path.join(__dirname, 'agreement-preload.js')
                }
            });
            
            // 加载协议HTML
            await this.agreementWindow.loadFile(path.join(__dirname, 'renderer', 'agreement.html'));
            
            // 窗口准备好后显示
            this.agreementWindow.once('ready-to-show', () => {
                this.agreementWindow.show();
                this.agreementWindow.center();
            });
            
            // 返回Promise，等待用户操作
            return new Promise((resolve) => {
                this.agreementWindow.on('closed', () => {
                    this.agreementWindow = null;
                    this.currentAgreement = null;
                    // 如果窗口被强制关闭，默认为拒绝
                    resolve(false);
                });
                
                // 监听协议结果
                ipcMain.once(`agreement-result-${result.agreement.id}`, (event, accepted) => {
                    resolve(accepted);
                });
            });
        } catch (error) {
            console.error('显示协议失败:', error);
            return true; // 错误时默认同意
        }
    }
    
    /**
     * 显示协议窗口（新方法，用于显示特定协议）
     * @param {Object} agreement - 协议数据
     */
    async showAgreementWindow(agreement) {
        try {
            console.log('显示协议窗口:', agreement);

            // 如果已经有协议窗口在显示，先关闭
            if (this.agreementWindow && !this.agreementWindow.isDestroyed()) {
                this.agreementWindow.close();
            }

            // 设置当前协议数据
            this.currentAgreement = agreement;

            // 创建协议窗口
            this.agreementWindow = new BrowserWindow({
                width: 900,
                height: 700,
                resizable: true,
                maximizable: true,
                minimizable: true,
                modal: false,
                frame: false,
                show: false,
                backgroundColor: '#ffffff',
                title: agreement ? agreement.title || '用户协议' : '用户协议',
                webPreferences: {
                    nodeIntegration: false,
                    contextIsolation: true,
                    preload: path.join(__dirname, 'agreement-preload.js')
                }
            });

            // 加载协议HTML
            await this.agreementWindow.loadFile(path.join(__dirname, 'renderer', 'agreement.html'));

            // 窗口准备好后显示
            this.agreementWindow.once('ready-to-show', () => {
                this.agreementWindow.show();
                this.agreementWindow.center();
            });

            // 窗口关闭时清理
            this.agreementWindow.on('closed', () => {
                this.agreementWindow = null;
                this.currentAgreement = null;
            });

            return true;
        } catch (error) {
            console.error('显示协议窗口失败:', error);
            return false;
        }
    }

    /**
     * 关闭协议窗口
     */
    closeAgreement() {
        if (this.agreementWindow && !this.agreementWindow.isDestroyed()) {
            this.agreementWindow.close();
        }
    }
    
    /**
     * 记录协议操作
     * @param {number} agreementId - 协议ID
     * @param {string} action - 操作类型 (accept, decline)
     */
    recordAgreementAction(agreementId, action) {
        const agreementHistory = this.store.get('agreementHistory', {});
        const agreementIdStr = agreementId.toString();
        
        agreementHistory[agreementIdStr] = {
            status: action === 'accept' ? 'accepted' : 'declined',
            timestamp: Date.now(),
            version: this.currentAgreement?.updated_at || Date.now()
        };
        
        this.store.set('agreementHistory', agreementHistory);
        
        // 发送结果给等待的Promise
        if (this.agreementWindow && !this.agreementWindow.isDestroyed()) {
            const { ipcMain } = require('electron');
            ipcMain.emit(`agreement-result-${agreementId}`, null, action === 'accept');
        }
    }
    
    /**
     * 强制显示协议（用于设置菜单）
     */
    async forceShowAgreement() {
        return await this.showAgreement();
    }
    
    /**
     * 清除协议历史记录
     */
    clearHistory() {
        this.store.delete('agreementHistory');
        console.log('协议历史记录已清除');
    }
    
    /**
     * 获取协议历史记录
     * @returns {Object} 协议历史记录
     */
    getAgreementHistory() {
        return this.store.get('agreementHistory', {});
    }
}

module.exports = AgreementManager;