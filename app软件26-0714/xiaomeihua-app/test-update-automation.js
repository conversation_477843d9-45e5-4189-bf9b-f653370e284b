/**
 * 测试自动化更新功能
 * 用于验证新的蓝奏云自动下载和安装功能
 */

const UpdateManager = require('./src/update-manager');
const { app, BrowserWindow } = require('electron');
const path = require('path');

class UpdateTester {
    constructor() {
        this.updateManager = null;
        this.testResults = [];
    }

    /**
     * 初始化测试环境
     */
    async initialize() {
        console.log('🧪 初始化更新功能测试环境...');
        
        // 创建测试用的更新管理器
        this.updateManager = new UpdateManager('1.0.0');
        
        // 模拟更新信息
        this.updateManager.updateInfo = {
            version: '2.0.0',
            title: '测试更新',
            description: '这是一个测试更新，用于验证自动化下载功能',
            features: [
                '增强的蓝奏云自动下载功能',
                '改进的用户界面体验',
                '更好的错误处理和重试机制',
                '自动安装和重启功能'
            ],
            download_urls: {
                windows: 'https://wwke.lanzoue.com/iuTmX323lc8f',  // 测试蓝奏云链接
                macos: 'https://wwke.lanzoue.com/iuTmX323lc8f',
                windows_web: 'https://wwke.lanzoue.com/iuTmX323lc8f',
                macos_web: 'https://wwke.lanzoue.com/iuTmX323lc8f'
            }
        };
        
        console.log('✅ 测试环境初始化完成');
    }

    /**
     * 测试蓝奏云链接解析
     */
    async testLanzouUrlParsing() {
        console.log('\n🔍 测试蓝奏云链接解析功能...');
        
        const testUrls = [
            'https://wwke.lanzoue.com/iuTmX323lc8f',
            'https://www.lanzoue.com/iuTmX323lc8f',
            'https://lanzou.com/iuTmX323lc8f'
        ];

        for (const url of testUrls) {
            try {
                console.log(`📡 测试链接: ${url}`);
                const realUrl = await this.updateManager.parseLanzouUrl(url);
                console.log(`✅ 解析成功: ${realUrl}`);
                this.testResults.push({
                    test: 'LanzouUrlParsing',
                    url: url,
                    result: 'success',
                    realUrl: realUrl
                });
            } catch (error) {
                console.error(`❌ 解析失败: ${error.message}`);
                this.testResults.push({
                    test: 'LanzouUrlParsing',
                    url: url,
                    result: 'failed',
                    error: error.message
                });
            }
        }
    }

    /**
     * 测试下载功能
     */
    async testDownloadFunction() {
        console.log('\n📥 测试下载功能...');
        
        try {
            const downloadUrl = this.updateManager.getDownloadUrl();
            if (!downloadUrl) {
                throw new Error('无法获取下载链接');
            }

            console.log(`📡 开始测试下载: ${downloadUrl}`);
            
            // 创建测试临时目录
            const tempDir = path.join(__dirname, 'test-temp');
            const fs = require('fs');
            if (!fs.existsSync(tempDir)) {
                fs.mkdirSync(tempDir, { recursive: true });
            }

            const testFilePath = path.join(tempDir, 'test-download.exe');
            
            // 测试下载（只下载前1MB来测试）
            await this.updateManager.downloadFileWithRetry(downloadUrl, testFilePath, 1);
            
            console.log('✅ 下载功能测试成功');
            this.testResults.push({
                test: 'DownloadFunction',
                result: 'success',
                filePath: testFilePath
            });

            // 清理测试文件
            if (fs.existsSync(testFilePath)) {
                fs.unlinkSync(testFilePath);
            }
            if (fs.existsSync(tempDir)) {
                fs.rmdirSync(tempDir);
            }

        } catch (error) {
            console.error(`❌ 下载功能测试失败: ${error.message}`);
            this.testResults.push({
                test: 'DownloadFunction',
                result: 'failed',
                error: error.message
            });
        }
    }

    /**
     * 测试文件验证功能
     */
    async testFileValidation() {
        console.log('\n🔍 测试文件验证功能...');
        
        const fs = require('fs');
        const tempDir = path.join(__dirname, 'test-temp');
        
        if (!fs.existsSync(tempDir)) {
            fs.mkdirSync(tempDir, { recursive: true });
        }

        // 测试用例
        const testCases = [
            {
                name: '正常文件',
                fileName: 'test-app.exe',
                size: 5 * 1024 * 1024, // 5MB
                expected: true
            },
            {
                name: '文件过小',
                fileName: 'small-app.exe',
                size: 500 * 1024, // 500KB
                expected: false
            },
            {
                name: '错误扩展名',
                fileName: 'wrong-ext.txt',
                size: 5 * 1024 * 1024,
                expected: false
            }
        ];

        for (const testCase of testCases) {
            try {
                const filePath = path.join(tempDir, testCase.fileName);
                
                // 创建测试文件
                const buffer = Buffer.alloc(testCase.size, 0);
                fs.writeFileSync(filePath, buffer);
                
                // 测试验证
                const result = this.updateManager.validateDownloadedFile(filePath);
                
                if (result === testCase.expected) {
                    console.log(`✅ ${testCase.name}: 验证结果正确`);
                    this.testResults.push({
                        test: 'FileValidation',
                        case: testCase.name,
                        result: 'success'
                    });
                } else {
                    console.error(`❌ ${testCase.name}: 验证结果错误，期望 ${testCase.expected}，实际 ${result}`);
                    this.testResults.push({
                        test: 'FileValidation',
                        case: testCase.name,
                        result: 'failed',
                        expected: testCase.expected,
                        actual: result
                    });
                }
                
                // 清理测试文件
                fs.unlinkSync(filePath);
                
            } catch (error) {
                console.error(`❌ ${testCase.name}: 测试失败 - ${error.message}`);
                this.testResults.push({
                    test: 'FileValidation',
                    case: testCase.name,
                    result: 'error',
                    error: error.message
                });
            }
        }

        // 清理临时目录
        if (fs.existsSync(tempDir)) {
            fs.rmdirSync(tempDir);
        }
    }

    /**
     * 测试错误处理和重试机制
     */
    async testErrorHandling() {
        console.log('\n🔄 测试错误处理和重试机制...');
        
        try {
            // 测试无效URL的处理
            const invalidUrl = 'https://invalid-url-for-testing.com/nonexistent';
            
            try {
                await this.updateManager.downloadFileWithRetry(invalidUrl, '/tmp/test-file.exe', 1);
                console.error('❌ 错误处理测试失败：应该抛出错误但没有');
                this.testResults.push({
                    test: 'ErrorHandling',
                    case: 'InvalidURL',
                    result: 'failed',
                    reason: 'Should have thrown error'
                });
            } catch (error) {
                console.log(`✅ 错误处理测试成功：正确捕获错误 - ${error.message}`);
                this.testResults.push({
                    test: 'ErrorHandling',
                    case: 'InvalidURL',
                    result: 'success',
                    error: error.message
                });
            }
            
        } catch (error) {
            console.error(`❌ 错误处理测试失败: ${error.message}`);
            this.testResults.push({
                test: 'ErrorHandling',
                result: 'failed',
                error: error.message
            });
        }
    }

    /**
     * 生成测试报告
     */
    generateTestReport() {
        console.log('\n📊 生成测试报告...');
        
        const totalTests = this.testResults.length;
        const successfulTests = this.testResults.filter(r => r.result === 'success').length;
        const failedTests = this.testResults.filter(r => r.result === 'failed').length;
        const errorTests = this.testResults.filter(r => r.result === 'error').length;
        
        console.log('\n' + '='.repeat(60));
        console.log('📋 自动化更新功能测试报告');
        console.log('='.repeat(60));
        console.log(`总测试数: ${totalTests}`);
        console.log(`成功: ${successfulTests} ✅`);
        console.log(`失败: ${failedTests} ❌`);
        console.log(`错误: ${errorTests} 💥`);
        console.log(`成功率: ${((successfulTests / totalTests) * 100).toFixed(1)}%`);
        console.log('='.repeat(60));
        
        // 详细结果
        console.log('\n📝 详细测试结果:');
        this.testResults.forEach((result, index) => {
            const status = result.result === 'success' ? '✅' : 
                          result.result === 'failed' ? '❌' : '💥';
            console.log(`${index + 1}. ${status} ${result.test}${result.case ? ` - ${result.case}` : ''}`);
            if (result.error) {
                console.log(`   错误: ${result.error}`);
            }
            if (result.url) {
                console.log(`   URL: ${result.url}`);
            }
        });
        
        return {
            total: totalTests,
            successful: successfulTests,
            failed: failedTests,
            errors: errorTests,
            successRate: (successfulTests / totalTests) * 100,
            details: this.testResults
        };
    }

    /**
     * 运行所有测试
     */
    async runAllTests() {
        console.log('🚀 开始运行自动化更新功能测试...\n');
        
        try {
            await this.initialize();
            await this.testLanzouUrlParsing();
            await this.testFileValidation();
            await this.testErrorHandling();
            // 注意：下载测试可能需要网络连接，在实际环境中谨慎运行
            // await this.testDownloadFunction();
            
            const report = this.generateTestReport();
            
            console.log('\n🎉 所有测试完成！');
            return report;
            
        } catch (error) {
            console.error('💥 测试运行失败:', error.message);
            throw error;
        }
    }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
    const tester = new UpdateTester();
    tester.runAllTests().then(report => {
        console.log('\n✅ 测试完成，退出程序');
        process.exit(report.failed > 0 ? 1 : 0);
    }).catch(error => {
        console.error('💥 测试失败:', error);
        process.exit(1);
    });
}

module.exports = UpdateTester;
