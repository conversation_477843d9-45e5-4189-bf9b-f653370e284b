#!/usr/bin/env node

/**
 * 精简优化打包脚本
 * 只打包软件功能，不包含多余内容
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始精简优化打包...');
console.log('='.repeat(50));

// 检查必要文件
const requiredFiles = [
    'src/main.js',
    'src/preload.js',
    'src/renderer/main.html',
    'src/renderer/login.html',
    'build/icon.ico',
    'build/icon.icns',
    'package.json'
];

console.log('📋 检查必要文件...');
for (const file of requiredFiles) {
    if (fs.existsSync(file)) {
        console.log(`  ✅ ${file}`);
    } else {
        console.log(`  ❌ ${file} - 文件缺失！`);
        process.exit(1);
    }
}

// 清理旧的构建文件
console.log('\n🧹 清理旧的构建文件...');
try {
    execSync('npm run clean', { stdio: 'inherit' });
    console.log('  ✅ 清理完成');
} catch (error) {
    console.log('  ⚠️ 清理失败，继续执行');
}

// 检查当前平台
const platform = process.platform;
console.log(`\n🖥️ 当前平台: ${platform}`);

// 根据平台执行打包
if (platform === 'darwin') {
    // macOS - 打包DMG
    console.log('\n📦 开始打包 macOS DMG...');
    try {
        execSync('npm run build:dmg', { stdio: 'inherit' });
        console.log('  ✅ DMG 打包完成');
        
        // 检查生成的文件
        const distDir = path.join(__dirname, 'dist');
        if (fs.existsSync(distDir)) {
            const files = fs.readdirSync(distDir);
            const dmgFiles = files.filter(f => f.endsWith('.dmg'));
            
            if (dmgFiles.length > 0) {
                console.log('\n📁 生成的DMG文件:');
                dmgFiles.forEach(file => {
                    const filePath = path.join(distDir, file);
                    const stats = fs.statSync(filePath);
                    const sizeMB = (stats.size / 1024 / 1024).toFixed(2);
                    console.log(`  📄 ${file} (${sizeMB} MB)`);
                });
            }
        }
    } catch (error) {
        console.error('❌ DMG 打包失败:', error.message);
        process.exit(1);
    }
} else if (platform === 'win32') {
    // Windows - 打包EXE
    console.log('\n📦 开始打包 Windows EXE...');
    try {
        execSync('npm run build:win', { stdio: 'inherit' });
        console.log('  ✅ EXE 打包完成');
        
        // 检查生成的文件
        const distDir = path.join(__dirname, 'dist');
        if (fs.existsSync(distDir)) {
            const files = fs.readdirSync(distDir);
            const exeFiles = files.filter(f => f.endsWith('.exe'));
            
            if (exeFiles.length > 0) {
                console.log('\n📁 生成的EXE文件:');
                exeFiles.forEach(file => {
                    const filePath = path.join(distDir, file);
                    const stats = fs.statSync(filePath);
                    const sizeMB = (stats.size / 1024 / 1024).toFixed(2);
                    console.log(`  📄 ${file} (${sizeMB} MB)`);
                });
            }
        }
    } catch (error) {
        console.error('❌ EXE 打包失败:', error.message);
        process.exit(1);
    }
} else {
    // 其他平台 - 打包所有
    console.log('\n📦 开始打包所有平台...');
    try {
        execSync('npm run build:all', { stdio: 'inherit' });
        console.log('  ✅ 全平台打包完成');
    } catch (error) {
        console.error('❌ 全平台打包失败:', error.message);
        process.exit(1);
    }
}

// 显示最终结果
console.log('\n📊 打包结果统计:');
const distDir = path.join(__dirname, 'dist');
if (fs.existsSync(distDir)) {
    const files = fs.readdirSync(distDir);
    const packageFiles = files.filter(f => f.endsWith('.dmg') || f.endsWith('.exe') || f.endsWith('.AppImage'));
    
    if (packageFiles.length > 0) {
        let totalSize = 0;
        packageFiles.forEach(file => {
            const filePath = path.join(distDir, file);
            const stats = fs.statSync(filePath);
            const sizeMB = (stats.size / 1024 / 1024).toFixed(2);
            totalSize += stats.size;
            console.log(`  📦 ${file} - ${sizeMB} MB`);
        });
        
        const totalSizeMB = (totalSize / 1024 / 1024).toFixed(2);
        console.log(`\n📈 总大小: ${totalSizeMB} MB`);
        console.log(`📁 输出目录: ${distDir}`);
    } else {
        console.log('  ⚠️ 未找到打包文件');
    }
} else {
    console.log('  ❌ dist目录不存在');
}

console.log('\n✨ 精简优化打包完成！');
console.log('\n🎯 优化效果:');
console.log('  • 删除了所有测试文件和文档');
console.log('  • 移除了不必要的依赖项');
console.log('  • 优化了打包配置');
console.log('  • 只包含软件运行必需的文件');
