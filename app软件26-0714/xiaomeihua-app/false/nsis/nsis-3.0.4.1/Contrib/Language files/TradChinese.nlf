﻿# Header, don't edit
NLF v6
# Language ID
1028
# Font and size - dash (-) means default 字型名稱與大小
新細明體
9
# Codepage - dash (-) means ASCII code page ASCII 字碼頁
950
# RTL - anything else than RTL means LTR 由右至左書寫
-
# Revision date: 2017-03-17
# Translators:
## Ki<PERSON> <<EMAIL>>, <<EMAIL>>, <<EMAIL>>
## <PERSON> <<EMAIL>>
#
# ^Branding
Nullsoft Install System %s
# ^SetupCaption
$(^Name) 安裝
# ^UninstallCaption
$(^Name) 解除安裝
# ^LicenseSubCaption
: 授權協議
# ^ComponentsSubCaption
: 安裝選項
# ^DirSubCaption
: 安裝資料夾
# ^InstallingSubCaption
: 正在安裝
# ^CompletedSubCaption
: 已完成
# ^UnComponentsSubCaption
: 解除安裝選項
# ^UnDirSubCaption
: 解除安裝資料夾
# ^ConfirmSubCaption
: 確認
# ^UninstallingSubCaption
: 正在解除安裝
# ^UnCompletedSubCaption
: 完成
# ^BackBtn
< 上一步(&B)
# ^NextBtn
下一步(&N) >
# ^AgreeBtn
我同意(&A)
# ^AcceptBtn
我接受「授權協議」的條款(&A)
# ^DontAcceptBtn
我不接受「授權協議」的條款(&D)
# ^InstallBtn
安裝(&I)
# ^UninstallBtn
解除安裝(&U)
# ^CancelBtn
取消
# ^CloseBtn
關閉(&C)
# ^BrowseBtn
瀏覽(&R)...
# ^ShowDetailsBtn
顯示細節(&D)
# ^ClickNext
按「下一步(N)」繼續。
# ^ClickInstall
按「安裝(I)」開始安裝。
# ^ClickUninstall
按「解除安裝(U)」開始解除安裝。
# ^Name
名稱
# ^Completed
已完成
# ^LicenseText
在安裝 $(^NameDA) 之前請檢閱授權協議。如果接受協議所有條款，按「我同意(A)」。
# ^LicenseTextCB
在安裝 $(^NameDA) 之前請檢閱授權協議。如果接受協議所有條款，按下方的勾選框。 $_CLICK
# ^LicenseTextRB
在安裝 $(^NameDA) 之前請檢閱授權協議。如果接受協議所有條款，選擇下方的第一個選項。 $_CLICK
# ^UnLicenseText
在解除安裝 $(^NameDA) 之前請檢閱授權協議。如果接受協議中所有條款，按「我同意(A)」。
# ^UnLicenseTextCB
在解除安裝 $(^NameDA) 之前請檢閱授權協議。如果接受協議中所有條款，按下方的勾選框。 $_CLICK
# ^UnLicenseTextRB
在解除安裝 $(^NameDA) 之前請檢閱授權協議。如果接受協議中所有條款，選擇的第一個選項。 $_CLICK
# ^Custom
自訂
# ^ComponentsText
勾選想要安裝的元件，並解除勾選不想安裝的元件。 $_CLICK
# ^ComponentsSubText1
選取安裝的類型: 
# ^ComponentsSubText2_NoInstTypes
選取安裝的元件: 
# ^ComponentsSubText2
又或者，自訂選取想安裝的元件: 
# ^UnComponentsText
勾選想要解除安裝的元件，並解除勾選不想解除安裝的元件。 $_CLICK
# ^UnComponentsSubText1
選取解除安裝的類型: 
# ^UnComponentsSubText2_NoInstTypes
選取要解除安裝的元件: 
# ^UnComponentsSubText2
又或者，選擇想要解除安裝的可選項元件: 
# ^DirText
安裝程式會將 $(^NameDA) 安裝在以下資料夾。要安裝到不同的資料夾，按「瀏覽(B)...」並選擇其他資料夾。 $_CLICK
# ^DirSubText
目標資料夾
# ^DirBrowseText
選取要安裝 $(^NameDA) 的資料夾: 
# ^UnDirText
安裝程式會自以下資料夾解除安裝 $(^NameDA) 。要解除安裝不同的資料夾，按「瀏覽(B)...」並選擇其他資料夾。 $_CLICK
# ^UnDirSubText
""
# ^UnDirBrowseText
選取要解除安裝 $(^NameDA) 的資料夾: 
# ^SpaceAvailable
"可用空間: "
# ^SpaceRequired
"所需空間: "
# ^UninstallingText
會自以下資料夾解除安裝 $(^NameDA) 。 $_CLICK
# ^UninstallingSubText
解除安裝目錄: 
# ^FileError
無法開啟要寫入的檔案: \r\n\t"$0"\r\n按「中止」停止安裝，\r\n「重試」重新嘗試寫入檔案，或\r\n「忽略」略過此檔案。
# ^FileError_NoIgnore
無法開啟要寫入的檔案: \r\n\t"$0"\r\n按「重試」重新嘗試寫入檔案，或\r\n「取消」停止安裝。
# ^CantWrite
"無法寫入: "
# ^CopyFailed
"複製失敗 "
# ^CopyTo
"複製到: "
# ^Registering
"正在註冊: "
# ^Unregistering
"正在解除註冊: "
# ^SymbolNotFound
"無法找到符號: "
# ^CouldNotLoad
"無法載入: "
# ^CreateFolder
"建立資料夾: " 
# ^CreateShortcut
"建立捷徑: "
# ^CreatedUninstaller
"建立解除安裝程式: "
# ^Delete
"刪除檔案: "
# ^DeleteOnReboot
"重新開機後刪除: "
# ^ErrorCreatingShortcut
"建立捷徑時發生錯誤: "
# ^ErrorCreating
"建立時發生錯誤: "
# ^ErrorDecompressing
"解壓縮資料發生錯誤！安裝程式是否已損壞？"
# ^ErrorRegistering
"註冊 DLL 時發生錯誤"
# ^ExecShell
"執行殼層程式: "
# ^Exec
"執行: "
# ^Extract
"抽取: "
# ^ErrorWriting
"抽取: 無法寫入檔案 "
# ^InvalidOpcode
"安裝程式損毀: 無效的作業代碼 "
# ^NoOLE
"沒有 OLE 用於: "
# ^OutputFolder
"輸出資料夾: "
# ^RemoveFolder
"移除資料夾: "
# ^RenameOnReboot
"重新開機後重新命名: "
# ^Rename
"重新命名: "
# ^Skipped
"已略過: "
# ^CopyDetails
"複製細節到剪貼簿 "
# ^LogInstall
"將安裝進程記入日誌"
# ^Byte
B
# ^Kilo
 K
# ^Mega
 M
# ^Giga
 G
