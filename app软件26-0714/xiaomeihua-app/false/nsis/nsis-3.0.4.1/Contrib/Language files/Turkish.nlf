﻿# Header, don't edit
NLF v6
# Start editing here
# Language ID
1055
# Font and size - dash (-) means default
-
-
# Codepage - dash (-) means ASCII code page
1254
# RTL - anything else than RTL means LTR
-
# Translation by ?atay <PERSON><PERSON>(chagy) (<EMAIL>)
# Corrections by Mozilla Türkiye Yerelleştirme Topluluğu tarafından çevrilmiştir. http://mozilla.org.tr
# ^Branding
Nullsoft Install System %s
# ^SetupCaption
$(^Name) Kurulumu
# ^UninstallCaption
$(^Name) Kaldırma
# ^LicenseSubCaption
: Lisans <PERSON>özleşmesi
# ^ComponentsSubCaption
: <PERSON><PERSON><PERSON> Seçenekleri
# ^DirSubCaption
: Kurulum Dizini
# ^InstallingSubCaption
: Kuruluyor
# ^CompletedSubCaption
: Tamamlandı
# ^UnComponentsSubCaption
: Kaldırma Seçenekleri
# ^UnDirSubCaption
: Kaldırılacak Dizin
# ^ConfirmSubCaption
: Onay
# ^UninstallingSubCaption
: Kaldırılıyor
# ^UnCompletedSubCaption
: Tamamlandı
# ^BackBtn
< &Geri
# ^NextBtn
İ&leri >
# ^AgreeBtn
&Kabul Ediyorum
# ^AcceptBtn
Lisans Sözleşmesi'nin koşullarını &kabul ediyorum
# ^DontAcceptBtn
Lisans Sözleşmesi'nin koşullarını kabul et&miyorum
# ^InstallBtn
&Kur
# ^UninstallBtn
&Kaldır
# ^CancelBtn
Vazgeç
# ^CloseBtn
&Kapat
# ^BrowseBtn
&Gözat...
# ^ShowDetailsBtn
&Ayrıntıları göster
# ^ClickNext
Devam etmek için İleri düğmesine basın.
# ^ClickInstall
Kurulumu başlatmak için Kur düğmesine basın.
# ^ClickUninstall
Kaldırmayı başlatmak için Kaldır düğmesine basın.
# ^Name
Ad
# ^Completed
Tamamlandı
# ^LicenseText
Lütfen $(^NameDA) uygulamasını kurmadan önce lisans sözleşmesini gözden geçirin. Sözleşmedeki bütün koşulları kabul ediyorsanız Kabul Ediyorum düğmesine basın.
# ^LicenseTextCB
Lütfen $(^NameDA) uygulamasını kurmadan önce lisans sözleşmesini gözden geçirin. Sözleşmedeki bütün koşulları kabul ediyorsanız aşağıdaki kutuya işaret koyun. $_CLICK
# ^LicenseTextRB
Lütfen $(^NameDA) uygulamasını kurmadan önce lisans sözleşmesini gözden geçirin. Sözleşmedeki bütün koşulları kabul ediyorsanız aşağıdaki ilk seçeneği seçin. $_CLICK
# ^UnLicenseText
Lütfen $(^NameDA) uygulamasını kaldırmadan önce lisans sözleşmesini gözden geçirin. Sözleşmedeki bütün koşulları kabul ediyorsanız Kabul Ediyorum düğmesine basın.
# ^UnLicenseTextCB
Lütfen $(^NameDA) uygulamasını kaldırmadan önce lisans sözleşmesini gözden geçirin. Sözleşmedeki bütün koşulları kabul ediyorsanız aşağıdaki kutuya işaret koyun. $_CLICK
# ^UnLicenseTextRB
Lütfen $(^NameDA) uygulamasını kaldırmadan önce lisans sözleşmesini gözden geçirin. Sözleşmedeki bütün koşulları kabul ediyorsanız aşağıdaki ilk seçeneği seçin. $_CLICK
# ^Custom
Özel
# ^ComponentsText
Kurmak istediğiniz bileşenleri işaretleyip kurmak istemediklerinizi işaretlemeden bırakın. $_CLICK
# ^ComponentsSubText1
Kurulum türünü seçin:
# ^ComponentsSubText2_NoInstTypes
Kurulacak bileşenleri seçin:
# ^ComponentsSubText2
ya da isteğe bağlı olarak kurmak istediğiniz bileşenleri seçin:
# ^UnComponentsText
Kaldırmak istediğiniz bileşenleri işaretleyip kaldırmak istemediklerinizi işaretlemeden bırakın. $_CLICK
# ^UnComponentsSubText1
Kaldırma türünü seçin:
# ^UnComponentsSubText2_NoInstTypes
Kaldırılacak bileşenleri seçin:
# ^UnComponentsSubText2
ya da isteğe bağlı olarak kaldırmak istediğiniz bileşenleri seçin:
# ^DirText
$(^NameDA) aşağıdaki dizinde kurulacak. Farklı bir dizinde kurmak için Gözat düğmesine basıp başka bir dizin seçin. $_CLICK
# ^DirSubText
Hedef Dizin
# ^DirBrowseText
$(^NameDA) uygulamasının kurulacağı dizini seçin:
# ^UnDirText
$(^NameDA) aşağıdaki dizinden kaldırılacak. Farklı bir dizinden kaldırmak için Gözat düğmesine basıp başka bir dizin seçin. $_CLICK
# ^UnDirSubText
""
# ^UnDirBrowseText
$(^NameDA) uygulamasının kaldırılacağı dizini seçin:
# ^SpaceAvailable
"Kullanılabilir boş alan: "
# ^SpaceRequired
"Gereken boş alan: "
# ^UninstallingText
$(^NameDA) aşağıdaki dizinden kaldırılacak. $_CLICK
# ^UninstallingSubText
Kaldırılan yer:
# ^FileError
Dosya yazmak için açılırken hata meydana geldi: \r\n\r\n$0\r\n\r\nKurulumu durdurmak için Dur düğmesine,\r\nyeniden denemek için Yeniden Dene düğmesine,\r\nbu dosyayı atlamak için Yoksay düğmesine basın.
# ^FileError_NoIgnore
Dosya yazmak için açılırken hata meydana geldi: \r\n\r\n$0\r\n\r\nYeniden denemek için Yeniden Dene düğmesine,\r\nkurulumu durdurmak için Vazgeç düğmesine basın.
# ^CantWrite
"Yazılamadı: "
# ^CopyFailed
Kopyalama başarısız oldu
# ^CopyTo
"Kayıt: "
# ^Registering
"Kaydediliyor: "
# ^Unregistering
"Kayıt siliniyor: "
# ^SymbolNotFound
"Simge bulunamadı: "
# ^CouldNotLoad
"Yüklenemedi: "
# ^CreateFolder
"Dizin oluştur: "
# ^CreateShortcut
"Kısayol oluştur: "
# ^CreatedUninstaller
"Kaldırma uygulaması oluştur: "
# ^Delete
"Dosya sil: "
# ^DeleteOnReboot
"Açılışta sil: "
# ^ErrorCreatingShortcut
"Kısayol oluşturulurken hata meydana geldi: "
# ^ErrorCreating
"Oluşturma hatası: "
# ^ErrorDecompressing
Veriyi açarken hata meydana geldi! Acaba kurulum uygulaması mı bozuk?
# ^ErrorRegistering
DLL kaydedilirken hata meydana geldi
# ^ExecShell
"ExecShell: "
# ^Exec
"Çalıştır: "
# ^Extract
"Aç: "
# ^ErrorWriting
"Açma: Dosyaya yazarken hata meydana geldi "
# ^InvalidOpcode
Kurulum bozuk: Geçersiz kod
# ^NoOLE
"OLE yok: "
# ^OutputFolder
"Çıktı dizini: "
# ^RemoveFolder
"Dizini sil: "
# ^RenameOnReboot
"Açılışta adını değiştir: "
# ^Rename
"Ad değiştir: "
# ^Skipped
"Atlandı: "
# ^CopyDetails
Ayrıntıları panoya kopyala
# ^LogInstall
Kurulum sürecinin kaydını tut
# ^Byte
B
# ^Kilo
 K
# ^Mega
 M
# ^Giga
 G
