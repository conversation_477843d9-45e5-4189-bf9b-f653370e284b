﻿# Header, don't edit
NLF v6
# Language ID
2052
# Font and size - dash (-) means default 字体名称与大小
宋体
9
# Codepage - dash (-) means ASCII code page ASCII 字码页
936
# RTL - anything else than RTL means LTR 由右至左书写
-
# Revision date: 2010-09-28
# Translator: <PERSON><PERSON> <<EMAIL>>, <<EMAIL>>, <<EMAIL>>
# ^Branding
Nullsoft Install System %s
# ^SetupCaption
$(^Name) 安装
# ^UninstallCaption
$(^Name) 解除安装
# ^LicenseSubCaption
: 许可证协议
# ^ComponentsSubCaption
: 安装选项
# ^DirSubCaption
: 安装文件夹
# ^InstallingSubCaption
: 正在安装
# ^CompletedSubCaption
: 已完成
# ^UnComponentsSubCaption
: 解除安装选项
# ^UnDirSubCaption
: 解除安装文件夹
# ^ConfirmSubCaption
: 确认
# ^UninstallingSubCaption
: 正在解除安装
# ^UnCompletedSubCaption
: 完成
# ^BackBtn
< 上一步(&P)
# ^NextBtn
下一步(&N) >
# ^AgreeBtn
我同意(&I)
# ^AcceptBtn
我接受“许可证协议”中的条款(&A)
# ^DontAcceptBtn
我不接受“许可证协议”中的条款(&N)
# ^InstallBtn
安装(&I)
# ^UninstallBtn
解除安装(&U)
# ^CancelBtn
取消(&C)
# ^CloseBtn
关闭(&L)
# ^BrowseBtn
浏览(&B)...
# ^ShowDetailsBtn
显示细节(&D)
# ^ClickNext
单击 [下一步(N)] 继续。
# ^ClickInstall
单击 [安装(I)] 开始安装进程。
# ^ClickUninstall
单击 [解除安装(U)] 开始解除安装进程。
# ^Name
名称
# ^Completed
已完成
# ^LicenseText
在安装 $(^NameDA) 之前请检阅许可证协议。如果你接受协议中所有条款，单击 [我同意(I)] 。
# ^LicenseTextCB
在安装 $(^NameDA) 之前请检阅许可证协议。如果你接受协议中所有条款，单击下方的勾选框。 $_CLICK
# ^LicenseTextRB
在安装 $(^NameDA) 之前请检阅许可证协议。如果你接受协议中所有条款，选择下方的第一个选项。 $_CLICK
# ^UnLicenseText
在解除安装 $(^NameDA) 之前请检阅许可证协议。如果你接受协议中所有条款，单击 [我同意(I)] 。
# ^UnLicenseTextCB
在解除安装 $(^NameDA) 之前请检阅许可证协议。如果你接受协议中所有条款，单击下方的勾选框。 $_CLICK
# ^UnLicenseTextRB
在解除安装 $(^NameDA) 之前请检阅许可证协议。如果你接受协议中所有条款，选择下方的第一个选项。 $_CLICK
# ^Custom
自定义
# ^ComponentsText
勾选你想要安装的组件，并解除勾选你不希望安装的组件。 $_CLICK
# ^ComponentsSubText1
选定安装的类型: 
# ^ComponentsSubText2_NoInstTypes
选定安装的组件: 
# ^ComponentsSubText2
或者，自定义选定想安装的组件: 
# ^UnComponentsText
勾选你想要解除安装的组件，并解除勾选你不想要望解除安装的组件。 $_CLICK
# ^UnComponentsSubText1
选择解除安装的类型: 
# ^UnComponentsSubText2_NoInstTypes
选择要解除安装的组件: 
# ^UnComponentsSubText2
或是，选择想要解除安装的可选项组件: 
# ^DirText
Setup 将安装 $(^NameDA) 在下列文件夹。要安装到不同文件夹，单击 [浏览(B)...] 并选择其他的文件夹。 $_CLICK
# ^DirSubText
目标文件夹
# ^DirBrowseText
选择要安装 $(^NameDA) 的文件夹位置: 
# ^UnDirText
Setup 将自下列文件夹解除安装 $(^NameDA) 。要解除安装到不同文件夹，单击 [浏览(B)...] 并选择其他的文件夹。 $_CLICK
# ^UnDirSubText
""
# ^UnDirBrowseText
选择要解除安装 $(^NameDA) 的文件夹: 
# ^SpaceAvailable
"可用空间: "
# ^SpaceRequired
"所需空间: "
# ^UninstallingText
这个向导将从你的计算机解除安装 $(^NameDA) 。 $_CLICK
# ^UninstallingSubText
解除安装目录: 
# ^FileError
不能打开要写入的文件: \r\n\t"$0"\r\n单击 [Abort] 停止安装，\r\n [Retry] 重新尝试写入文件，或\r\n [Ignore] 忽略这个文件。
# ^FileError_NoIgnore
不能打开要写入的文件: \r\n\t"$0"\r\n单击 [Retry] 重新尝试写入文件，或\r\n [Cancel] 停止安装。
# ^CantWrite
"无法写入: "
# ^CopyFailed
"复制失败 "
# ^CopyTo
"复制到: "
# ^Registering
"正在注册: "
# ^Unregistering
"正在解除注册: "
# ^SymbolNotFound
"无法找到符号: "
# ^CouldNotLoad
"无法加载: "
# ^CreateFolder
"创建文件夹: " 
# ^CreateShortcut
"创建快捷方式: "
# ^CreatedUninstaller
"创建解除安装程序: "
# ^Delete
"删除文件: "
# ^DeleteOnReboot
"重新启动后删除: "
# ^ErrorCreatingShortcut
"正在创建快捷方式时发生错误: "
# ^ErrorCreating
"正在创建时发生错误: "
# ^ErrorDecompressing
"正在解压缩数据发生错误！已损坏的安装程序？"
# ^ErrorRegistering
"正在注册 DLL 时发生错误"
# ^ExecShell
"运行壳层程序: "
# ^Exec
"运行: "
# ^Extract
"抽取: "
# ^ErrorWriting
"抽取: 无法写入文件 "
# ^InvalidOpcode
"安装损毁: 无效的操作代码 "
# ^NoOLE
"没有 OLE 用于: "
# ^OutputFolder
"输出目录: "
# ^RemoveFolder
"移除目录: "
# ^RenameOnReboot
"重新启动后重命名: "
# ^Rename
"重命名: "
# ^Skipped
"已跳过: "
# ^CopyDetails
"复制细节到剪贴板 "
# ^LogInstall
"日志安装进程"
# byte
B
# kilo
 K
# mega
 M
# giga
 G
