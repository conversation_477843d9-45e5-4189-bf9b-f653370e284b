﻿# Header, don't edit
NLF v6
# Language ID
1062
# Font and size - dash (-) means default
-
-
# Codepage - dash (-) means ASCII code page
1257
# RTL - anything else than RTL means LTR
-
# Translation by <PERSON><PERSON> Griķis (<EMAIL>)
# Corrections by <PERSON>ta<PERSON> Meņģelis / x-f (x-f 'AT' inbox.lv)
# ^Branding
Nullsoft Install System %s
# ^SetupCaption
'$(^Name)' Uzstādīšana
# ^UninstallCaption
'$(^Name)' Atinstalēšana
# ^LicenseSubCaption
: Licences līgums
# ^ComponentsSubCaption
: Uzstādīšanas opcijas
# ^DirSubCaption
: Uzstādīšanas mape
# ^InstallingSubCaption
: Notiek uzstādīšana
# ^CompletedSubCaption
: Uzstād<PERSON><PERSON>na pabeigta.
# ^UnComponentsSubCaption
: Atinstalēšanas opcijas
# ^UnDirSubCaption
: Atinstalēšanas mape
# ^ConfirmSubCaption
: Aps<PERSON><PERSON><PERSON><PERSON>na
# ^UninstallingSubCaption
: Notiek atinstalēšana
# ^UnCompletedSubCaption
: Atinstal<PERSON><PERSON>na pabeigta
# ^BackBtn
< &Atpakaļ
# ^NextBtn
&Tālāk >
# ^AgreeBtn
Es &piekrītu
# ^AcceptBtn
Es &piekrītu licences līguma noteikumiem
# ^DontAcceptBtn
Es &nepiekrītu licences līguma noteikumiem
# ^InstallBtn
&Uzstādīt
# ^UninstallBtn
&Atinstalēt
# ^CancelBtn
Atcelt
# ^CloseBtn
Ai&zvērt
# ^BrowseBtn
Pā&rlūkot...
# ^ShowDetailsBtn
Parādīt &detaļas
# ^ClickNext
Spiediet 'Tālāk', lai turpinātu.
# ^ClickInstall
Spiediet 'Uzstādīt', lai sāktu uzstādīšanas procesu.
# ^ClickUninstall
Spiediet 'Atinstalēt', lai sāktu atinstalēšanas procesu.
# ^Name
Vārds
# ^Completed
Uzstādīšana pabeigta
# ^LicenseText
Lūdzu izlasiet licences līgumu pirms '$(^NameDA)' uzstādīšanas. Ja piekrītat licences līguma noteikumiem, tad spiediet 'Es piekrītu'.
# ^LicenseTextCB
Lūdzu izlasiet licences līgumu pirms '$(^NameDA)' uzstādīšanas. Ja piekrītat licences līguma noteikumiem, tad atzīmējiet izvēles rūtiņu. $_CLICK
# ^LicenseTextRB
Lūdzu izlasiet licences līgumu pirms '$(^NameDA)' uzstādīšanas. Ja piekrītat licences līguma noteikumiem, tad izvēlieties pirmo zemākesošo opciju. $_CLICK
# ^UnLicenseText
Lūdzu izlasiet licences līgumu pirms '$(^NameDA)' atinstalēšanas. Ja piekrītat licences līguma noteikumiem, tad spiediet 'Es piekrītu'.
# ^UnLicenseTextCB
Lūdzu izlasiet licences līgumu pirms '$(^NameDA)' atinstalēšanas. Ja piekrītat licences līguma noteikumiem, tad atzīmējiet izvēles rūtiņu. $_CLICK
# ^UnLicenseTextRB
Lūdzu izlasiet licences līgumu pirms '$(^NameDA)' atinstalēšanas. Ja piekrītat licences līguma noteikumiem, tad izvēlieties zemākesošo opciju. $_CLICK
# ^Custom
Pielāgots
# ^ComponentsText
Izvēlieties, kurus komponentus vēlaties uzstādīt un neiezīmējiet tos, kurus nevēlaties uzstādīt. $_CLICK
# ^ComponentsSubText1
Izvēlieties uzstādīšanas veidu:
# ^ComponentsSubText2_NoInstTypes
Izvēlieties uzstādāmos komponentus:
# ^ComponentsSubText2
Vai arī – izvēlieties tikai nepieciešamos komponentus, kurus vēlaties uzstādīt:
# ^UnComponentsText
Izvēlieties, kurus komponentus atinstalēt un neiezīmējiet tos, kurus nevēlaties atinstalēt. $_CLICK
# ^UnComponentsSubText1
Izvēlieties atinstalēšanas veidu:
# ^UnComponentsSubText2_NoInstTypes
Izvēlieties atinstalējamos komponentus:
# ^UnComponentsSubText2
Vai arī – izvēlieties tikai nepieciešamos komponentus, kurus vēlaties atinstalēt:
# ^DirText
'$(^NameDA)' tiks uzstādīta šajā mapē. Lai to uzstādītu citā mapē, nospiediet 'Pārlūkot' un izvēlieties citu mapi. $_CLICK
# ^DirSubText
Uzstādīšanas mape
# ^DirBrowseText
Izvēlieties mapi, kurā uzstādīt '$(^NameDA)':
# ^UnDirText
'$(^NameDA)' tiks atinstalēta no šīs mapes. Lai to atinstalētu no citas mapes, nospiediet 'Pārlūkot' un izvēlieties citu mapi. $_CLICK
# ^UnDirSubText
""
# ^UnDirBrowseText
Izvēlieties mapi, no kuras atinstalēt '$(^NameDA)':
# ^SpaceAvailable
"Pieejamais diska apjoms: "
# ^SpaceRequired
"Nepieciešamais diska apjoms: "
# ^UninstallingText
'$(^NameDA)' tiks atinstalēta no šīs mapes. $_CLICK
# ^UninstallingSubText
Atinstalēšana no:
# ^FileError
Kļūda atverot failu rakstīšanai: \r\n\t"$0"\r\nNospiediet 'Atcelt', lai atceltu uzstādīšanas procesu,\r\n'Mēģināt vēlreiz', lai atkārtoti mēģinātu rakstīt failā vai\r\n'Ignorēt', lai izlaistu šī faila uzstādīšanu
# ^FileError_NoIgnore
Kļūda atverot failu rakstīšanai: \r\n\t"$0"\r\nNospiediet 'Atcelt', lai pārtrauktu uzstādīšanas procesu
# ^CantWrite
"Nevar ierakstīt: "
# ^CopyFailed
Kopēšana neizdevās
# ^CopyTo
"Kopē uz "
# ^Registering
"Reģistrē: "
# ^Unregistering
"Atreģistrē: "
# ^SymbolNotFound
"Simbols nav atrasts: "
# ^CouldNotLoad
"Nav iespējams ielādēt: "
# ^CreateFolder
"Izveido mapi: "
# ^CreateShortcut
"Izveido saīsni: "
# ^CreatedUninstaller
"Izveidots atinstalētājs: "
# ^Delete
"Dzēš failu: "
# ^DeleteOnReboot
"Dzēst pēc pārstartēšanas: "
# ^ErrorCreatingShortcut
"Kļūda veidojot saīsni: "
# ^ErrorCreating
"Kļūda veidojot: "
# ^ErrorDecompressing
Kļūda atkompresējot datus! Bojāta instalācija?
# ^ErrorRegistering
Kļūda reģistrējot DLL failu
# ^ExecShell
"Izpilda čaulā: "
# ^Exec
"Izpilda: "
# ^Extract
"Atspiež: "
# ^ErrorWriting
"Atspiešana: kļūda rakstot failā "
# ^InvalidOpcode
Instalācija bojāta: nederīgs CRC kods
# ^NoOLE
"Nav OLE priekš: "
# ^OutputFolder
"Izvades mape: "
# ^RemoveFolder
"Dzēš mapi: "
# ^RenameOnReboot
"Pārsaukt pēc pārstartēšanas: "
# ^Rename
"Pārsaukt: "
# ^Skipped
"Izlaists: "
# ^CopyDetails
Iekopēt detaļas starpliktuvē
# ^LogInstall
Ierakstīt žurnāla failā uzstādīšanas procesu
# ^Byte
B
# ^Kilo
 K
# ^Mega
 M
# ^Giga
 G
