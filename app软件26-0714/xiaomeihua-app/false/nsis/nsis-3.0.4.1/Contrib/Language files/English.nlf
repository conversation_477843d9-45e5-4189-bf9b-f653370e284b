﻿# Header, don't edit
NLF v6
# Start editing here
# Language ID
1033
# Font and size - dash (-) means default
-
-
# Codepage - dash (-) means ASCII code page
-
# RTL - anything else than RTL means LTR
-
# Translation by ..... (any credits should go here)
# ^Branding
Nullsoft Install System %s
# ^SetupCaption
$(^Name) Setup
# ^UninstallCaption
$(^Name) Uninstall
# ^LicenseSubCaption
: License Agreement
# ^ComponentsSubCaption
: Installation Options
# ^DirSubCaption
: Installation Folder
# ^InstallingSubCaption
: Installing
# ^CompletedSubCaption
: Completed
# ^UnComponentsSubCaption
: Uninstallation Options
# ^UnDirSubCaption
: Uninstallation Folder
# ^ConfirmSubCaption
: Confirmation
# ^UninstallingSubCaption
: Uninstalling
# ^UnCompletedSubCaption
: Completed
# ^BackBtn
< &Back
# ^NextBtn
&Next >
# ^AgreeBtn
I &Agree
# ^AcceptBtn
I &accept the terms of the License Agreement
# ^DontAcceptBtn
I &do not accept the terms of the License Agreement
# ^InstallBtn
&Install
# ^UninstallBtn
&Uninstall
# ^CancelBtn
Cancel
# ^CloseBtn
&Close
# ^BrowseBtn
B&rowse...
# ^ShowDetailsBtn
Show &details
# ^ClickNext
Click Next to continue.
# ^ClickInstall
Click Install to start the installation.
# ^ClickUninstall
Click Uninstall to start the uninstallation.
# ^Name
Name
# ^Completed
Completed
# ^LicenseText
Please review the license agreement before installing $(^NameDA). If you accept all terms of the agreement, click I Agree.
# ^LicenseTextCB
Please review the license agreement before installing $(^NameDA). If you accept all terms of the agreement, click the check box below. $_CLICK
# ^LicenseTextRB
Please review the license agreement before installing $(^NameDA). If you accept all terms of the agreement, select the first option below. $_CLICK
# ^UnLicenseText
Please review the license agreement before uninstalling $(^NameDA). If you accept all terms of the agreement, click I Agree.
# ^UnLicenseTextCB
Please review the license agreement before uninstalling $(^NameDA). If you accept all terms of the agreement, click the check box below. $_CLICK
# ^UnLicenseTextRB
Please review the license agreement before uninstalling $(^NameDA). If you accept all terms of the agreement, select the first option below. $_CLICK
# ^Custom
Custom
# ^ComponentsText
Check the components you want to install and uncheck the components you don't want to install. $_CLICK
# ^ComponentsSubText1
Select the type of install:
# ^ComponentsSubText2_NoInstTypes
Select components to install:
# ^ComponentsSubText2
Or, select the optional components you wish to install:
# ^UnComponentsText
Check the components you want to uninstall and uncheck the components you don't want to uninstall. $_CLICK
# ^UnComponentsSubText1
Select the type of uninstall:
# ^UnComponentsSubText2_NoInstTypes
Select components to uninstall:
# ^UnComponentsSubText2
Or, select the optional components you wish to uninstall:
# ^DirText
Setup will install $(^NameDA) in the following folder. To install in a different folder, click Browse and select another folder. $_CLICK
# ^DirSubText
Destination Folder
# ^DirBrowseText
Select the folder to install $(^NameDA) in:
# ^UnDirText
Setup will uninstall $(^NameDA) from the following folder. To uninstall from a different folder, click Browse and select another folder. $_CLICK
# ^UnDirSubText
""
# ^UnDirBrowseText
Select the folder to uninstall $(^NameDA) from:
# ^SpaceAvailable
"Space available: "
# ^SpaceRequired
"Space required: "
# ^UninstallingText
$(^NameDA) will be uninstalled from the following folder. $_CLICK
# ^UninstallingSubText
Uninstalling from:
# ^FileError
Error opening file for writing: \r\n\r\n$0\r\n\r\nClick Abort to stop the installation,\r\nRetry to try again, or\r\nIgnore to skip this file.
# ^FileError_NoIgnore
Error opening file for writing: \r\n\r\n$0\r\n\r\nClick Retry to try again, or\r\nCancel to stop the installation.
# ^CantWrite
"Can't write: "
# ^CopyFailed
Copy failed
# ^CopyTo
"Copy to "
# ^Registering
"Registering: "
# ^Unregistering
"Unregistering: "
# ^SymbolNotFound
"Could not find symbol: "
# ^CouldNotLoad
"Could not load: "
# ^CreateFolder
"Create folder: "
# ^CreateShortcut
"Create shortcut: "
# ^CreatedUninstaller
"Created uninstaller: "
# ^Delete
"Delete file: "
# ^DeleteOnReboot
"Delete on reboot: "
# ^ErrorCreatingShortcut
"Error creating shortcut: "
# ^ErrorCreating
"Error creating: "
# ^ErrorDecompressing
Error decompressing data! Corrupted installer?
# ^ErrorRegistering
Error registering DLL
# ^ExecShell
"ExecShell: "
# ^Exec
"Execute: "
# ^Extract
"Extract: "
# ^ErrorWriting
"Extract: error writing to file "
# ^InvalidOpcode
Installer corrupted: invalid opcode
# ^NoOLE
"No OLE for: "
# ^OutputFolder
"Output folder: "
# ^RemoveFolder
"Remove folder: "
# ^RenameOnReboot
"Rename on reboot: "
# ^Rename
"Rename: "
# ^Skipped
"Skipped: "
# ^CopyDetails
Copy Details To Clipboard
# ^LogInstall
Log install process
# ^Byte
B
# ^Kilo
 K
# ^Mega
 M
# ^Giga
 G
