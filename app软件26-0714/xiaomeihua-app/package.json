{"name": "xiaomeihua-ai", "productName": "小梅花AI智能客服", "version": "1.0.0", "description": "小梅花AI智能客服 - 支持自动更新", "main": "src/main.js", "scripts": {"start": "electron .", "dev": "cross-env NODE_ENV=development electron .", "build:mac": "electron-builder --mac", "build:win": "electron-builder --win", "build:dmg": "electron-builder --mac dmg", "build:dmg:x64": "electron-builder --mac --x64", "build:dmg:arm64": "electron-builder --mac --arm64", "build:dmg:universal": "electron-builder --mac --universal", "build:all": "electron-builder --mac --win --x64", "clean": "<PERSON><PERSON><PERSON> dist", "package:dmg": "node scripts/package-dmg.js", "package:dmg:enhanced": "node scripts/build-dmg-enhanced.js", "package:dmg:secure": "node scripts/build-dmg-secure.js", "package:dmg:quick": "node scripts/quick-dmg.js", "package:dmg:x64": "node scripts/quick-dmg.js x64", "package:dmg:arm64": "node scripts/quick-dmg.js arm64", "package:dmg:universal": "node scripts/quick-dmg.js universal"}, "author": "小梅花AI科技", "license": "UNLICENSED", "private": true, "build": {"appId": "cn.xiaomeihuakefu.app", "productName": "小梅花AI智能客服", "copyright": "Copyright © 2025 小梅花AI科技", "directories": {"output": "dist"}, "mac": {"category": "public.app-category.business", "target": [{"target": "dmg", "arch": ["x64"]}], "icon": "build/icon.icns", "entitlements": "build/entitlements.mac.plist", "hardenedRuntime": false, "gatekeeperAssess": false, "darkModeSupport": true, "identity": null, "type": "development"}, "dmg": {"title": "${productName} ${version}", "icon": "build/icon.icns", "iconSize": 80, "background": "build/dmg-background.svg", "contents": [{"x": 150, "y": 120, "type": "file", "path": "修复已损坏.command"}, {"x": 150, "y": 280}, {"x": 450, "y": 280, "type": "link", "path": "/Applications"}], "window": {"width": 650, "height": 400, "x": 400, "y": 200}, "sign": false, "writeUpdateInfo": false, "format": "UDZO"}, "win": {"target": "nsis", "icon": "build/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "installerLanguages": ["zh_CN"], "language": "2052"}, "files": ["src/**/*", "package.json", "修复已损坏.command", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}", "!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}", "!**/node_modules/*/{.editorconfig,.gitignore,.travis.yml}", "!**/node_modules/*/{*.d.ts,*.map}", "!**/node_modules/*/docs/**", "!**/node_modules/*/doc/**", "!**/node_modules/*/man/**", "!**/node_modules/*/coverage/**", "!**/node_modules/*/.nyc_output/**", "!**/node_modules/*/bench/**", "!**/node_modules/*/benchmark/**", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,__pycache__,thumbs.db,.gitkeep}", "!build/**", "!scripts/**", "!dist/**", "!release/**", "!test*.js", "!verify*.js", "!debug*.js", "!run*.js", "!simple*.js", "!*.md", "!.giti<PERSON>re", "!.eslintrc*", "!.prettierrc*", "!tsconfig.json", "!false/**", "!*.bat", "!*.crx"], "extraResources": [{"from": "resources/icon.ico", "to": "resources/icon.ico"}], "publish": null, "asarUnpack": ["src/preload-browser.js", "src/preload.js", "src/popup-preload.js", "src/agreement-preload.js", "src/renderer/popup.html", "src/renderer/agreement.html"]}, "devDependencies": {"cross-env": "^7.0.3", "electron": "^28.1.0", "electron-builder": "^24.9.1", "rimraf": "^5.0.5"}, "dependencies": {"axios": "^1.6.2", "electron-store": "^8.1.0", "uuid": "^9.0.1"}}