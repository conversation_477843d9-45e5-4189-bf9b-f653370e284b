#!/usr/bin/env node

/**
 * 重新打包DMG软件 - 完整流程脚本
 * 包含清理、构建、验证、发布的完整自动化流程
 */

const fs = require('fs');
const path = require('path');
const { execSync, spawn } = require('child_process');
const crypto = require('crypto');

class DMGRebuilder {
    constructor() {
        this.projectRoot = process.cwd();
        this.packageJson = require(path.join(this.projectRoot, 'package.json'));
        this.version = this.packageJson.version;
        this.productName = this.packageJson.productName || this.packageJson.name;
        this.distDir = path.join(this.projectRoot, 'dist');
        this.releaseDir = path.join(this.projectRoot, 'release', 'macos');
        
        // 构建时间戳
        this.buildTime = new Date();
        this.buildId = this.buildTime.toISOString().replace(/[:.]/g, '-').slice(0, 19);
    }

    /**
     * 彩色日志输出
     */
    log(message, type = 'info') {
        const colors = {
            info: '\x1b[36m',    // 青色
            success: '\x1b[32m', // 绿色
            warning: '\x1b[33m', // 黄色
            error: '\x1b[31m',   // 红色
            step: '\x1b[35m',    // 紫色
            reset: '\x1b[0m'     // 重置
        };
        
        const icons = {
            info: 'ℹ️',
            success: '✅',
            warning: '⚠️',
            error: '❌',
            step: '🔄'
        };
        
        const timestamp = new Date().toLocaleTimeString('zh-CN');
        console.log(`${colors[type]}[${timestamp}] ${icons[type]} ${message}${colors.reset}`);
    }

    /**
     * 执行命令
     */
    exec(command, options = {}) {
        try {
            this.log(`执行命令: ${command}`, 'info');
            const result = execSync(command, {
                encoding: 'utf8',
                stdio: options.silent ? 'pipe' : 'inherit',
                cwd: this.projectRoot,
                ...options
            });
            return result;
        } catch (error) {
            if (options.ignoreError) {
                this.log(`命令执行失败但忽略错误: ${error.message}`, 'warning');
                return null;
            }
            throw new Error(`命令执行失败: ${command}\n${error.message}`);
        }
    }

    /**
     * 步骤1: 环境检查
     */
    checkEnvironment() {
        this.log('检查构建环境...', 'step');
        
        // 检查操作系统
        this.log(`操作系统: ${process.platform}`, 'info');
        if (process.platform !== 'darwin') {
            this.log('警告: 不在macOS系统上，某些功能可能不可用', 'warning');
        }
        
        // 检查Node.js版本
        this.log(`Node.js版本: ${process.version}`, 'info');
        
        // 检查electron-builder
        try {
            const electronBuilderVersion = this.exec('npx electron-builder --version', { silent: true });
            this.log(`electron-builder版本: ${electronBuilderVersion.trim()}`, 'info');
        } catch (error) {
            throw new Error('electron-builder未安装，请运行: npm install');
        }
        
        // 检查必要文件
        const requiredFiles = [
            'package.json',
            'src/main.js',
            'build/icon.icns'
        ];
        
        for (const file of requiredFiles) {
            const filePath = path.join(this.projectRoot, file);
            if (!fs.existsSync(filePath)) {
                throw new Error(`必要文件不存在: ${file}`);
            }
        }
        
        this.log('环境检查完成', 'success');
    }

    /**
     * 步骤2: 清理旧文件
     */
    cleanOldFiles() {
        this.log('清理旧文件...', 'step');
        
        const dirsToClean = [
            this.distDir,
            this.releaseDir,
            path.join(this.projectRoot, 'node_modules/.cache')
        ];
        
        for (const dir of dirsToClean) {
            if (fs.existsSync(dir)) {
                fs.rmSync(dir, { recursive: true, force: true });
                this.log(`已清理: ${path.relative(this.projectRoot, dir)}`, 'success');
            }
        }
    }

    /**
     * 步骤3: 安装依赖
     */
    installDependencies() {
        this.log('安装/更新依赖...', 'step');
        
        // 检查package-lock.json是否存在
        const lockFile = path.join(this.projectRoot, 'package-lock.json');
        if (fs.existsSync(lockFile)) {
            this.exec('npm ci');
        } else {
            this.exec('npm install');
        }
        
        this.log('依赖安装完成', 'success');
    }

    /**
     * 步骤4: 构建DMG
     */
    async buildDMG() {
        this.log('开始构建DMG...', 'step');
        
        // 构建不同架构的版本
        const targets = [
            { name: 'Intel (x64)', command: 'npx electron-builder --mac --x64' },
            { name: 'Apple Silicon (arm64)', command: 'npx electron-builder --mac --arm64' }
        ];
        
        for (const target of targets) {
            this.log(`构建${target.name}版本...`, 'info');
            this.exec(target.command);
            this.log(`${target.name}版本构建完成`, 'success');
        }
    }

    /**
     * 步骤5: 验证构建产物
     */
    verifyBuildArtifacts() {
        this.log('验证构建产物...', 'step');
        
        if (!fs.existsSync(this.distDir)) {
            throw new Error('dist目录不存在');
        }
        
        const files = fs.readdirSync(this.distDir);
        const dmgFiles = files.filter(file => file.endsWith('.dmg'));
        
        if (dmgFiles.length === 0) {
            throw new Error('未找到DMG文件');
        }
        
        this.log(`找到${dmgFiles.length}个DMG文件:`, 'success');
        
        const fileInfo = [];
        dmgFiles.forEach(file => {
            const filePath = path.join(this.distDir, file);
            const stats = fs.statSync(filePath);
            const sizeInMB = (stats.size / 1024 / 1024).toFixed(1);
            
            fileInfo.push({
                name: file,
                size: stats.size,
                sizeFormatted: `${sizeInMB}MB`,
                path: filePath
            });
            
            this.log(`  ${file} (${sizeInMB}MB)`, 'info');
        });
        
        return fileInfo;
    }

    /**
     * 步骤6: DMG文件验证
     */
    verifyDMGFiles(fileInfo) {
        if (process.platform !== 'darwin') {
            this.log('跳过DMG验证（非macOS系统）', 'warning');
            return;
        }
        
        this.log('验证DMG文件完整性...', 'step');
        
        fileInfo.forEach(file => {
            try {
                this.exec(`hdiutil verify "${file.path}"`, { silent: true });
                this.log(`${file.name} 验证通过`, 'success');
            } catch (error) {
                this.log(`${file.name} 验证失败: ${error.message}`, 'warning');
            }
        });
    }

    /**
     * 步骤7: 生成校验和
     */
    generateChecksums(fileInfo) {
        this.log('生成文件校验和...', 'step');
        
        const checksums = {};
        
        fileInfo.forEach(file => {
            const fileBuffer = fs.readFileSync(file.path);
            
            checksums[file.name] = {
                md5: crypto.createHash('md5').update(fileBuffer).digest('hex'),
                sha1: crypto.createHash('sha1').update(fileBuffer).digest('hex'),
                sha256: crypto.createHash('sha256').update(fileBuffer).digest('hex'),
                size: file.size,
                sizeFormatted: file.sizeFormatted
            };
            
            this.log(`${file.name} 校验和已生成`, 'success');
        });
        
        return checksums;
    }

    /**
     * 步骤8: 准备发布文件
     */
    prepareRelease(fileInfo, checksums) {
        this.log('准备发布文件...', 'step');
        
        // 创建发布目录
        if (!fs.existsSync(this.releaseDir)) {
            fs.mkdirSync(this.releaseDir, { recursive: true });
        }
        
        // 复制DMG文件和相关文件
        fileInfo.forEach(file => {
            const destPath = path.join(this.releaseDir, file.name);
            fs.copyFileSync(file.path, destPath);
            this.log(`已复制: ${file.name}`, 'success');
            
            // 复制blockmap文件
            const blockmapFile = file.name + '.blockmap';
            const blockmapSource = path.join(this.distDir, blockmapFile);
            const blockmapDest = path.join(this.releaseDir, blockmapFile);
            
            if (fs.existsSync(blockmapSource)) {
                fs.copyFileSync(blockmapSource, blockmapDest);
                this.log(`已复制: ${blockmapFile}`, 'success');
            }
        });
        
        // 保存校验和文件
        const checksumPath = path.join(this.releaseDir, 'checksums.json');
        fs.writeFileSync(checksumPath, JSON.stringify(checksums, null, 2));
        this.log('已生成校验和文件', 'success');
    }

    /**
     * 步骤9: 生成发布文档
     */
    generateReleaseDocuments() {
        this.log('生成发布文档...', 'step');
        
        // 生成README
        const readme = this.generateReadme();
        const readmePath = path.join(this.releaseDir, 'README.md');
        fs.writeFileSync(readmePath, readme, 'utf8');
        
        // 生成构建报告
        const buildReport = this.generateBuildReport();
        const reportPath = path.join(this.releaseDir, 'build-report.json');
        fs.writeFileSync(reportPath, JSON.stringify(buildReport, null, 2));
        
        this.log('发布文档已生成', 'success');
    }

    /**
     * 生成README文档
     */
    generateReadme() {
        return `# ${this.productName} v${this.version} - macOS版本

## 📦 安装包信息
- **产品名称**: ${this.productName}
- **版本号**: ${this.version}
- **构建时间**: ${this.buildTime.toLocaleString('zh-CN')}
- **构建ID**: ${this.buildId}

## 💻 系统要求
- macOS 10.15 (Catalina) 或更高版本
- Intel 或 Apple Silicon (M1/M2/M3) 处理器
- 至少 200MB 可用磁盘空间

## 🚀 安装步骤
1. 下载对应架构的DMG文件
   - **Intel Mac**: 选择包含 "x64" 的文件
   - **Apple Silicon Mac**: 选择包含 "arm64" 的文件
2. 双击打开DMG文件
3. 将应用程序拖拽到Applications文件夹
4. 在Launchpad或Applications文件夹中启动应用

## 🔒 安全说明
首次运行时，macOS可能显示安全警告：
1. 打开"系统偏好设置" > "安全性与隐私"
2. 在"通用"标签页中点击"仍要打开"
3. 或在终端中运行: \`sudo xattr -rd com.apple.quarantine "/Applications/${this.productName}.app"\`

## ✨ 主要功能
- AI智能客服系统
- 多店铺管理
- 自动化脚本执行
- 数据同步和备份
- 自动更新功能
- 原生macOS体验

## 📞 技术支持
如遇问题，请联系技术支持团队。

---
*构建时间: ${this.buildTime.toISOString()}*
`;
    }

    /**
     * 生成构建报告
     */
    generateBuildReport() {
        return {
            buildTime: this.buildTime.toISOString(),
            buildId: this.buildId,
            version: this.version,
            productName: this.productName,
            platform: 'macOS',
            nodeVersion: process.version,
            electronBuilderVersion: this.exec('npx electron-builder --version', { silent: true }).trim(),
            environment: {
                os: process.platform,
                arch: process.arch,
                cwd: this.projectRoot
            }
        };
    }

    /**
     * 显示最终结果
     */
    showResults() {
        console.log('\n' + '='.repeat(60));
        this.log('🎉 DMG重新打包完成！', 'success');
        console.log('='.repeat(60));
        
        console.log(`\n📋 构建信息:`);
        console.log(`  产品: ${this.productName}`);
        console.log(`  版本: ${this.version}`);
        console.log(`  构建时间: ${this.buildTime.toLocaleString('zh-CN')}`);
        console.log(`  构建ID: ${this.buildId}`);
        
        console.log(`\n📁 发布目录: ${this.releaseDir}`);
        
        // 显示生成的文件
        if (fs.existsSync(this.releaseDir)) {
            const files = fs.readdirSync(this.releaseDir);
            console.log(`\n📦 生成的文件 (${files.length}个):`);
            files.forEach(file => {
                const filePath = path.join(this.releaseDir, file);
                const stats = fs.statSync(filePath);
                const sizeInMB = (stats.size / 1024 / 1024).toFixed(1);
                console.log(`  ${file} ${sizeInMB > 0 ? `(${sizeInMB}MB)` : ''}`);
            });
        }
        
        console.log(`\n💡 下一步操作:`);
        console.log(`  1. 测试DMG文件安装`);
        console.log(`  2. 验证应用功能`);
        console.log(`  3. 准备分发或上传`);
        
        if (process.platform === 'darwin') {
            console.log(`\n🔍 快速查看:`);
            console.log(`  open "${this.releaseDir}"`);
        }
    }

    /**
     * 主构建流程
     */
    async rebuild() {
        try {
            console.log('🚀 开始重新打包DMG软件...\n');
            
            // 执行构建步骤
            this.checkEnvironment();
            this.cleanOldFiles();
            this.installDependencies();
            await this.buildDMG();
            
            const fileInfo = this.verifyBuildArtifacts();
            this.verifyDMGFiles(fileInfo);
            
            const checksums = this.generateChecksums(fileInfo);
            this.prepareRelease(fileInfo, checksums);
            this.generateReleaseDocuments();
            
            this.showResults();
            
        } catch (error) {
            this.log(`构建失败: ${error.message}`, 'error');
            console.error('\n详细错误信息:');
            console.error(error.stack);
            process.exit(1);
        }
    }
}

// 主程序入口
async function main() {
    const rebuilder = new DMGRebuilder();
    await rebuilder.rebuild();
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = DMGRebuilder;
