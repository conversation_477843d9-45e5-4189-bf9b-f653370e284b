const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('准备启动小梅花智能AI系统...');

// 确保资源目录存在
const resourcesDir = path.join(__dirname, 'resources');
if (!fs.existsSync(resourcesDir)) {
  fs.mkdirSync(resourcesDir, { recursive: true });
}

// 确保Tampermonkey扩展存在
const tampermonkeySource = path.join(__dirname, '..', 'tampermonkey_stable.crx');
const tampermonkeyDest = path.join(resourcesDir, 'tampermonkey_stable.crx');

if (!fs.existsSync(tampermonkeyDest) && fs.existsSync(tampermonkeySource)) {
  console.log('复制Tampermonkey扩展...');
  fs.copyFileSync(tampermonkeySource, tampermonkeyDest);
}

// 确保卡密验证脚本存在
const kamiyanSource = path.join(__dirname, '..', 'kamiyanzheng.js');
const kamiyanDest = path.join(resourcesDir, 'kamiyanzheng.js');

if (!fs.existsSync(kamiyanDest) && fs.existsSync(kamiyanSource)) {
  console.log('复制卡密验证脚本...');
  fs.copyFileSync(kamiyanSource, kamiyanDest);
}

// 确保build目录存在
const buildDir = path.join(__dirname, 'build');
if (!fs.existsSync(buildDir)) {
  fs.mkdirSync(buildDir);
}

// 复制图标
const iconSource = path.join(__dirname, 'src/assets/logo.png');
const iconDest = path.join(buildDir, 'icon.png');

if (!fs.existsSync(iconDest) && fs.existsSync(iconSource)) {
  console.log('复制图标...');
  fs.copyFileSync(iconSource, iconDest);
}

// 设置开发环境变量
process.env.NODE_ENV = 'development';

// 启动应用
console.log('启动应用...');
try {
  execSync('npx electron .', { stdio: 'inherit' });
} catch (error) {
  console.error('启动失败:', error.message);
  process.exit(1);
} 