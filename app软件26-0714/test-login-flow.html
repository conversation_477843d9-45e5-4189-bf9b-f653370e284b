<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试登录流程修复</title>
    <style>
        body {
            font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
            margin: 0;
        }
        .test-container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .test-header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        .test-header p {
            color: #666;
            font-size: 16px;
        }
        .test-section {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            background: #fafbfc;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
            font-size: 18px;
        }
        .test-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 8px;
            transition: all 0.3s ease;
        }
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        .test-btn.danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        }
        .test-btn.danger:hover {
            box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
        }
        .test-btn.success {
            background: linear-gradient(135deg, #51cf66 0%, #40c057 100%);
        }
        .test-btn.success:hover {
            box-shadow: 0 4px 12px rgba(81, 207, 102, 0.4);
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 6px;
            font-weight: 500;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .flow-steps {
            display: flex;
            justify-content: space-between;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .flow-step {
            flex: 1;
            min-width: 200px;
            margin: 10px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 2px solid #e1e5e9;
            text-align: center;
            transition: all 0.3s ease;
        }
        .flow-step.active {
            border-color: #667eea;
            background: #f8f9ff;
        }
        .flow-step.completed {
            border-color: #51cf66;
            background: #f8fff9;
        }
        .flow-step.error {
            border-color: #ff6b6b;
            background: #fff8f8;
        }
        .step-number {
            display: inline-block;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #667eea;
            color: white;
            line-height: 30px;
            margin-bottom: 10px;
            font-weight: bold;
        }
        .step-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .step-desc {
            font-size: 14px;
            color: #666;
        }
        .test-results {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e1e5e9;
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🔧 店铺退出登录修复测试</h1>
            <p>测试彻底修复店铺退出后无法重新登录的问题</p>
        </div>

        <div class="progress-bar">
            <div class="progress-fill" id="progress"></div>
        </div>

        <div class="flow-steps">
            <div class="flow-step" id="step1">
                <div class="step-number">1</div>
                <div class="step-title">模拟登录状态</div>
                <div class="step-desc">创建登录状态和数据</div>
            </div>
            <div class="flow-step" id="step2">
                <div class="step-number">2</div>
                <div class="step-title">模拟退出登录</div>
                <div class="step-desc">触发退出登录清理</div>
            </div>
            <div class="flow-step" id="step3">
                <div class="step-number">3</div>
                <div class="step-title">验证清理效果</div>
                <div class="step-desc">检查数据是否清理干净</div>
            </div>
            <div class="flow-step" id="step4">
                <div class="step-number">4</div>
                <div class="step-title">模拟重新登录</div>
                <div class="step-desc">测试是否能正常登录</div>
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 完整流程测试</h3>
            <p>点击下面的按钮开始完整的退出登录修复测试</p>
            <button class="test-btn success" onclick="startCompleteTest()">开始完整测试</button>
            <button class="test-btn" onclick="resetTest()">重置测试</button>
            <div id="complete-test-status" class="status info">点击"开始完整测试"按钮开始测试</div>
        </div>

        <div class="test-section">
            <h3>🔍 单步测试</h3>
            <p>可以单独测试每个步骤</p>
            <button class="test-btn" onclick="simulateLogin()">1. 模拟登录</button>
            <button class="test-btn danger" onclick="simulateLogout()">2. 模拟退出</button>
            <button class="test-btn" onclick="checkCleanup()">3. 检查清理</button>
            <button class="test-btn success" onclick="simulateRelogin()">4. 重新登录</button>
            <div id="step-test-status" class="status info">选择要测试的步骤</div>
        </div>

        <div class="test-section">
            <h3>📊 数据状态监控</h3>
            <button class="test-btn" onclick="checkDataStatus()">检查当前数据状态</button>
            <div id="data-status" class="status info">点击按钮检查当前数据状态</div>
        </div>

        <div class="test-results">
            <h3>📋 测试结果</h3>
            <div id="test-results">等待测试开始...</div>
        </div>
    </div>

    <script>
        let currentStep = 0;
        let testResults = [];

        function updateProgress(step) {
            const progress = document.getElementById('progress');
            const percentage = (step / 4) * 100;
            progress.style.width = percentage + '%';
            
            // 更新步骤状态
            for (let i = 1; i <= 4; i++) {
                const stepEl = document.getElementById(`step${i}`);
                stepEl.classList.remove('active', 'completed', 'error');
                
                if (i < step) {
                    stepEl.classList.add('completed');
                } else if (i === step) {
                    stepEl.classList.add('active');
                }
            }
        }

        function addResult(step, status, message) {
            testResults.push({
                step: step,
                status: status,
                message: message,
                timestamp: new Date().toLocaleTimeString()
            });
            updateResultsDisplay();
        }

        function updateResultsDisplay() {
            const resultsDiv = document.getElementById('test-results');
            if (testResults.length === 0) {
                resultsDiv.innerHTML = '等待测试开始...';
                return;
            }

            let html = '<div style="max-height: 300px; overflow-y: auto;">';
            testResults.forEach((result, index) => {
                const statusClass = result.status === 'success' ? 'success' : 
                                  result.status === 'error' ? 'error' : 'info';
                const icon = result.status === 'success' ? '✅' : 
                           result.status === 'error' ? '❌' : 'ℹ️';
                
                html += `
                    <div style="margin: 8px 0; padding: 8px; border-left: 3px solid ${
                        result.status === 'success' ? '#51cf66' : 
                        result.status === 'error' ? '#ff6b6b' : '#667eea'
                    }; background: ${
                        result.status === 'success' ? '#f8fff9' : 
                        result.status === 'error' ? '#fff8f8' : '#f8f9ff'
                    };">
                        <strong>${icon} 步骤${result.step}: ${result.message}</strong>
                        <div style="font-size: 12px; color: #666; margin-top: 4px;">
                            ${result.timestamp}
                        </div>
                    </div>
                `;
            });
            html += '</div>';
            resultsDiv.innerHTML = html;
        }

        async function startCompleteTest() {
            testResults = [];
            currentStep = 1;
            updateProgress(1);
            
            document.getElementById('complete-test-status').innerHTML = 
                '<span style="color: blue;">🔄 开始完整测试流程...</span>';
            document.getElementById('complete-test-status').className = 'status info';

            try {
                // 步骤1: 模拟登录
                await simulateLogin();
                await sleep(1000);

                // 步骤2: 模拟退出
                currentStep = 2;
                updateProgress(2);
                await simulateLogout();
                await sleep(2000);

                // 步骤3: 检查清理
                currentStep = 3;
                updateProgress(3);
                await checkCleanup();
                await sleep(1000);

                // 步骤4: 重新登录
                currentStep = 4;
                updateProgress(4);
                await simulateRelogin();

                // 完成
                updateProgress(5);
                document.getElementById('complete-test-status').innerHTML = 
                    '<span style="color: green;">✅ 完整测试流程完成！所有步骤都正常执行</span>';
                document.getElementById('complete-test-status').className = 'status success';

            } catch (error) {
                document.getElementById('complete-test-status').innerHTML = 
                    `<span style="color: red;">❌ 测试过程中出现错误: ${error.message}</span>`;
                document.getElementById('complete-test-status').className = 'status error';
                addResult(currentStep, 'error', `测试失败: ${error.message}`);
            }
        }

        function simulateLogin() {
            return new Promise((resolve) => {
                console.log('🔑 模拟登录状态...');
                
                // 模拟设置登录相关数据
                localStorage.setItem('xiaomeihua_login_detected', Date.now().toString());
                localStorage.setItem('login_method', 'scan_code');
                localStorage.setItem('shop_id', 'test_shop_123');
                localStorage.setItem('shop_name', '测试店铺');
                localStorage.setItem('session_token', 'test_token_' + Date.now());
                localStorage.setItem('auth_cookie', 'test_cookie_' + Date.now());
                
                sessionStorage.setItem('current_session', 'active');
                sessionStorage.setItem('login_timestamp', Date.now().toString());
                
                // 模拟设置cookies
                document.cookie = "login_ticket=test_ticket_" + Date.now() + "; path=/";
                document.cookie = "session_id=test_session_" + Date.now() + "; path=/";
                
                addResult(1, 'success', '模拟登录状态创建完成');
                document.getElementById('step-test-status').innerHTML = 
                    '<span style="color: green;">✅ 模拟登录状态创建完成</span>';
                document.getElementById('step-test-status').className = 'status success';
                
                resolve();
            });
        }

        function simulateLogout() {
            return new Promise((resolve) => {
                console.log('🚪 模拟退出登录...');
                
                // 模拟退出登录的清理过程
                performLogoutCleanup();
                
                addResult(2, 'success', '模拟退出登录和清理完成');
                document.getElementById('step-test-status').innerHTML = 
                    '<span style="color: green;">✅ 模拟退出登录和清理完成</span>';
                document.getElementById('step-test-status').className = 'status success';
                
                resolve();
            });
        }

        function performLogoutCleanup() {
            // 清理localStorage
            const localKeys = Object.keys(localStorage);
            let localCleared = 0;
            localKeys.forEach(key => {
                if (key.includes('shop') || 
                    key.includes('store') || 
                    key.includes('login') || 
                    key.includes('auth') || 
                    key.includes('token') ||
                    key.includes('session') ||
                    key.includes('cookie') ||
                    key.includes('xiaomeihua')) {
                    localStorage.removeItem(key);
                    localCleared++;
                }
            });

            // 清理sessionStorage
            const sessionKeys = Object.keys(sessionStorage);
            let sessionCleared = 0;
            sessionKeys.forEach(key => {
                if (key.includes('shop') || 
                    key.includes('store') || 
                    key.includes('login') || 
                    key.includes('auth') || 
                    key.includes('token') ||
                    key.includes('session') ||
                    key.includes('cookie') ||
                    key.includes('xiaomeihua')) {
                    sessionStorage.removeItem(key);
                    sessionCleared++;
                }
            });

            // 清理cookies
            let cookieCleared = 0;
            if (document.cookie) {
                document.cookie.split(";").forEach(function(c) { 
                    const eqPos = c.indexOf("=");
                    const name = eqPos > -1 ? c.substr(0, eqPos).trim() : c.trim();
                    if (name.includes('login') || name.includes('session') || name.includes('auth') || name.includes('token')) {
                        document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/";
                        cookieCleared++;
                    }
                });
            }

            // 设置退出登录标记
            localStorage.setItem('logout_detected', Date.now().toString());
            
            console.log(`清理完成: localStorage(${localCleared}), sessionStorage(${sessionCleared}), cookies(${cookieCleared})`);
        }

        function checkCleanup() {
            return new Promise((resolve) => {
                console.log('🔍 检查清理效果...');
                
                const logoutDetected = localStorage.getItem('logout_detected');
                const remainingLocalKeys = Object.keys(localStorage).filter(key => 
                    key.includes('shop') || key.includes('login') || key.includes('auth') || key.includes('session')
                );
                const remainingSessionKeys = Object.keys(sessionStorage).filter(key => 
                    key.includes('shop') || key.includes('login') || key.includes('auth') || key.includes('session')
                );

                if (logoutDetected && remainingLocalKeys.length <= 1 && remainingSessionKeys.length === 0) {
                    addResult(3, 'success', '数据清理验证通过，退出登录标记存在，相关数据已清理');
                    document.getElementById('step-test-status').innerHTML = 
                        '<span style="color: green;">✅ 数据清理验证通过</span>';
                    document.getElementById('step-test-status').className = 'status success';
                } else {
                    addResult(3, 'error', `数据清理验证失败，残留数据: localStorage(${remainingLocalKeys.length}), sessionStorage(${remainingSessionKeys.length})`);
                    document.getElementById('step-test-status').innerHTML = 
                        '<span style="color: red;">❌ 数据清理验证失败</span>';
                    document.getElementById('step-test-status').className = 'status error';
                }
                
                resolve();
            });
        }

        function simulateRelogin() {
            return new Promise((resolve) => {
                console.log('🔄 模拟重新登录...');
                
                // 清除退出登录标记
                localStorage.removeItem('logout_detected');
                
                // 设置新的登录状态
                localStorage.setItem('xiaomeihua_login_detected', Date.now().toString());
                localStorage.setItem('login_method', 'scan_code_new');
                localStorage.setItem('new_session_token', 'new_token_' + Date.now());
                
                sessionStorage.setItem('new_session', 'active');
                
                // 模拟新的cookies
                document.cookie = "new_login_ticket=new_ticket_" + Date.now() + "; path=/";
                
                addResult(4, 'success', '模拟重新登录成功，新的登录状态已建立');
                document.getElementById('step-test-status').innerHTML = 
                    '<span style="color: green;">✅ 模拟重新登录成功</span>';
                document.getElementById('step-test-status').className = 'status success';
                
                resolve();
            });
        }

        function checkDataStatus() {
            const localKeys = Object.keys(localStorage);
            const sessionKeys = Object.keys(sessionStorage);
            const cookieCount = document.cookie ? document.cookie.split(';').length : 0;
            
            const relevantLocalKeys = localKeys.filter(key => 
                key.includes('shop') || key.includes('login') || key.includes('auth') || 
                key.includes('session') || key.includes('xiaomeihua') || key.includes('logout')
            );
            
            const relevantSessionKeys = sessionKeys.filter(key => 
                key.includes('shop') || key.includes('login') || key.includes('auth') || 
                key.includes('session') || key.includes('xiaomeihua')
            );

            let statusHtml = `
                <div style="font-family: monospace; font-size: 14px;">
                    <div><strong>📊 当前数据状态:</strong></div>
                    <div>• localStorage总数: ${localKeys.length}</div>
                    <div>• sessionStorage总数: ${sessionKeys.length}</div>
                    <div>• Cookie总数: ${cookieCount}</div>
                    <div>• 相关localStorage: ${relevantLocalKeys.length}</div>
                    <div>• 相关sessionStorage: ${relevantSessionKeys.length}</div>
                    <div>• 退出登录标记: ${localStorage.getItem('logout_detected') ? '存在' : '不存在'}</div>
                    <div>• 登录检测标记: ${localStorage.getItem('xiaomeihua_login_detected') ? '存在' : '不存在'}</div>
            `;

            if (relevantLocalKeys.length > 0) {
                statusHtml += `<div style="margin-top: 10px;"><strong>相关localStorage项:</strong></div>`;
                relevantLocalKeys.forEach(key => {
                    statusHtml += `<div style="margin-left: 20px;">• ${key}</div>`;
                });
            }

            if (relevantSessionKeys.length > 0) {
                statusHtml += `<div style="margin-top: 10px;"><strong>相关sessionStorage项:</strong></div>`;
                relevantSessionKeys.forEach(key => {
                    statusHtml += `<div style="margin-left: 20px;">• ${key}</div>`;
                });
            }

            statusHtml += '</div>';

            document.getElementById('data-status').innerHTML = statusHtml;
            document.getElementById('data-status').className = 'status info';
        }

        function resetTest() {
            testResults = [];
            currentStep = 0;
            updateProgress(0);
            
            // 清理所有测试数据
            localStorage.clear();
            sessionStorage.clear();
            
            // 清理cookies
            document.cookie.split(";").forEach(function(c) { 
                const eqPos = c.indexOf("=");
                const name = eqPos > -1 ? c.substr(0, eqPos).trim() : c.trim();
                document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/";
            });
            
            document.getElementById('complete-test-status').innerHTML = 
                '<span style="color: blue;">🔄 测试已重置，可以重新开始测试</span>';
            document.getElementById('complete-test-status').className = 'status info';
            
            document.getElementById('step-test-status').innerHTML = '测试已重置';
            document.getElementById('step-test-status').className = 'status info';
            
            document.getElementById('data-status').innerHTML = '测试已重置';
            document.getElementById('data-status').className = 'status info';
            
            updateResultsDisplay();
        }

        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // 初始化
        updateProgress(0);
        updateResultsDisplay();
    </script>
</body>
</html>
