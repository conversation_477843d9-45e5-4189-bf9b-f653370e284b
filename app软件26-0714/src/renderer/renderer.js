const { ipc<PERSON>enderer } = require('electron');

document.addEventListener('DOMContentLoaded', () => {
    const tabContainer = document.getElementById('tab-container');
    const webviewContainer = document.getElementById('webview-container');
    const closeBtn = document.getElementById('close-btn');
    const minimizeBtn = document.getElementById('minimize-btn');
    const winCloseBtn = document.getElementById('win-close-btn');
    const winMinimizeBtn = document.getElementById('win-minimize-btn');
    const winControls = document.querySelector('.win-controls');
    const trafficLights = document.querySelector('.traffic-lights');

    let tabs = {};
    let activeTabId = null;

    // 检测平台并设置适当的控制按钮可见性
    const platform = navigator.platform.toLowerCase();
    const isWindows = platform.includes('win');
    const isMac = platform.includes('mac');

    // 根据平台显示/隐藏相应的控制按钮
    if (isWindows) {
        if (trafficLights) trafficLights.style.visibility = 'hidden';
        if (winControls) winControls.style.display = 'flex';
    } else {
        if (winControls) winControls.style.display = 'none';
    }

    // --- Window Controls ---
    // macOS控制按钮
    if (closeBtn) closeBtn.addEventListener('click', () => ipcRenderer.send('close-window'));
    if (minimizeBtn) minimizeBtn.addEventListener('click', () => ipcRenderer.send('minimize-window'));
    
    // Windows控制按钮
    if (winCloseBtn) winCloseBtn.addEventListener('click', () => ipcRenderer.send('close-window'));
    if (winMinimizeBtn) winMinimizeBtn.addEventListener('click', () => ipcRenderer.send('minimize-window'));

    // --- IPC Listeners ---
    ipcRenderer.on('create-tab', (event, { url, title }) => {
        console.log(`Renderer: Received create-tab for URL: ${url}`);
        createNewTab(url, title, true);
    });

    // --- Tab Management ---
    function createNewTab(url, title = '新标签页', activate = true) {
        const tabId = `tab-${Date.now()}`;

        // Create Tab Element
        const tabEl = document.createElement('div');
        tabEl.className = 'tab';
        tabEl.dataset.tabId = tabId;
        tabEl.innerHTML = `
            <span class="tab-title">${title}</span>
            <span class="tab-close">×</span>
        `;
        tabContainer.appendChild(tabEl);

        // Create Webview Element
        const webviewEl = document.createElement('webview');
        webviewEl.id = tabId;
        webviewEl.src = url;
        webviewEl.setAttribute('preload', `file://${require('path').resolve(__dirname, '..', 'preload-browser.js')}`);
        webviewEl.setAttribute('allowpopups', 'true'); // Allow popups but intercept them
        webviewContainer.appendChild(webviewEl);

        // Store tab info
        tabs[tabId] = { tabEl, webviewEl };

        // Add event listeners
        addTabEventListeners(tabId);
        addWebviewEventListeners(tabId);
        
        if (activate) {
            activateTab(tabId);
        }
    }

    function activateTab(tabId) {
        if (activeTabId) {
            tabs[activeTabId].tabEl.classList.remove('active');
            tabs[activeTabId].webviewEl.classList.remove('active');
        }
        
        activeTabId = tabId;
        tabs[tabId].tabEl.classList.add('active');
        tabs[tabId].webviewEl.classList.add('active');
    }

    function closeTab(tabId) {
        const { tabEl, webviewEl } = tabs[tabId];

        tabEl.remove();
        webviewEl.remove();
        delete tabs[tabId];
        
        // If the closed tab was active, activate another tab
        if (activeTabId === tabId) {
            const remainingTabIds = Object.keys(tabs);
            if (remainingTabIds.length > 0) {
                activateTab(remainingTabIds[0]);
            } else {
                activeTabId = null;
                // Optionally, close the window if the last tab is closed
                // ipcRenderer.send('close-window'); 
            }
        }
    }

    // --- Event Listeners ---
    function addTabEventListeners(tabId) {
        const { tabEl } = tabs[tabId];
        
        // Click to activate
        tabEl.addEventListener('click', (e) => {
            if (e.target.className !== 'tab-close') {
                activateTab(tabId);
            }
        });

        // Click to close
        tabEl.querySelector('.tab-close').addEventListener('click', (e) => {
            e.stopPropagation(); // prevent the tab activation
            closeTab(tabId);
        });
    }

    function addWebviewEventListeners(tabId) {
        const { webviewEl, tabEl } = tabs[tabId];

        webviewEl.addEventListener('did-finish-load', () => {
            const pageTitle = webviewEl.getTitle();
            tabEl.querySelector('.tab-title').textContent = pageTitle;
            tabEl.querySelector('.tab-title').title = pageTitle;
        });

        webviewEl.addEventListener('page-title-updated', (e) => {
            const pageTitle = e.title;
            tabEl.querySelector('.tab-title').textContent = pageTitle;
            tabEl.querySelector('.tab-title').title = pageTitle;
        });
        
        // Final fallback for popups
        webviewEl.addEventListener('new-window', (e) => {
            e.preventDefault();
            console.log(`Webview new-window event intercepted for URL: ${e.url}`);
            createNewTab(e.url, '新页面', true);
        });
    }

    // --- Initial Tab ---
    // 不再自动创建店铺客服标签页
}); 