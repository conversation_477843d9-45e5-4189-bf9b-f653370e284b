// ==UserScript==
// @name         小梅花AI客服助手
// @namespace    http://xiaomeihuakefu.cn/
// @version      1.0.0
// @description  小梅花智能AI客服助手
// <AUTHOR>
// @match        https://store.weixin.qq.com/shop/kf*
// @grant        GM_xmlhttpRequest
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_deleteValue
// @grant        GM_addStyle
// @grant        GM_openInTab
// @grant        window.open
// @grant        unsafeWindow
// @connect      xiaomeihuakefu.cn
// @connect      *
// ==/UserScript==

(function() {
    'use strict';

    console.log('=== 小梅花AI客服助手调试版启动 ===');

    let isScriptLoaded = false;
    let currentKey = null;
    let controlPanel = null;
    let floatingIcon = null;
    let isAuthenticated = false;
    let keyStatusMonitor = null; // 卡密状态监控定时器
    let configUpdateMonitor = null; // 配置更新监控定时器
    let apiKeyUpdateMonitor = null; // API密钥更新监控定时器

    // 【新增】动态API配置系统
    const API_CONFIG = {
        // 服务器地址（从后台API配置获取）
        servers: {
            primary: 'https://xiaomeihuakefu.cn',
            backup: 'https://api.xiaomeihuakefu.cn',
            secure: 'https://secure.xiaomeihuakefu.cn'
        },
        // 当前使用的服务器
        current: 'https://xiaomeihuakefu.cn',
        // API端点
        endpoints: {
            verify: '/api/verify.php',
            status: '/api/status.php',
            heartbeat: '/api/heartbeat.php',
            key_info: '/api/key_info.php',
            config: '/api/config.php'
        },
        // API密钥（动态获取）
        api_key: null,
        // 配置版本
        version: 'v2',
        // 最后更新时间
        last_update: 0,
        // 更新间隔（5分钟）
        update_interval: 5 * 60 * 1000,

        // 初始化配置
        async init() {
            console.log('🔧 初始化API配置系统...');

            // 从本地存储加载配置
            this.loadFromStorage();

            // 获取最新配置
            await this.updateConfig();

            console.log('✅ API配置系统初始化完成');
            this.logCurrentConfig();
        },

        // 从本地存储加载配置
        loadFromStorage() {
            try {
                const stored = GM_getValue('api_config', null);
                if (stored) {
                    const config = JSON.parse(stored);
                    if (config.servers) {
                        this.servers = { ...this.servers, ...config.servers };
                    }
                    if (config.api_key) {
                        this.api_key = config.api_key;
                    }
                    if (config.version) {
                        this.version = config.version;
                    }
                    if (config.last_update) {
                        this.last_update = config.last_update;
                    }
                    console.log('📦 从本地存储加载API配置');
                }
            } catch (e) {
                console.warn('⚠️ 加载本地API配置失败:', e);
            }
        },

        // 保存配置到本地存储
        saveToStorage() {
            try {
                const config = {
                    servers: this.servers,
                    api_key: this.api_key,
                    version: this.version,
                    last_update: Date.now()
                };
                GM_setValue('api_config', JSON.stringify(config));
                console.log('💾 API配置已保存到本地');
            } catch (e) {
                console.error('❌ 保存API配置失败:', e);
            }
        },

        // 更新配置
        async updateConfig() {
            console.log('🔄 更新API配置...');

            // 尝试从各个服务器获取配置
            const servers = [this.servers.primary, this.servers.backup, this.servers.secure];

            for (const server of servers) {
                try {
                    const config = await this.fetchConfigFromServer(server);
                    if (config) {
                        this.applyConfig(config);
                        this.current = server;
                        this.saveToStorage();
                        console.log(`✅ 从服务器 ${server} 获取配置成功`);
                        return true;
                    }
                } catch (e) {
                    console.warn(`⚠️ 从服务器 ${server} 获取配置失败:`, e);
                }
            }

            console.warn('⚠️ 所有服务器配置获取失败，使用本地配置');
            return false;
        },

        // 从服务器获取配置
        async fetchConfigFromServer(serverUrl) {
            return new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('配置获取超时'));
                }, 10000);

                GM_xmlhttpRequest({
                    method: 'POST',
                    url: `${serverUrl}${this.endpoints.key_info}`,
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
                        'Accept': 'application/json'
                    },
                    data: `key=${encodeURIComponent(currentKey || 'config_request')}&action=get_config`,
                    timeout: 10000,
                    onload: (response) => {
                        clearTimeout(timeout);

                        if (response.status === 200) {
                            try {
                                const data = JSON.parse(response.responseText);
                                if (data.success && data.api_config) {
                                    resolve(data.api_config);
                                } else {
                                    reject(new Error('配置响应格式错误'));
                                }
                            } catch (e) {
                                reject(new Error('配置解析失败'));
                            }
                        } else {
                            reject(new Error(`HTTP ${response.status}`));
                        }
                    },
                    onerror: () => {
                        clearTimeout(timeout);
                        reject(new Error('网络请求失败'));
                    },
                    ontimeout: () => {
                        clearTimeout(timeout);
                        reject(new Error('请求超时'));
                    }
                });
            });
        },

        // 应用配置
        applyConfig(config) {
            console.log('🔧 应用新配置:', config);

            // 更新服务器地址
            if (config.servers) {
                this.servers = { ...this.servers, ...config.servers };
            }

            // 更新API密钥
            if (config.api_key && config.api_key !== this.api_key) {
                console.log('🔑 检测到API密钥更新');
                this.api_key = config.api_key;

                // 触发API密钥更新事件
                this.onApiKeyUpdated();
            }

            // 更新版本
            if (config.version) {
                this.version = config.version;
            }

            this.last_update = Date.now();
        },

        // API密钥更新事件处理
        onApiKeyUpdated() {
            console.log('🔄 API密钥已更新，重新初始化连接...');

            // 重新验证当前卡密
            if (currentKey && isAuthenticated) {
                console.log('🔄 重新验证卡密以同步新API密钥...');
                this.revalidateCurrentKey();
            }
        },

        // 重新验证当前卡密
        async revalidateCurrentKey() {
            if (!currentKey) return;

            try {
                const response = await this.makeRequest(this.endpoints.verify, {
                    key: currentKey,
                    action: 'revalidate'
                });

                if (response.success) {
                    console.log('✅ 卡密重新验证成功');
                } else {
                    console.warn('⚠️ 卡密重新验证失败:', response.message);
                }
            } catch (e) {
                console.error('❌ 卡密重新验证异常:', e);
            }
        },

        // 获取当前API URL
        getApiUrl(endpoint) {
            return `${this.current}${this.endpoints[endpoint]}`;
        },

        // 获取API密钥
        getApiKey() {
            return this.api_key;
        },

        // 切换服务器
        switchServer() {
            const servers = [this.servers.primary, this.servers.backup, this.servers.secure];
            const currentIndex = servers.indexOf(this.current);
            const nextIndex = (currentIndex + 1) % servers.length;
            this.current = servers[nextIndex];
            console.log('🔄 切换到服务器:', this.current);
            return this.current;
        },

        // 创建API请求
        async makeRequest(endpoint, data = {}) {
            const url = this.getApiUrl(endpoint);
            const apiKey = this.getApiKey();

            return new Promise((resolve, reject) => {
                const requestData = {
                    ...data,
                    timestamp: Date.now()
                };

                // 添加API密钥认证
                const headers = {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
                    'Accept': 'application/json'
                };

                // 如果有API密钥，添加到请求头
                if (apiKey) {
                    headers['X-API-Key'] = apiKey;
                    headers['X-API-Version'] = this.version;
                }

                GM_xmlhttpRequest({
                    method: 'POST',
                    url: url,
                    headers: headers,
                    data: Object.keys(requestData).map(key =>
                        `${encodeURIComponent(key)}=${encodeURIComponent(requestData[key])}`
                    ).join('&'),
                    timeout: 15000,
                    onload: (response) => {
                        if (response.status === 200) {
                            try {
                                const data = JSON.parse(response.responseText);
                                resolve(data);
                            } catch (e) {
                                reject(new Error('响应解析失败'));
                            }
                        } else {
                            reject(new Error(`HTTP ${response.status}`));
                        }
                    },
                    onerror: (error) => {
                        reject(new Error('网络请求失败'));
                    },
                    ontimeout: () => {
                        reject(new Error('请求超时'));
                    }
                });
            });
        },

        // 启动配置监控
        startConfigMonitoring() {
            if (configUpdateMonitor) {
                clearInterval(configUpdateMonitor);
            }

            console.log('🔄 启动配置监控服务...');

            configUpdateMonitor = setInterval(async () => {
                try {
                    await this.updateConfig();
                    console.log('🔄 配置监控检查完成');
                } catch (e) {
                    console.warn('⚠️ 配置监控检查失败:', e);
                }
            }, this.update_interval);
        },

        // 停止配置监控
        stopConfigMonitoring() {
            if (configUpdateMonitor) {
                console.log('🛑 停止配置监控');
                clearInterval(configUpdateMonitor);
                configUpdateMonitor = null;
            }
        },

        // 记录当前配置
        logCurrentConfig() {
            console.log('📋 当前API配置:');
            console.log('  • 主服务器:', this.servers.primary);
            console.log('  • 备用服务器:', this.servers.backup);
            console.log('  • 安全服务器:', this.servers.secure);
            console.log('  • 当前服务器:', this.current);
            console.log('  • API密钥:', this.api_key ? this.api_key.substring(0, 8) + '...' : '未设置');
            console.log('  • 版本:', this.version);
            console.log('  • 最后更新:', new Date(this.last_update).toLocaleString());
        }
    };

    // 【新增】API密钥同步管理器
    const ApiKeyManager = {
        // 检查API密钥是否需要更新
        async checkForUpdates() {
            if (!currentKey || !isAuthenticated) return false;

            try {
                console.log('🔍 检查API密钥更新...');

                const response = await API_CONFIG.makeRequest('key_info', {
                    key: currentKey,
                    current_api_key: API_CONFIG.getApiKey(),
                    action: 'check_updates'
                });

                if (response.success) {
                    // 检查API密钥是否有更新
                    if (response.api_key_updated) {
                        console.log('🔑 检测到API密钥更新');
                        API_CONFIG.api_key = response.api_key;
                        API_CONFIG.saveToStorage();
                        return true;
                    }

                    // 检查脚本是否有更新
                    if (response.script_updated && response.script) {
                        console.log('📝 检测到脚本更新');
                        this.updateScript(response.script);
                    }

                    // 更新API配置
                    if (response.api_config) {
                        API_CONFIG.applyConfig(response.api_config);
                        API_CONFIG.saveToStorage();
                    }
                }

                return false;
            } catch (e) {
                console.error('❌ API密钥更新检查失败:', e);
                return false;
            }
        },

        // 更新脚本
        updateScript(newScript) {
            console.log('🔄 更新脚本代码...');

            try {
                // 备份当前脚本状态
                const currentState = {
                    isAuthenticated,
                    currentKey,
                    isScriptLoaded
                };

                // 执行新脚本
                eval(newScript);

                // 恢复状态
                isAuthenticated = currentState.isAuthenticated;
                currentKey = currentState.currentKey;
                isScriptLoaded = true;

                console.log('✅ 脚本更新成功');

                // 显示更新通知
                this.showUpdateNotification('脚本已更新到最新版本');

            } catch (e) {
                console.error('❌ 脚本更新失败:', e);
                this.showUpdateNotification('脚本更新失败: ' + e.message, 'error');
            }
        },

        // 显示更新通知
        showUpdateNotification(message, type = 'success') {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? 'linear-gradient(135deg, #28a745, #20c997)' : 'linear-gradient(135deg, #dc3545, #c82333)'};
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                box-shadow: 0 5px 15px rgba(0,0,0,0.3);
                z-index: 100000;
                font-family: sans-serif;
                font-size: 14px;
                font-weight: bold;
                max-width: 300px;
                animation: slideInRight 0.3s ease-out;
            `;

            notification.innerHTML = `
                <div style="display: flex; align-items: center;">
                    <span style="margin-right: 10px;">${type === 'success' ? '✅' : '❌'}</span>
                    <span>${message}</span>
                </div>
            `;

            // 添加动画样式
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideInRight {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }
                @keyframes slideOutRight {
                    from {
                        transform: translateX(0);
                        opacity: 1;
                    }
                    to {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);

            document.body.appendChild(notification);

            // 3秒后自动消失
            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease-in';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                    if (style.parentNode) {
                        style.parentNode.removeChild(style);
                    }
                }, 300);
            }, 3000);
        },

        // 启动API密钥监控
        startMonitoring() {
            if (apiKeyUpdateMonitor) {
                clearInterval(apiKeyUpdateMonitor);
            }

            console.log('🔄 启动API密钥监控服务...');

            apiKeyUpdateMonitor = setInterval(async () => {
                try {
                    const updated = await this.checkForUpdates();
                    if (updated) {
                        console.log('🔑 API密钥已同步更新');
                    }
                } catch (e) {
                    console.warn('⚠️ API密钥监控检查失败:', e);
                }
            }, API_CONFIG.update_interval);
        },

        // 停止API密钥监控
        stopMonitoring() {
            if (apiKeyUpdateMonitor) {
                console.log('🛑 停止API密钥监控');
                clearInterval(apiKeyUpdateMonitor);
                apiKeyUpdateMonitor = null;
            }
        }
    };

    // 【修复】全局API保存，确保动态脚本可以访问
    window._tampermonkeyAPIs = {
        GM_openInTab: GM_openInTab,
        GM_setValue: GM_setValue,
        GM_getValue: GM_getValue,
        GM_xmlhttpRequest: GM_xmlhttpRequest,
        GM_addStyle: GM_addStyle,
        windowOpen: window.open
    };

    // 【修复】增强的权限传递函数
    function setupAPIAccess() {
        console.log('🔧 设置API访问权限...');

        // 方法1: 通过unsafeWindow传递
        if (typeof unsafeWindow !== 'undefined') {
            unsafeWindow.GM_openInTab = GM_openInTab;
            unsafeWindow.GM_setValue = GM_setValue;
            unsafeWindow.GM_getValue = GM_getValue;
            unsafeWindow.GM_xmlhttpRequest = GM_xmlhttpRequest;
            unsafeWindow.GM_addStyle = GM_addStyle;
            console.log('✅ unsafeWindow API传递完成');
        }

        // 方法2: 直接在window对象上设置
        window.GM_openInTab = GM_openInTab;
        window.GM_setValue = GM_setValue;
        window.GM_getValue = GM_getValue;
        window.GM_xmlhttpRequest = GM_xmlhttpRequest;
        window.GM_addStyle = GM_addStyle;
        console.log('✅ window API设置完成');

        // 方法3: 确保window.open可用
        if (typeof window.open === 'undefined' || !window.open) {
            window.open = function(url, name, specs) {
                console.log('🔗 使用GM_openInTab打开链接:', url);
                if (typeof GM_openInTab !== 'undefined') {
                    return GM_openInTab(url, { active: true });
                }
                console.error('❌ GM_openInTab不可用');
                return null;
            };
            console.log('✅ window.open备用实现已设置');
        }

        // 方法4: 创建全局辅助函数
        window.openNewTab = function(url) {
            console.log('🚀 尝试打开新标签页:', url);

            // 优先使用GM_openInTab
            if (typeof GM_openInTab !== 'undefined') {
                console.log('使用GM_openInTab');
                return GM_openInTab(url, { active: true });
            }

            // 备用使用window.open
            if (typeof window.open !== 'undefined') {
                console.log('使用window.open');
                return window.open(url, '_blank');
            }

            // 最后备用：通过location.href跳转
            console.log('使用location.href跳转');
            window.location.href = url;
            return null;
        };

        console.log('🎯 API访问权限设置完成');
    }

    // 【修复】API可用性测试函数
    function testAPIAvailability() {
        console.log('🧪 测试API可用性...');

        const results = {
            GM_openInTab: typeof GM_openInTab !== 'undefined',
            window_open: typeof window.open !== 'undefined',
            unsafeWindow: typeof unsafeWindow !== 'undefined',
            openNewTab: typeof window.openNewTab !== 'undefined'
        };

        console.log('API可用性测试结果:', results);
        return results;
    }

    // 【修改】卡密状态检查函数 - 使用API配置系统
    async function checkKeyStatus(key, silent = false) {
        if (!key || !isAuthenticated) {
            return;
        }

        if (!silent) {
            console.log('🔍 检查卡密状态:', key.substring(0, 8) + '...');
        }

        try {
            const response = await API_CONFIG.makeRequest('verify', {
                key: key,
                check_status: 1
            });

            if (!silent) {
                console.log('📨 卡密状态检查响应:', response);
            }

            handleKeyStatusResponse(response);
        } catch (error) {
            if (!silent) {
                console.error('❌ 卡密状态检查失败:', error);

                // 尝试切换服务器重试
                API_CONFIG.switchServer();
                console.log('🔄 切换服务器后重试卡密状态检查...');

                try {
                    const retryResponse = await API_CONFIG.makeRequest('verify', {
                        key: key,
                        check_status: 1
                    });
                    handleKeyStatusResponse(retryResponse);
                } catch (retryError) {
                    console.error('❌ 重试后仍然失败:', retryError);
                }
            }
        }
    }

    // 【新增】获取设备信息的辅助函数
    function getDeviceId() {
        let deviceId = localStorage.getItem('xmh_device_id');
        if (!deviceId) {
            deviceId = 'web_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
            localStorage.setItem('xmh_device_id', deviceId);
        }
        return deviceId;
    }

    function getDeviceName() {
        return navigator.userAgent.includes('Windows') ? 'Windows设备' :
               navigator.userAgent.includes('Mac') ? 'Mac设备' :
               navigator.userAgent.includes('Linux') ? 'Linux设备' : '未知设备';
    }

    function getPlatform() {
        return navigator.platform || 'unknown';
    }

    // 【新增】处理卡密状态响应
    function handleKeyStatusResponse(data) {
        console.log('📋 卡密状态数据:', data);

        if (!data.success) {
            // 卡密状态异常，需要处理
            let message = '';
            let title = '⚠️ 卡密状态异常';

            if (data.error_code) {
                switch (data.error_code) {
                    case 'CONCURRENT_LOGIN_LIMIT':
                        message = '老板您好！\n\n该卡密已经在其他电脑登录，不能同时登录';
                        title = '🚫 同时登录限制';
                        break;
                    case 'MULTI_STORE_LOGIN_LIMIT':
                        const maxDevices = data.max_devices || '未知';
                        message = `老板您好！\n\n该多店卡密已达到最大同时登录数量限制（${maxDevices}台设备），请先退出其他设备后再登录`;
                        title = '🚫 多店登录限制';
                        break;
                    case 'KEY_DISABLED':
                        message = '您的卡密已经被禁用，如有疑问请联系代理商';
                        title = '🚫 卡密已禁用';
                        break;
                    case 'KEY_DELETED':
                        message = '您的卡密不存在已被删除，如有疑问请联系代理商';
                        title = '❌ 卡密已删除';
                        break;
                    case 'KEY_EXPIRED':
                        message = '您的卡密已经过期，如需继续使用请联系代理商';
                        title = '⏰ 卡密已过期';
                        break;
                    default:
                        message = data.message || '卡密验证失败，请重新验证';
                        break;
                }
            } else {
                message = data.message || '卡密验证失败，请重新验证';
            }

            console.warn('⚠️ 卡密状态异常:', message);

            // 显示状态异常弹窗
            showKeyStatusAlert(title, message);

            // 清除本地数据并退出
            handleKeyStatusExit();
        } else {
            // 卡密状态正常，更新功能信息
            if (data.function_type) {
                GM_setValue('function_type', data.function_type);
                GM_setValue('has_customer_service', data.has_customer_service);
                GM_setValue('has_product_listing', data.has_product_listing);

                // 更新控制面板显示
                updateFunctionTypeDisplay();
            }
        }
    }

    // 【新增】显示卡密状态异常弹窗
    function showKeyStatusAlert(title, message) {
        // 移除可能存在的旧弹窗
        const existingAlert = document.getElementById('key-status-alert');
        if (existingAlert) {
            existingAlert.remove();
        }

        const alert = document.createElement('div');
        alert.id = 'key-status-alert';
        alert.innerHTML = `
            <div class="alert-overlay"></div>
            <div class="alert-content">
                <div class="alert-header">
                    ${title}
                </div>
                <div class="alert-body">
                    <div class="alert-message">
                        ${message}
                    </div>
                    <div class="alert-buttons">
                        <button id="alert-confirm-btn" class="alert-btn alert-btn-primary">确定</button>
                    </div>
                </div>
            </div>
        `;

        // 添加样式
        GM_addStyle(`
            #key-status-alert {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 999999;
                font-family: sans-serif;
            }

            #key-status-alert .alert-overlay {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.6);
                backdrop-filter: blur(5px);
            }

            #key-status-alert .alert-content {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                border-radius: 15px;
                box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
                overflow: hidden;
                min-width: 400px;
                max-width: 90vw;
                animation: alertFadeIn 0.3s ease-out;
            }

            #key-status-alert .alert-header {
                background: linear-gradient(135deg, #dc3545, #c82333);
                color: white;
                padding: 20px;
                font-size: 18px;
                font-weight: bold;
                text-align: center;
            }

            #key-status-alert .alert-body {
                padding: 30px;
            }

            #key-status-alert .alert-message {
                font-size: 16px;
                line-height: 1.6;
                color: #333;
                text-align: center;
                margin-bottom: 25px;
            }

            #key-status-alert .alert-buttons {
                text-align: center;
            }

            #key-status-alert .alert-btn {
                padding: 12px 30px;
                border: none;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s ease;
            }

            #key-status-alert .alert-btn-primary {
                background: linear-gradient(135deg, #dc3545, #c82333);
                color: white;
            }

            #key-status-alert .alert-btn-primary:hover {
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
            }

            @keyframes alertFadeIn {
                from {
                    opacity: 0;
                    transform: translate(-50%, -50%) scale(0.9);
                }
                to {
                    opacity: 1;
                    transform: translate(-50%, -50%) scale(1);
                }
            }
        `);

        document.body.appendChild(alert);

        // 绑定确定按钮事件
        document.getElementById('alert-confirm-btn').addEventListener('click', () => {
            alert.remove();
        });

        // 点击遮罩层关闭
        alert.querySelector('.alert-overlay').addEventListener('click', () => {
            alert.remove();
        });
    }

    // 【新增】处理卡密状态异常退出
    function handleKeyStatusExit() {
        console.log('🚪 卡密状态异常，执行退出流程...');

        // 停止状态监控
        if (keyStatusMonitor) {
            clearInterval(keyStatusMonitor);
            keyStatusMonitor = null;
        }

        // 清除本地存储的数据
        GM_deleteValue('saved_license_key_xiaomeihua');
        GM_deleteValue('function_type');
        GM_deleteValue('has_customer_service');
        GM_deleteValue('has_product_listing');
        GM_deleteValue('shop_name');
        GM_deleteValue('panel_state_xiaomeihua');

        // 重置状态变量
        isAuthenticated = false;
        currentKey = null;
        isScriptLoaded = false;

        // 移除控制面板和浮动图标
        if (controlPanel) {
            controlPanel.remove();
            controlPanel = null;
        }
        if (floatingIcon) {
            floatingIcon.remove();
            floatingIcon = null;
        }

        // 延迟后重新加载页面或显示验证界面
        setTimeout(() => {
            location.reload();
        }, 3000);
    }

    // 【新增】启动卡密状态监控
    function startKeyStatusMonitor(key) {
        if (!key) return;

        console.log('🔄 启动卡密状态监控...');

        // 停止之前的监控
        if (keyStatusMonitor) {
            clearInterval(keyStatusMonitor);
        }

        // 每5分钟检查一次卡密状态
        keyStatusMonitor = setInterval(() => {
            checkKeyStatus(key, true); // 静默检查
        }, 5 * 60 * 1000); // 5分钟

        // 立即进行一次检查（非静默）
        setTimeout(() => {
            checkKeyStatus(key, false);
        }, 10000); // 10秒后进行首次检查
    }

    // 【新增】停止卡密状态监控
    function stopKeyStatusMonitor() {
        if (keyStatusMonitor) {
            console.log('🛑 停止卡密状态监控');
            clearInterval(keyStatusMonitor);
            keyStatusMonitor = null;
        }
    }

    // 【修复】添加缺失的showMessage函数
    function showMessage(element, message, type) {
        if (!element) {
            console.error('showMessage: 元素不存在');
            return;
        }

        // 清除之前的类名
        element.className = '';

        // 根据类型添加相应的样式类
        switch(type) {
            case 'success':
                element.className = 'success';
                break;
            case 'error':
                element.className = 'error';
                break;
            case 'info':
                element.className = 'info';
                break;
            default:
                element.className = '';
        }

        // 设置消息内容
        element.innerHTML = message;
        console.log(`showMessage [${type}]: ${message}`);
    }

    // 创建UI
    function createUI() {
        if (document.getElementById('auth-panel')) return;

        const panel = document.createElement('div');
        panel.id = 'auth-panel';
        panel.innerHTML = `
            <div class="auth-header">
                小梅花AI客服助手 v1.0.0 (调试版)
                <button id="close-panel-btn" style="float: right; background: none; border: none; color: white; font-size: 18px; cursor: pointer; padding: 0; width: 20px; height: 20px;">×</button>
            </div>
            <div class="auth-body">
                <p>请输入卡密以解锁完整功能:</p>
                <input type="text" id="license-key-input" placeholder="在此输入卡密" autocomplete="off">
                <button id="verify-key-btn">验证</button>
                <div id="auth-message"></div>
            </div>
        `;
        document.body.appendChild(panel);

        GM_addStyle(`
            #auth-panel { position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 99999;
                         background-image: url('https://img.zcool.cn/community/01e3d85e71636ca801216518168b81.jpg@1280w_1l_2o_100sh.jpg');
                         background-size: cover; background-position: center;
                         border: 1px solid #ddd; border-radius: 15px;
                         box-shadow: 0 10px 30px rgba(0,0,0,0.5); width: 380px; font-family: sans-serif; }
            #auth-panel::before { content: ""; position: absolute; top: 0; left: 0; right: 0; bottom: 0;
                                background-color: rgba(255,255,255,0.85); border-radius: 15px; z-index: -1; }
            #auth-panel .auth-header { background: linear-gradient(135deg, #667eea, #764ba2); color: white;
                                      padding: 20px; border-top-left-radius: 15px; border-top-right-radius: 15px;
                                      font-size: 18px; text-align: center; font-weight: bold; position: relative; }
            #auth-panel .auth-body { padding: 25px; position: relative; z-index: 1; }
            #auth-panel p { margin-bottom: 15px; color: #333; font-size: 14px; font-weight: bold; }
            #auth-panel input { width: 100%; padding: 12px; box-sizing: border-box; margin-bottom: 15px;
                               border-radius: 8px; border: 2px solid #e1e5e9; font-size: 14px; }
            #auth-panel input:focus { outline: none; border-color: #667eea; }
            #auth-panel button { width: 100%; padding: 12px; background: linear-gradient(135deg, #667eea, #764ba2);
                                color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 16px;
                                font-weight: bold; transition: all 0.3s ease; }
            #auth-panel button:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(102,126,234,0.4); }
            #auth-message { margin-top: 15px; text-align: center; font-weight: bold; padding: 8px; border-radius: 5px; }
            .success { color: #28a745; background-color: rgba(40, 167, 69, 0.1); }
            .error { color: #dc3545; background-color: rgba(220, 53, 69, 0.1); }
            .info { color: #007bff; background-color: rgba(0, 123, 255, 0.1); }

            /* 成功提示中的特殊样式 */
            .success span[style*="color: #ff0000"] {
                color: #ff0000 !important;
                font-weight: bold !important;
            }

            /* 控制面板样式 */
            #control-panel { position: fixed; top: 20px; left: 20px; z-index: 99998; background: rgba(255,255,255,0.95);
                           border: 1px solid #ddd; border-radius: 10px; box-shadow: 0 5px 20px rgba(0,0,0,0.2);
                           width: 320px; font-family: sans-serif; backdrop-filter: blur(10px);
                           transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1); }
            #control-panel .panel-header { background: linear-gradient(135deg, #28a745, #20c997); color: white;
                                         padding: 15px; border-top-left-radius: 10px; border-top-right-radius: 10px;
                                         font-size: 16px; font-weight: bold; position: relative; }
            #control-panel .panel-body { padding: 20px; }
            #control-panel .status-item { margin-bottom: 10px; padding: 8px; background: #f8f9fa; border-radius: 5px; }
            #control-panel .status-label { font-weight: bold; color: #495057; margin-bottom: 4px; }
            #control-panel .status-value { color: #28a745; word-wrap: break-word; line-height: 1.4; }

            /* 浮动图标样式 */
            #floating-icon {
                position: fixed;
                top: 20px;
                left: 20px;
                width: 60px;
                height: 60px;
                border-radius: 50%;
                cursor: pointer;
                z-index: 99999;
                display: none;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                background: linear-gradient(135deg, #ff6b9d, #c44569, #f8b500, #e55039, #3c6382);
                background-size: 300% 300%;
                animation: gradientShift 3s ease infinite, float 2s ease-in-out infinite;
                box-shadow: 0 4px 15px rgba(255, 107, 157, 0.4);
            }

            #floating-icon:hover {
                transform: scale(1.1);
                box-shadow: 0 8px 25px rgba(255, 107, 157, 0.6);
            }

            #floating-icon::before {
                content: '';
                position: absolute;
                top: -10px;
                left: -10px;
                right: -10px;
                bottom: -10px;
                border-radius: 50%;
                background: radial-gradient(circle, rgba(255, 107, 157, 0.3) 0%, transparent 70%);
                animation: pulse 2s ease-in-out infinite;
                z-index: -1;
            }

            #floating-icon::after {
                content: '';
                position: absolute;
                top: -20px;
                left: -20px;
                right: -20px;
                bottom: -20px;
                border-radius: 50%;
                background: radial-gradient(circle, rgba(255, 107, 157, 0.1) 0%, transparent 70%);
                animation: pulse 2s ease-in-out infinite 0.5s;
                z-index: -2;
            }

            /* 梅花图标 */
            .plum-flower {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 30px;
                height: 30px;
                color: white;
                font-size: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                animation: rotate 4s linear infinite;
            }

            /* 动画定义 */
            @keyframes gradientShift {
                0% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
                100% { background-position: 0% 50%; }
            }

            @keyframes float {
                0%, 100% { transform: translateY(0px); }
                50% { transform: translateY(-5px); }
            }

            @keyframes pulse {
                0%, 100% {
                    opacity: 0.8;
                    transform: scale(1);
                }
                50% {
                    opacity: 0.4;
                    transform: scale(1.2);
                }
            }

            @keyframes rotate {
                0% { transform: translate(-50%, -50%) rotate(0deg); }
                100% { transform: translate(-50%, -50%) rotate(360deg); }
            }

            /* 面板隐藏时的样式 */
            #control-panel.minimized {
                opacity: 0;
                transform: scale(0.8) translateX(100px);
                pointer-events: none;
            }

            /* 面板显示动画 */
            #control-panel.show {
                opacity: 1;
                transform: scale(1) translateX(0);
                pointer-events: all;
            }

            /* 成功弹窗样式 */
            #success-popup {
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                z-index: 100000;
                background: rgba(255, 255, 255, 0.98);
                border: 2px solid #28a745;
                border-radius: 20px;
                box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
                width: 550px;
                max-width: 90vw;
                font-family: sans-serif;
                backdrop-filter: blur(15px);
                animation: popupFadeIn 0.5s ease-out;
            }

            #success-popup .popup-header {
                background: linear-gradient(135deg, #28a745, #20c997);
                color: white;
                padding: 20px;
                border-top-left-radius: 18px;
                border-top-right-radius: 18px;
                text-align: center;
                font-size: 20px;
                font-weight: bold;
            }

            #success-popup .popup-body {
                padding: 30px;
                text-align: center;
            }

            #success-popup .function-info {
                font-size: 24px;
                font-weight: bold;
                margin: 20px 0;
                padding: 15px;
                border-radius: 12px;
                background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
                border: 2px solid #e1bee7;
            }

            #success-popup .success-text {
                font-size: 16px;
                color: #155724;
                line-height: 1.6;
                margin: 15px 0;
            }

            #success-popup .countdown {
                font-size: 14px;
                color: #6c757d;
                margin-top: 20px;
            }

            @keyframes popupFadeIn {
                from {
                    opacity: 0;
                    transform: translate(-50%, -50%) scale(0.8);
                }
                to {
                    opacity: 1;
                    transform: translate(-50%, -50%) scale(1);
                }
            }

            @keyframes popupFadeOut {
                from {
                    opacity: 1;
                    transform: translate(-50%, -50%) scale(1);
                }
                to {
                    opacity: 0;
                    transform: translate(-50%, -50%) scale(0.8);
                }
            }
        `);

        document.getElementById('verify-key-btn').addEventListener('click', verifyKey);
        document.getElementById('license-key-input').addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                verifyKey();
            }
        });
        document.getElementById('close-panel-btn').addEventListener('click', function() {
            document.getElementById('auth-panel').remove();
        });
        
        // 自动聚焦到输入框
        setTimeout(() => {
            const inputElement = document.getElementById('license-key-input');
            if (inputElement) {
                inputElement.focus();
            }
        }, 300);
    }

    // 创建浮动图标
    function createFloatingIcon() {
        // 如果已存在，先移除
        if (floatingIcon) {
            if (floatingIcon.parentNode) {
                floatingIcon.parentNode.removeChild(floatingIcon);
            }
        }

        // 检查是否已有同ID元素
        const existingIcon = document.getElementById('floating-icon');
        if (existingIcon) {
            existingIcon.remove();
        }

        floatingIcon = document.createElement('div');
        floatingIcon.id = 'floating-icon';
        floatingIcon.innerHTML = `
            <div class="plum-flower">🌸</div>
        `;

        floatingIcon.addEventListener('click', function() {
            toggleControlPanel();
        });

        document.body.appendChild(floatingIcon);
        console.log('浮动图标已创建并添加到页面');
    }

    // 创建控制面板
    function createControlPanel() {
        if (document.getElementById('control-panel')) return;

        controlPanel = document.createElement('div');
        controlPanel.id = 'control-panel';
        controlPanel.innerHTML = `
            <div class="panel-header">
                🌸 脚本控制面板
                <button id="minimize-panel-btn" style="float: right; background: none; border: none; color: white; font-size: 16px; cursor: pointer; padding: 0; width: 18px; height: 18px;">−</button>
            </div>
            <div class="panel-body">
                <div class="status-item">
                    <div class="status-label">🟢 状态:</div>
                    <div class="status-value">已激活</div>
                </div>
                <div class="status-item">
                    <div class="status-label">🏪 店铺:</div>
                    <div class="status-value" id="shop-name-value" style="color: #e74c3c; font-weight: bold;">${getShopInfo()}</div>
                </div>
                <div class="status-item">
                    <div class="status-label">🔑 卡密:</div>
                    <div class="status-value" style="font-family: monospace; font-size: 11px; word-break: break-all;">${currentKey || '未知'}</div>
                </div>
                <div class="status-item">
                    <div class="status-label">⭐ 功能类型:</div>
                    <div class="status-value" id="function-type-value">检测中...</div>
                </div>
                <div class="status-item">
                    <div class="status-label">📦 版本:</div>
                    <div class="status-value">v1.0.0</div>
                </div>
                <div class="status-item">
                    <div class="status-label">⏰ 运行时间:</div>
                    <div class="status-value" id="runtime-counter">0秒</div>
                </div>
                <button id="logout-btn" style="width: 100%; padding: 10px; background: linear-gradient(135deg, #dc3545, #c82333); color: white; border: none; border-radius: 5px; cursor: pointer; margin-top: 15px; font-weight: bold; transition: all 0.3s ease;">🚪 退出登录</button>
            </div>
        `;
        document.body.appendChild(controlPanel);

        // 启动运行时间计时器
        startRuntimeCounter();

        document.getElementById('minimize-panel-btn').addEventListener('click', function() {
            minimizeControlPanel();
        });

        document.getElementById('logout-btn').addEventListener('click', function() {
            if (confirm('🤔 确定要退出登录吗？\n\n退出后需要重新输入卡密才能使用功能。')) {
                // 【新增】停止卡密状态监控
                stopKeyStatusMonitor();

                GM_deleteValue('saved_license_key_xiaomeihua');
                GM_deleteValue('panel_state_xiaomeihua');
                GM_deleteValue('function_type');
                GM_deleteValue('has_customer_service');
                GM_deleteValue('has_product_listing');
                GM_deleteValue('shop_name');

                if (controlPanel) controlPanel.remove();
                if (floatingIcon) floatingIcon.remove();
                location.reload();
            }
        });

        // 添加悬停效果
        const logoutBtn = document.getElementById('logout-btn');
        logoutBtn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 5px 15px rgba(220, 53, 69, 0.4)';
        });
        logoutBtn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = 'none';
        });

        // 更新功能类型显示
        updateFunctionTypeDisplay();
    }

    // 最小化控制面板
    function minimizeControlPanel() {
        console.log('最小化控制面板...');

        if (controlPanel) {
            controlPanel.classList.add('minimized');
            setTimeout(() => {
                controlPanel.style.display = 'none';
            }, 300);
        }

        // 确保浮动图标存在并显示
        if (!floatingIcon) {
            console.log('浮动图标不存在，重新创建...');
            createFloatingIcon();
        }

        if (floatingIcon) {
            floatingIcon.style.display = 'block';
            floatingIcon.style.opacity = '1';
            floatingIcon.style.transform = 'scale(1)';
            console.log('浮动图标已显示');
        }

        // 保存面板状态
        GM_setValue('panel_state_xiaomeihua', 'minimized');
    }

    // 显示控制面板
    function showControlPanel() {
        if (controlPanel) {
            controlPanel.style.display = 'block';
            controlPanel.classList.remove('minimized');
            controlPanel.classList.add('show');
        }

        // 隐藏浮动图标
        if (floatingIcon) {
            floatingIcon.style.opacity = '0';
            floatingIcon.style.transform = 'scale(0.8)';
            setTimeout(() => {
                floatingIcon.style.display = 'none';
            }, 300);
        }

        // 保存面板状态
        GM_setValue('panel_state_xiaomeihua', 'expanded');
    }

    // 切换控制面板显示/隐藏
    function toggleControlPanel() {
        if (controlPanel.style.display === 'none' || controlPanel.classList.contains('minimized')) {
            showControlPanel();
        } else {
            minimizeControlPanel();
        }
    }

    // =================================================================================
    // 【优化】恢复面板状态 - 强制显示缩略图
    // 根据用户要求，在页面刷新后，无论之前的状态如何，都默认显示缩略图图标。
    // 这解决了"刷新页面后图标消失"的问题。
    function restorePanelState() {
        console.log('🔄 初始化UI状态：强制显示缩略图...');

        // 1. 确保核心UI元素已创建
        // 如果在自动验证流程中尚未创建，则在这里创建它们。
        if (!controlPanel) {
            console.log('面板不存在，创建控制面板...');
            createControlPanel();
        }
        if (!floatingIcon) {
            console.log('图标不存在，创建浮动图标...');
            createFloatingIcon();
        }

        // 2. 延迟执行以确保页面渲染稳定
        // 使用一个短延迟来避免与其他页面加载脚本的冲突。
        setTimeout(() => {
            console.log('执行UI状态设置 -> 最小化面板');
            // 3. 调用 minimizeControlPanel 函数
            // 这个函数会隐藏主面板并【显示】浮动图标，同时保存状态。
            minimizeControlPanel();
            console.log('✅ UI状态已成功设置为缩略图模式。');
        }, 200); // 使用较短延迟，响应更快
    }
    // =================================================================================

    // 启动运行时间计时器
    function startRuntimeCounter() {
        const startTime = Date.now();

        setInterval(() => {
            const runtimeElement = document.getElementById('runtime-counter');
            if (runtimeElement) {
                const elapsed = Math.floor((Date.now() - startTime) / 1000);
                const minutes = Math.floor(elapsed / 60);
                const seconds = elapsed % 60;

                if (minutes > 0) {
                    runtimeElement.textContent = `${minutes}分${seconds}秒`;
                } else {
                    runtimeElement.textContent = `${seconds}秒`;
                }
            }
        }, 1000);
    }

    // 更新功能类型显示
    function updateFunctionTypeDisplay() {
        const functionTypeElem = document.getElementById('function-type-value');
        if (!functionTypeElem) return;

        const functionType = GM_getValue('function_type', '');
        const hasCustomerService = GM_getValue('has_customer_service', true);
        const hasProductListing = GM_getValue('has_product_listing', false);

        let displayText = '';
        let displayColor = '';

        if (hasCustomerService && hasProductListing) {
            displayText = '小梅花AI全功能版本';
            displayColor = '#ff6b9d';
        } else if (hasProductListing && !hasCustomerService) {
            displayText = '小梅花AI上架产品功能版本';
            displayColor = '#4ecdc4';
        } else if (hasCustomerService && !hasProductListing) {
            displayText = '小梅花AI客服功能版本';
            displayColor = '#667eea';
        } else {
            displayText = '❌ 无功能';
            displayColor = '#666';
        }

        functionTypeElem.textContent = displayText;
        functionTypeElem.style.color = displayColor;
        functionTypeElem.style.fontWeight = 'bold';
    }

    // 【优化】获取店铺信息 - 精准识别店铺名称
    function getShopInfo() {
        try {
            console.log('🏪 开始获取店铺信息...');

            // 方法1: 从页面标题获取店铺名称（最准确）
            const title = document.title;
            console.log('页面标题:', title);

            // 匹配各种标题格式
            const titlePatterns = [
                /^(.+?)\s*[-|·|—|丨]\s*微信小店/,           // "小梅花旗舰店 - 微信小店"
                /^(.+?)\s*[-|·|—|丨]\s*客服/,               // "小梅花旗舰店 - 客服"
                /^(.+?)\s*[-|·|—|丨]\s*小店/,               // "小梅花旗舰店 - 小店"
                /^(.+?)\s*微信小店/,                       // "小梅花旗舰店微信小店"
                /^(.+?)\s*客服$/,                          // "小梅花旗舰店客服"
                /^(.+?)\s*小店$/                           // "小梅花旗舰店小店"
            ];

            for (const pattern of titlePatterns) {
                const match = title.match(pattern);
                if (match && match[1]) {
                    const shopName = match[1].trim();
                    if (shopName && shopName.length > 0 && shopName.length < 50) {
                        console.log('✅ 从标题获取店铺名称:', shopName);
                        return shopName;
                    }
                }
            }

            // 方法2: 从页面元素获取店铺名称
            console.log('📋 尝试从页面元素获取店铺名称...');
            const shopNameSelectors = [
                // 微信小店常见的店铺名称选择器
                '.shop-info .shop-name',
                '.store-header .store-name',
                '.shop-title',
                '.store-title',
                '[data-testid="shop-name"]',
                '[class*="shop-name"]',
                '[class*="store-name"]',
                '.header-title',
                '.page-title',
                // 通用选择器
                'h1', 'h2', '.title'
            ];

            for (const selector of shopNameSelectors) {
                const elements = document.querySelectorAll(selector);
                for (const element of elements) {
                    if (element && element.textContent) {
                        const text = element.textContent.trim();
                        console.log(`检查元素 ${selector}:`, text);

                        // 过滤条件更精确
                        if (text &&
                            text.length > 1 &&
                            text.length < 50 &&
                            !text.includes('客服') &&
                            !text.includes('微信') &&
                            !text.includes('设置') &&
                            !text.includes('管理') &&
                            !text.includes('页面') &&
                            !text.match(/^[0-9]+$/) && // 不是纯数字
                            !text.includes('kf') &&
                            !text.includes('shop')) {
                            console.log('✅ 从页面元素获取店铺名称:', text);
                            return text;
                        }
                    }
                }
            }

            // 方法3: 从URL路径获取店铺信息
            console.log('🔗 尝试从URL获取店铺信息...');
            const url = window.location.href;
            console.log('当前URL:', url);

            // 匹配URL中的店铺ID或名称
            const urlPatterns = [
                /\/shop\/([^\/\?]+)/,                      // /shop/shopname
                /shopId=([^&]+)/,                          // ?shopId=xxx
                /shop_id=([^&]+)/,                         // ?shop_id=xxx
                /store\/([^\/\?]+)/,                       // /store/storename
                /storeId=([^&]+)/                          // ?storeId=xxx
            ];

            for (const pattern of urlPatterns) {
                const match = url.match(pattern);
                if (match && match[1]) {
                    const identifier = decodeURIComponent(match[1]);
                    console.log('从URL获取标识符:', identifier);

                    // 如果是有意义的名称而不是ID
                    if (identifier &&
                        !identifier.match(/^[0-9a-f]+$/i) && // 不是纯16进制ID
                        !identifier.match(/^[0-9]+$/) &&     // 不是纯数字ID
                        identifier.length > 2) {
                        console.log('✅ 从URL获取店铺名称:', identifier);
                        return identifier;
                    } else {
                        console.log('✅ 从URL获取店铺ID:', identifier);
                        return `店铺${identifier}`;
                    }
                }
            }

            // 方法4: 从localStorage或sessionStorage获取
            console.log('💾 尝试从本地存储获取店铺信息...');
            const storageKeys = ['shopName', 'storeName', 'shop_name', 'store_name'];
            for (const key of storageKeys) {
                const stored = localStorage.getItem(key) || sessionStorage.getItem(key);
                if (stored && stored.trim()) {
                    console.log(`✅ 从本地存储 ${key} 获取店铺名称:`, stored);
                    return stored.trim();
                }
            }

            // 方法5: 检查页面中的JSON数据
            console.log('📄 尝试从页面JSON数据获取店铺信息...');
            const scripts = document.querySelectorAll('script');
            for (const script of scripts) {
                if (script.textContent) {
                    try {
                        // 查找包含店铺信息的JSON
                        const jsonMatches = script.textContent.match(/"shop_?name"\s*:\s*"([^"]+)"/i) ||
                                          script.textContent.match(/"store_?name"\s*:\s*"([^"]+)"/i);
                        if (jsonMatches && jsonMatches[1]) {
                            console.log('✅ 从JSON数据获取店铺名称:', jsonMatches[1]);
                            return jsonMatches[1];
                        }
                    } catch (e) {
                        // 忽略JSON解析错误
                    }
                }
            }

            console.log('⚠️ 无法获取具体店铺名称，使用默认称呼');
            return '尊贵的商家';

        } catch (error) {
            console.error('❌ 获取店铺信息时发生错误:', error);
            return '尊贵的商家';
        }
    }

    // 显示成功弹窗
    function showSuccessPopup(functionType, hasCustomerService, hasProductListing) {
        // 移除可能存在的旧弹窗
        const existingPopup = document.getElementById('success-popup');
        if (existingPopup) {
            existingPopup.remove();
        }

        // 获取店铺信息
        const shopName = getShopInfo();

        let functionName = '';
        let functionColor = '';
        let icon = '';

        if (hasCustomerService && hasProductListing) {
            functionName = '小梅花AI全功能版本';
            functionColor = '#ff6b9d';
            icon = '🌟';
        } else if (hasProductListing && !hasCustomerService) {
            functionName = '小梅花AI上架产品功能版本';
            functionColor = '#4ecdc4';
            icon = '📦';
        } else if (hasCustomerService && !hasProductListing) {
            functionName = '小梅花AI客服功能版本';
            functionColor = '#667eea';
            icon = '🤖';
        } else {
            functionName = '无功能版本';
            functionColor = '#666';
            icon = '❌';
        }

        const popup = document.createElement('div');
        popup.id = 'success-popup';
        popup.innerHTML = `
            <div class="popup-header">
                ${icon} 卡密验证成功
            </div>
            <div class="popup-body">
                <div class="welcome-message" style="
                    font-size: 18px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin: 20px 0;
                    padding: 15px;
                    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
                    border-radius: 12px;
                    border-left: 4px solid #28a745;
                    text-align: center;
                    line-height: 1.6;
                ">
                    🎉 欢迎 "<span style="color: #e74c3c; font-weight: bold;">${shopName}</span>"<br>
                    <span style="font-size: 16px; color: #6c757d;">感谢您使用小梅花智能AI客服系统</span>
                </div>
                <div class="function-info" style="color: ${functionColor};">
                    ${functionName}
                </div>
                <div class="success-text">
                    恭喜您！卡密验证通过，已成功激活对应功能。
                    <br>系统将为您的店铺提供智能化客服服务！
                </div>
                <div class="countdown" id="popup-countdown">
                    此弹窗将在 <span id="countdown-timer">5</span> 秒后自动关闭
                </div>
            </div>
        `;

        document.body.appendChild(popup);

        // 倒计时
        let countdown = 5;
        const timer = setInterval(() => {
            countdown--;
            const timerElem = document.getElementById('countdown-timer');
            if (timerElem) {
                timerElem.textContent = countdown;
            }

            if (countdown <= 0) {
                clearInterval(timer);
                // 添加淡出动画
                popup.style.animation = 'popupFadeOut 0.5s ease-in';
                setTimeout(() => {
                    if (popup.parentNode) {
                        popup.parentNode.removeChild(popup);
                    }
                }, 500);
            }
        }, 1000);

        // 点击弹窗关闭
        popup.addEventListener('click', () => {
            clearInterval(timer);
            popup.style.animation = 'popupFadeOut 0.5s ease-in';
            setTimeout(() => {
                if (popup.parentNode) {
                    popup.parentNode.removeChild(popup);
                }
            }, 500);
        });
    }

    // 验证逻辑
    function verifyKey() {
        const keyInput = document.getElementById('license-key-input');
        const messageDiv = document.getElementById('auth-message');
        const verifyBtn = document.getElementById('verify-key-btn');

        if (!keyInput || !messageDiv || !verifyBtn) {
            console.error('验证界面元素未找到:', {
                keyInput: !!keyInput,
                messageDiv: !!messageDiv,
                verifyBtn: !!verifyBtn
            });
            alert('❌ 验证界面初始化失败，请刷新页面重试');
            return;
        }

        // 修复：先获取值再进行trim，避免null或undefined报错
        const key = keyInput.value ? keyInput.value.trim() : '';
        
        // 修复：增加更详细的卡密格式验证
        if (!key) {
            showMessage(messageDiv, '请输入卡密后再验证', 'error');
            keyInput.focus(); // 自动聚焦到输入框
            return;
        }

        // 静默处理空格：自动移除空格字符
        key = key.replace(/\s/g, '');

        // 增加基本的卡密格式验证
        if (key.length < 6) {
            showMessage(messageDiv, '卡密格式不正确，请检查后重试', 'error');
            return;
        }

        console.log('🔑 开始验证卡密:', key.substring(0, 8) + '...');

        verifyBtn.disabled = true;
        verifyBtn.textContent = '验证中...';
        showMessage(messageDiv, '正在验证卡密...', 'info');

        // 【修改】使用API配置系统发送验证请求
        console.log('📡 发送验证请求到:', API_CONFIG.getApiUrl('verify'));

        // 【优化】使用新的API配置系统进行验证，添加设备信息用于同时登录检测
        API_CONFIG.makeRequest('verify', {
            key: key,
            device_id: getDeviceId(),
            device_name: getDeviceName(),
            platform: getPlatform(),
            check_concurrent_login: 1 // 启用同时登录检测
        })
            .then(function(data) {
                console.log('📨 验证API响应:', data);

                // 【修复】确保按钮状态恢复
                verifyBtn.disabled = false;
                verifyBtn.textContent = '验证';

                console.log('📋 解析后的响应数据:', data);

                if (data.success) {
                    console.log('验证成功');
                    isAuthenticated = true;
                    currentKey = key;
                    GM_setValue('saved_license_key_xiaomeihua', key);

                    // 保存功能类型信息和店铺信息
                    if (data.function_type) {
                        GM_setValue('function_type', data.function_type);
                        GM_setValue('has_customer_service', data.has_customer_service);
                        GM_setValue('has_product_listing', data.has_product_listing);
                        console.log('💡 功能类型已保存:', data.function_type);

                        // 保存店铺信息
                        const shopName = getShopInfo();
                        GM_setValue('shop_name', shopName);
                        console.log('🏪 店铺信息已保存:', shopName);

                        // 显示功能类型弹窗
                        showSuccessPopup(data.function_type, data.has_customer_service, data.has_product_listing);
                    }

                    showMessage(messageDiv, data.message || '验证成功！正在加载脚本...', 'success');

                    // 延长显示时间以便用户看到功能类型提示
                    setTimeout(() => {
                        try {
                            const panel = document.getElementById('auth-panel');
                            if (panel) panel.remove();

                            if (!data.script || data.script.trim() === '') {
                                throw new Error('从服务器获取的脚本代码为空');
                            }

                            console.log('开始执行脚本...');

                            // 【修复】在脚本执行前设置完整的API访问权限
                            setupAPIAccess();

                            // 【修复】测试API可用性
                            testAPIAvailability();

                            // 执行脚本
                            eval(data.script);
                            console.log('脚本执行成功');

                            // 【修复】脚本执行后再次确保API可用
                            setTimeout(() => {
                                setupAPIAccess();
                                testAPIAvailability();
                                console.log('🔄 脚本执行后API权限重新设置完成');
                            }, 1000);

                            isScriptLoaded = true;

                            // 创建控制面板和浮动图标
                            createControlPanel();
                            createFloatingIcon();

                            // 恢复上次的面板状态
                            restorePanelState();

                            // 【新增】启动卡密状态监控
                            startKeyStatusMonitor(key);

                            // 【新增】启动API配置监控和API密钥监控
                            API_CONFIG.startConfigMonitoring();
                            ApiKeyManager.startMonitoring();

                            console.log('手动验证成功，控制面板已创建');

                        } catch (scriptError) {
                            console.error('脚本执行错误:', scriptError);
                            alert('❌ 脚本执行失败: ' + scriptError.message);
                        }
                    }, 3000);

                } else {
                    // 【修复】增强错误信息处理
                    let errorMessage = data.message || '验证失败';

                    // 【优化】根据错误代码提供更具体的错误信息，包括同时登录限制
                    if (data.error_code) {
                        switch (data.error_code) {
                            case 'CONCURRENT_LOGIN_LIMIT':
                                errorMessage = '老板您好！\n\n该卡密已经在其他电脑登录，不能同时登录';
                                break;
                            case 'MULTI_STORE_LOGIN_LIMIT':
                                const maxDevices = data.max_devices || '未知';
                                errorMessage = `老板您好！\n\n该多店卡密已达到最大同时登录数量限制（${maxDevices}台设备），请先退出其他设备后再登录`;
                                break;
                            case 'KEY_DELETED':
                                errorMessage = '卡密不存在或已被删除，请检查卡密是否正确';
                                break;
                            case 'KEY_DISABLED':
                                errorMessage = '卡密已被禁用，请联系代理商';
                                break;
                            case 'KEY_EXPIRED':
                                errorMessage = '卡密已过期，请联系代理商续费';
                                break;
                            case 'INVALID_KEY':
                                errorMessage = '无效的卡密格式，请检查后重试';
                                break;
                            default:
                                errorMessage = data.message || '验证失败';
                        }
                    }

                    // 【新增】检查是否为系统错误
                    if (errorMessage.includes('系统错误') || errorMessage.includes('系统异常')) {
                        errorMessage += '\n\n💡 可能的解决方案：\n1. 检查网络连接\n2. 稍后重试\n3. 联系技术支持';

                        // 【新增】显示网络诊断信息
                        console.log('🔍 开始网络诊断...');
                        performNetworkDiagnosis();
                    }

                    showMessage(messageDiv, errorMessage, 'error');
                }
            })
            .catch(function(error) {
                console.error('❌ 验证请求失败:', error);
                verifyBtn.disabled = false;
                verifyBtn.textContent = '验证';

                // 【修复】更详细的网络错误处理
                let errorMessage = '网络连接失败';

                if (error && error.message) {
                    errorMessage = '网络错误: ' + error.message;
                } else {
                    errorMessage = '网络连接失败，请检查网络连接';
                }

                showMessage(messageDiv, errorMessage, 'error');

                // 【新增】执行网络诊断
                performNetworkDiagnosis();

                // 【新增】尝试切换服务器重试
                console.log('🔄 尝试切换服务器重试...');
                API_CONFIG.switchServer();

                API_CONFIG.makeRequest('verify', { key: key })
                    .then(function(retryData) {
                        console.log('🔄 重试验证成功:', retryData);
                        verifyBtn.disabled = false;
                        verifyBtn.textContent = '验证';

                        // 处理重试成功的响应
                        if (retryData.success) {
                            showMessage(messageDiv, '验证成功（已切换服务器）', 'success');
                            // 继续处理成功逻辑...
                        } else {
                            showMessage(messageDiv, retryData.message || '验证失败', 'error');
                        }
                    })
                    .catch(function(retryError) {
                        console.error('❌ 重试验证也失败:', retryError);
                        showMessage(messageDiv, '所有服务器都无法连接，请稍后重试', 'error');
                    });
            });
    }

    // 【新增】网络诊断函数
    function performNetworkDiagnosis() {
        console.log('🔍 执行网络诊断...');

        // 检查基本网络连接
        const testUrls = [
            'http://xiaomeihuakefu.cn',
            'http://xiaomeihuakefu.cn/api/verify.php',
            'https://www.baidu.com'
        ];

        testUrls.forEach((url, index) => {
            const img = new Image();
            img.onload = function() {
                console.log(`✅ 网络诊断 ${index + 1}: ${url} - 可访问`);
            };
            img.onerror = function() {
                console.error(`❌ 网络诊断 ${index + 1}: ${url} - 不可访问`);
            };
            img.src = url + '/favicon.ico?' + Date.now();
        });

        // 检查DNS解析
        console.log('🔍 检查DNS解析...');
        try {
            const xhr = new XMLHttpRequest();
            xhr.open('GET', 'http://xiaomeihuakefu.cn', true);
            xhr.timeout = 5000;
            xhr.onload = function() {
                console.log('✅ DNS解析正常');
            };
            xhr.onerror = function() {
                console.error('❌ DNS解析失败');
            };
            xhr.send();
        } catch (e) {
            console.error('❌ DNS检查异常:', e);
        }
    }

    // 【修改】自动验证保存的卡密 - 使用API配置系统
    async function autoVerifyKey(savedKey) {
        console.log('🔄 尝试自动验证保存的卡密...', savedKey.substring(0, 8) + '...');

        try {
            const data = await API_CONFIG.makeRequest('verify', { key: savedKey });
            console.log('📨 自动验证API响应:', data);
            console.log('📋 自动验证解析后的响应数据:', data);

            if (data.success) {
                console.log('自动验证成功');
                isAuthenticated = true;
                currentKey = savedKey;

                // 检查脚本代码
                if (data.script && data.script.trim() !== '') {
                    try {
                        // 保存功能类型信息和店铺信息
                        if (data.function_type) {
                            GM_setValue('function_type', data.function_type);
                            GM_setValue('has_customer_service', data.has_customer_service);
                            GM_setValue('has_product_listing', data.has_product_listing);
                            console.log('💡 自动验证 - 功能类型已保存:', data.function_type);

                            // 保存店铺信息
                            const shopName = getShopInfo();
                            GM_setValue('shop_name', shopName);
                            console.log('🏪 自动验证 - 店铺信息已保存:', shopName);

                            // 自动验证成功也显示弹窗
                            showSuccessPopup(data.function_type, data.has_customer_service, data.has_product_listing);
                        }

                        // 【修复】在脚本执行前设置完整的API访问权限
                        console.log('自动验证成功，开始执行脚本...');
                        setupAPIAccess();
                        testAPIAvailability();

                        // 执行脚本
                        eval(data.script);
                        console.log('自动验证脚本执行成功');

                        // 【修复】脚本执行后再次确保API可用
                        setTimeout(() => {
                            setupAPIAccess();
                            testAPIAvailability();
                            console.log('🔄 自动验证脚本执行后API权限重新设置完成');
                        }, 1000);

                        isScriptLoaded = true;

                        // 创建控制面板和浮动图标
                        createControlPanel();
                        createFloatingIcon();

                        // 【调用优化后的函数】恢复上次的面板状态
                        restorePanelState();

                        // 【新增】启动卡密状态监控
                        startKeyStatusMonitor(savedKey);

                        // 【新增】启动API配置监控和API密钥监控
                        API_CONFIG.startConfigMonitoring();
                        ApiKeyManager.startMonitoring();

                        console.log('自动验证成功，控制面板已创建');

                    } catch (scriptError) {
                        console.error('自动验证脚本执行错误:', scriptError);
                        // 如果脚本执行失败，显示验证界面
                        createUI();
                    }
                } else {
                    console.error('自动验证：脚本代码为空');
                    createUI();
                }

            } else {
                console.log('自动验证失败，显示验证界面');
                console.log('失败原因:', data.message);

                // 【修复】检查是否为系统错误
                if (data.message && (data.message.includes('系统错误') || data.message.includes('系统异常'))) {
                    console.error('❌ 自动验证遇到系统错误:', data.message);
                    performNetworkDiagnosis();
                }

                createUI();
            }
        } catch (error) {
            console.error('❌ 自动验证请求失败:', error);

            // 【修复】网络错误时执行诊断
            performNetworkDiagnosis();

            // 【新增】尝试切换服务器重试
            console.log('🔄 自动验证切换服务器重试...');
            API_CONFIG.switchServer();

            try {
                const retryData = await API_CONFIG.makeRequest('verify', { key: savedKey });
                console.log('🔄 自动验证重试成功:', retryData);

                if (retryData.success) {
                    // 重试成功，继续处理
                    console.log('自动验证重试成功，继续处理...');
                    // 这里可以复用上面的成功处理逻辑
                } else {
                    createUI();
                }
            } catch (retryError) {
                console.error('❌ 自动验证重试也失败:', retryError);
                createUI();
            }
        }
    }

    // 【修改】页面加载后执行 - 初始化API配置系统
    async function main() {
        console.log('调试版加载器启动...');

        // 【新增】初始化API配置系统
        await API_CONFIG.init();

        const savedKey = GM_getValue('saved_license_key_xiaomeihua', null);
        if (savedKey) {
            console.log('发现保存的卡密，尝试自动验证...');
            // 设置当前卡密以便配置系统使用
            currentKey = savedKey;
            await autoVerifyKey(savedKey);
        } else {
            console.log('未发现保存的卡密，显示验证界面...');
            createUI();
        }
    }

    if (document.readyState === 'complete' || document.readyState === 'interactive') {
        main();
    } else {
        window.addEventListener('DOMContentLoaded', main);
    }

})();
