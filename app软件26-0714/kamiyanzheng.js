// ==UserScript==
// @name         小梅花AI客服助手
// @namespace    http://xiaomeihuakefu.cn/
// @version      1.0.0
// @description  小梅花智能AI客服助手
// <AUTHOR>
// @match        https://store.weixin.qq.com/shop/kf*
// @grant        GM_xmlhttpRequest
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_deleteValue
// @grant        GM_addStyle
// @grant        GM_openInTab
// @grant        window.open
// @grant        unsafeWindow
// @connect      xiaomeihuakefu.cn
// @connect      *
// ==/UserScript==

(function() {
    'use strict';

    console.log('=== 小梅花AI客服助手调试版启动 ===');

    let isScriptLoaded = false;
    let currentKey = null;
    let controlPanel = null;
    let floatingIcon = null;
    let isAuthenticated = false;
    let keyStatusMonitor = null; // 卡密状态监控定时器

    // 【修复】全局API保存，确保动态脚本可以访问
    window._tampermonkeyAPIs = {
        GM_openInTab: GM_openInTab,
        GM_setValue: GM_setValue,
        GM_getValue: GM_getValue,
        GM_xmlhttpRequest: GM_xmlhttpRequest,
        GM_addStyle: GM_addStyle,
        windowOpen: window.open
    };

    // 【修复】增强的权限传递函数
    function setupAPIAccess() {
        console.log('🔧 设置API访问权限...');

        // 方法1: 通过unsafeWindow传递
        if (typeof unsafeWindow !== 'undefined') {
            unsafeWindow.GM_openInTab = GM_openInTab;
            unsafeWindow.GM_setValue = GM_setValue;
            unsafeWindow.GM_getValue = GM_getValue;
            unsafeWindow.GM_xmlhttpRequest = GM_xmlhttpRequest;
            unsafeWindow.GM_addStyle = GM_addStyle;
            console.log('✅ unsafeWindow API传递完成');
        }

        // 方法2: 直接在window对象上设置
        window.GM_openInTab = GM_openInTab;
        window.GM_setValue = GM_setValue;
        window.GM_getValue = GM_getValue;
        window.GM_xmlhttpRequest = GM_xmlhttpRequest;
        window.GM_addStyle = GM_addStyle;
        console.log('✅ window API设置完成');

        // 方法3: 确保window.open可用
        if (typeof window.open === 'undefined' || !window.open) {
            window.open = function(url, name, specs) {
                console.log('🔗 使用GM_openInTab打开链接:', url);
                if (typeof GM_openInTab !== 'undefined') {
                    return GM_openInTab(url, { active: true });
                }
                console.error('❌ GM_openInTab不可用');
                return null;
            };
            console.log('✅ window.open备用实现已设置');
        }

        // 方法4: 创建全局辅助函数
        window.openNewTab = function(url) {
            console.log('🚀 尝试打开新标签页:', url);

            // 优先使用GM_openInTab
            if (typeof GM_openInTab !== 'undefined') {
                console.log('使用GM_openInTab');
                return GM_openInTab(url, { active: true });
            }

            // 备用使用window.open
            if (typeof window.open !== 'undefined') {
                console.log('使用window.open');
                return window.open(url, '_blank');
            }

            // 最后备用：通过location.href跳转
            console.log('使用location.href跳转');
            window.location.href = url;
            return null;
        };

        console.log('🎯 API访问权限设置完成');
    }

    // 【修复】API可用性测试函数
    function testAPIAvailability() {
        console.log('🧪 测试API可用性...');

        const results = {
            GM_openInTab: typeof GM_openInTab !== 'undefined',
            window_open: typeof window.open !== 'undefined',
            unsafeWindow: typeof unsafeWindow !== 'undefined',
            openNewTab: typeof window.openNewTab !== 'undefined'
        };

        console.log('API可用性测试结果:', results);
        return results;
    }

    // 【新增】卡密状态检查函数
    function checkKeyStatus(key, silent = false) {
        if (!key || !isAuthenticated) {
            return;
        }

        if (!silent) {
            console.log('🔍 检查卡密状态:', key.substring(0, 8) + '...');
        }

        GM_xmlhttpRequest({
            method: 'POST',
            url: 'http://xiaomeihuakefu.cn/api/verify.php',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
                'Accept': 'application/json, text/plain, */*'
            },
            data: `key=${encodeURIComponent(key)}&check_status=1`,
            timeout: 10000,
            onload: function(response) {
                if (!silent) {
                    console.log('📨 卡密状态检查响应:', response.status, response.responseText);
                }

                if (response.status === 200 && response.responseText) {
                    try {
                        const data = JSON.parse(response.responseText);
                        handleKeyStatusResponse(data);
                    } catch (e) {
                        if (!silent) {
                            console.error('❌ 卡密状态响应解析失败:', e);
                        }
                    }
                }
            },
            onerror: function(error) {
                if (!silent) {
                    console.error('❌ 卡密状态检查失败:', error);
                }
            },
            ontimeout: function() {
                if (!silent) {
                    console.error('❌ 卡密状态检查超时');
                }
            }
        });
    }

    // 【新增】处理卡密状态响应
    function handleKeyStatusResponse(data) {
        console.log('📋 卡密状态数据:', data);

        if (!data.success) {
            // 卡密状态异常，需要处理
            let message = '';
            let title = '⚠️ 卡密状态异常';

            if (data.error_code) {
                switch (data.error_code) {
                    case 'CONCURRENT_LOGIN_LIMIT':
                        message = '老板您好！\n\n该卡密已经在其他电脑登录，不能同时登录';
                        title = '🚫 同时登录限制';
                        break;
                    case 'MULTI_STORE_LOGIN_LIMIT':
                        const maxDevices = data.max_devices || '未知';
                        message = `老板您好！\n\n该多店卡密已达到最大同时登录数量限制（${maxDevices}台设备），请先退出其他设备后再登录`;
                        title = '🚫 多店登录限制';
                        break;
                    case 'KEY_DISABLED':
                        message = '您的卡密已经被禁用，如有疑问请联系代理商';
                        title = '🚫 卡密已禁用';
                        break;
                    case 'KEY_DELETED':
                        message = '您的卡密不存在已被删除，如有疑问请联系代理商';
                        title = '❌ 卡密已删除';
                        break;
                    case 'KEY_EXPIRED':
                        message = '您的卡密已经过期，如需继续使用请联系代理商';
                        title = '⏰ 卡密已过期';
                        break;
                    default:
                        message = data.message || '卡密验证失败，请重新验证';
                        break;
                }
            } else {
                message = data.message || '卡密验证失败，请重新验证';
            }

            console.warn('⚠️ 卡密状态异常:', message);

            // 显示状态异常弹窗
            showKeyStatusAlert(title, message);

            // 清除本地数据并退出
            handleKeyStatusExit();
        } else {
            // 卡密状态正常，更新功能信息
            if (data.function_type) {
                GM_setValue('function_type', data.function_type);
                GM_setValue('has_customer_service', data.has_customer_service);
                GM_setValue('has_product_listing', data.has_product_listing);

                // 更新控制面板显示
                updateFunctionTypeDisplay();
            }
        }
    }

    // 【新增】显示卡密状态异常弹窗
    function showKeyStatusAlert(title, message) {
        // 移除可能存在的旧弹窗
        const existingAlert = document.getElementById('key-status-alert');
        if (existingAlert) {
            existingAlert.remove();
        }

        const alert = document.createElement('div');
        alert.id = 'key-status-alert';
        alert.innerHTML = `
            <div class="alert-overlay"></div>
            <div class="alert-content">
                <div class="alert-header">
                    ${title}
                </div>
                <div class="alert-body">
                    <div class="alert-message">
                        ${message}
                    </div>
                    <div class="alert-buttons">
                        <button id="alert-confirm-btn" class="alert-btn alert-btn-primary">确定</button>
                    </div>
                </div>
            </div>
        `;

        // 添加样式
        GM_addStyle(`
            #key-status-alert {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 999999;
                font-family: sans-serif;
            }

            #key-status-alert .alert-overlay {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.6);
                backdrop-filter: blur(5px);
            }

            #key-status-alert .alert-content {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                border-radius: 15px;
                box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
                overflow: hidden;
                min-width: 400px;
                max-width: 90vw;
                animation: alertFadeIn 0.3s ease-out;
            }

            #key-status-alert .alert-header {
                background: linear-gradient(135deg, #dc3545, #c82333);
                color: white;
                padding: 20px;
                font-size: 18px;
                font-weight: bold;
                text-align: center;
            }

            #key-status-alert .alert-body {
                padding: 30px;
            }

            #key-status-alert .alert-message {
                font-size: 16px;
                line-height: 1.6;
                color: #333;
                text-align: center;
                margin-bottom: 25px;
            }

            #key-status-alert .alert-buttons {
                text-align: center;
            }

            #key-status-alert .alert-btn {
                padding: 12px 30px;
                border: none;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s ease;
            }

            #key-status-alert .alert-btn-primary {
                background: linear-gradient(135deg, #dc3545, #c82333);
                color: white;
            }

            #key-status-alert .alert-btn-primary:hover {
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
            }

            @keyframes alertFadeIn {
                from {
                    opacity: 0;
                    transform: translate(-50%, -50%) scale(0.9);
                }
                to {
                    opacity: 1;
                    transform: translate(-50%, -50%) scale(1);
                }
            }
        `);

        document.body.appendChild(alert);

        // 绑定确定按钮事件
        document.getElementById('alert-confirm-btn').addEventListener('click', () => {
            alert.remove();
        });

        // 点击遮罩层关闭
        alert.querySelector('.alert-overlay').addEventListener('click', () => {
            alert.remove();
        });
    }

    // 【新增】处理卡密状态异常退出
    function handleKeyStatusExit() {
        console.log('🚪 卡密状态异常，执行退出流程...');

        // 停止状态监控
        if (keyStatusMonitor) {
            clearInterval(keyStatusMonitor);
            keyStatusMonitor = null;
        }

        // 清除本地存储的数据
        GM_deleteValue('saved_license_key_xiaomeihua');
        GM_deleteValue('function_type');
        GM_deleteValue('has_customer_service');
        GM_deleteValue('has_product_listing');
        GM_deleteValue('shop_name');
        GM_deleteValue('panel_state_xiaomeihua');

        // 重置状态变量
        isAuthenticated = false;
        currentKey = null;
        isScriptLoaded = false;

        // 移除控制面板和浮动图标
        if (controlPanel) {
            controlPanel.remove();
            controlPanel = null;
        }
        if (floatingIcon) {
            floatingIcon.remove();
            floatingIcon = null;
        }

        // 延迟后重新加载页面或显示验证界面
        setTimeout(() => {
            location.reload();
        }, 3000);
    }

    // 【新增】启动卡密状态监控
    function startKeyStatusMonitor(key) {
        if (!key) return;

        console.log('🔄 启动卡密状态监控...');

        // 停止之前的监控
        if (keyStatusMonitor) {
            clearInterval(keyStatusMonitor);
        }

        // 每5分钟检查一次卡密状态
        keyStatusMonitor = setInterval(() => {
            checkKeyStatus(key, true); // 静默检查
        }, 5 * 60 * 1000); // 5分钟

        // 立即进行一次检查（非静默）
        setTimeout(() => {
            checkKeyStatus(key, false);
        }, 10000); // 10秒后进行首次检查
    }

    // 【新增】停止卡密状态监控
    function stopKeyStatusMonitor() {
        if (keyStatusMonitor) {
            console.log('🛑 停止卡密状态监控');
            clearInterval(keyStatusMonitor);
            keyStatusMonitor = null;
        }
    }

    // 【修复】添加缺失的showMessage函数
    function showMessage(element, message, type) {
        if (!element) {
            console.error('showMessage: 元素不存在');
            return;
        }

        // 清除之前的类名
        element.className = '';

        // 根据类型添加相应的样式类
        switch(type) {
            case 'success':
                element.className = 'success';
                break;
            case 'error':
                element.className = 'error';
                break;
            case 'info':
                element.className = 'info';
                break;
            default:
                element.className = '';
        }

        // 设置消息内容
        element.innerHTML = message;
        console.log(`showMessage [${type}]: ${message}`);
    }

    // 创建UI
    function createUI() {
        if (document.getElementById('auth-panel')) return;

        const panel = document.createElement('div');
        panel.id = 'auth-panel';
        panel.innerHTML = `
            <div class="auth-header">
                小梅花AI客服助手 v1.0.0 (调试版)
                <button id="close-panel-btn" style="float: right; background: none; border: none; color: white; font-size: 18px; cursor: pointer; padding: 0; width: 20px; height: 20px;">×</button>
            </div>
            <div class="auth-body">
                <p>请输入卡密以解锁完整功能:</p>
                <input type="text" id="license-key-input" placeholder="在此输入卡密">
                <button id="verify-key-btn">验证</button>
                <div id="auth-message"></div>
            </div>
        `;
        document.body.appendChild(panel);

        GM_addStyle(`
            #auth-panel { position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 99999;
                         background: rgba(255,255,255,0.95); border: 1px solid #ddd; border-radius: 15px;
                         box-shadow: 0 10px 30px rgba(0,0,0,0.3); width: 380px; font-family: sans-serif;
                         backdrop-filter: blur(10px); }
            #auth-panel .auth-header { background: linear-gradient(135deg, #667eea, #764ba2); color: white;
                                      padding: 20px; border-top-left-radius: 15px; border-top-right-radius: 15px;
                                      font-size: 18px; text-align: center; font-weight: bold; position: relative; }
            #auth-panel .auth-body { padding: 25px; }
            #auth-panel p { margin-bottom: 15px; color: #333; font-size: 14px; }
            #auth-panel input { width: 100%; padding: 12px; box-sizing: border-box; margin-bottom: 15px;
                               border-radius: 8px; border: 2px solid #e1e5e9; font-size: 14px; }
            #auth-panel input:focus { outline: none; border-color: #667eea; }
            #auth-panel button { width: 100%; padding: 12px; background: linear-gradient(135deg, #667eea, #764ba2);
                                color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 16px;
                                font-weight: bold; transition: all 0.3s ease; }
            #auth-panel button:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(102,126,234,0.4); }
            #auth-message { margin-top: 15px; text-align: center; font-weight: bold; }
            .success { color: #28a745; }
            .error { color: #dc3545; }
            .info { color: #007bff; }

            /* 成功提示中的特殊样式 */
            .success span[style*="color: #ff0000"] {
                color: #ff0000 !important;
                font-weight: bold !important;
            }

            /* 控制面板样式 */
            #control-panel { position: fixed; top: 20px; left: 20px; z-index: 99998; background: rgba(255,255,255,0.95);
                           border: 1px solid #ddd; border-radius: 10px; box-shadow: 0 5px 20px rgba(0,0,0,0.2);
                           width: 320px; font-family: sans-serif; backdrop-filter: blur(10px);
                           transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1); }
            #control-panel .panel-header { background: linear-gradient(135deg, #28a745, #20c997); color: white;
                                         padding: 15px; border-top-left-radius: 10px; border-top-right-radius: 10px;
                                         font-size: 16px; font-weight: bold; position: relative; }
            #control-panel .panel-body { padding: 20px; }
            #control-panel .status-item { margin-bottom: 10px; padding: 8px; background: #f8f9fa; border-radius: 5px; }
            #control-panel .status-label { font-weight: bold; color: #495057; margin-bottom: 4px; }
            #control-panel .status-value { color: #28a745; word-wrap: break-word; line-height: 1.4; }

            /* 浮动图标样式 */
            #floating-icon {
                position: fixed;
                top: 20px;
                left: 20px;
                width: 60px;
                height: 60px;
                border-radius: 50%;
                cursor: pointer;
                z-index: 99999;
                display: none;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                background: linear-gradient(135deg, #ff6b9d, #c44569, #f8b500, #e55039, #3c6382);
                background-size: 300% 300%;
                animation: gradientShift 3s ease infinite, float 2s ease-in-out infinite;
                box-shadow: 0 4px 15px rgba(255, 107, 157, 0.4);
            }

            #floating-icon:hover {
                transform: scale(1.1);
                box-shadow: 0 8px 25px rgba(255, 107, 157, 0.6);
            }

            #floating-icon::before {
                content: '';
                position: absolute;
                top: -10px;
                left: -10px;
                right: -10px;
                bottom: -10px;
                border-radius: 50%;
                background: radial-gradient(circle, rgba(255, 107, 157, 0.3) 0%, transparent 70%);
                animation: pulse 2s ease-in-out infinite;
                z-index: -1;
            }

            #floating-icon::after {
                content: '';
                position: absolute;
                top: -20px;
                left: -20px;
                right: -20px;
                bottom: -20px;
                border-radius: 50%;
                background: radial-gradient(circle, rgba(255, 107, 157, 0.1) 0%, transparent 70%);
                animation: pulse 2s ease-in-out infinite 0.5s;
                z-index: -2;
            }

            /* 梅花图标 */
            .plum-flower {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 30px;
                height: 30px;
                color: white;
                font-size: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                animation: rotate 4s linear infinite;
            }

            /* 动画定义 */
            @keyframes gradientShift {
                0% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
                100% { background-position: 0% 50%; }
            }

            @keyframes float {
                0%, 100% { transform: translateY(0px); }
                50% { transform: translateY(-5px); }
            }

            @keyframes pulse {
                0%, 100% {
                    opacity: 0.8;
                    transform: scale(1);
                }
                50% {
                    opacity: 0.4;
                    transform: scale(1.2);
                }
            }

            @keyframes rotate {
                0% { transform: translate(-50%, -50%) rotate(0deg); }
                100% { transform: translate(-50%, -50%) rotate(360deg); }
            }

            /* 面板隐藏时的样式 */
            #control-panel.minimized {
                opacity: 0;
                transform: scale(0.8) translateX(100px);
                pointer-events: none;
            }

            /* 面板显示动画 */
            #control-panel.show {
                opacity: 1;
                transform: scale(1) translateX(0);
                pointer-events: all;
            }

            /* 成功弹窗样式 */
            #success-popup {
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                z-index: 100000;
                background: rgba(255, 255, 255, 0.98);
                border: 2px solid #28a745;
                border-radius: 20px;
                box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
                width: 550px;
                max-width: 90vw;
                font-family: sans-serif;
                backdrop-filter: blur(15px);
                animation: popupFadeIn 0.5s ease-out;
            }

            #success-popup .popup-header {
                background: linear-gradient(135deg, #28a745, #20c997);
                color: white;
                padding: 20px;
                border-top-left-radius: 18px;
                border-top-right-radius: 18px;
                text-align: center;
                font-size: 20px;
                font-weight: bold;
            }

            #success-popup .popup-body {
                padding: 30px;
                text-align: center;
            }

            #success-popup .function-info {
                font-size: 24px;
                font-weight: bold;
                margin: 20px 0;
                padding: 15px;
                border-radius: 12px;
                background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
                border: 2px solid #e1bee7;
            }

            #success-popup .success-text {
                font-size: 16px;
                color: #155724;
                line-height: 1.6;
                margin: 15px 0;
            }

            #success-popup .countdown {
                font-size: 14px;
                color: #6c757d;
                margin-top: 20px;
            }

            @keyframes popupFadeIn {
                from {
                    opacity: 0;
                    transform: translate(-50%, -50%) scale(0.8);
                }
                to {
                    opacity: 1;
                    transform: translate(-50%, -50%) scale(1);
                }
            }

            @keyframes popupFadeOut {
                from {
                    opacity: 1;
                    transform: translate(-50%, -50%) scale(1);
                }
                to {
                    opacity: 0;
                    transform: translate(-50%, -50%) scale(0.8);
                }
            }
        `);

        document.getElementById('verify-key-btn').addEventListener('click', verifyKey);
        document.getElementById('license-key-input').addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                verifyKey();
            }
        });
        document.getElementById('close-panel-btn').addEventListener('click', function() {
            document.getElementById('auth-panel').remove();
        });
    }

    // 创建浮动图标
    function createFloatingIcon() {
        // 如果已存在，先移除
        if (floatingIcon) {
            if (floatingIcon.parentNode) {
                floatingIcon.parentNode.removeChild(floatingIcon);
            }
        }

        // 检查是否已有同ID元素
        const existingIcon = document.getElementById('floating-icon');
        if (existingIcon) {
            existingIcon.remove();
        }

        floatingIcon = document.createElement('div');
        floatingIcon.id = 'floating-icon';
        floatingIcon.innerHTML = `
            <div class="plum-flower">🌸</div>
        `;

        floatingIcon.addEventListener('click', function() {
            toggleControlPanel();
        });

        document.body.appendChild(floatingIcon);
        console.log('浮动图标已创建并添加到页面');
    }

    // 创建控制面板
    function createControlPanel() {
        if (document.getElementById('control-panel')) return;

        controlPanel = document.createElement('div');
        controlPanel.id = 'control-panel';
        controlPanel.innerHTML = `
            <div class="panel-header">
                🌸 脚本控制面板
                <button id="minimize-panel-btn" style="float: right; background: none; border: none; color: white; font-size: 16px; cursor: pointer; padding: 0; width: 18px; height: 18px;">−</button>
            </div>
            <div class="panel-body">
                <div class="status-item">
                    <div class="status-label">🟢 状态:</div>
                    <div class="status-value">已激活</div>
                </div>
                <div class="status-item">
                    <div class="status-label">🏪 店铺:</div>
                    <div class="status-value" id="shop-name-value" style="color: #e74c3c; font-weight: bold;">${getShopInfo()}</div>
                </div>
                <div class="status-item">
                    <div class="status-label">🔑 卡密:</div>
                    <div class="status-value" style="font-family: monospace; font-size: 11px; word-break: break-all;">${currentKey || '未知'}</div>
                </div>
                <div class="status-item">
                    <div class="status-label">⭐ 功能类型:</div>
                    <div class="status-value" id="function-type-value">检测中...</div>
                </div>
                <div class="status-item">
                    <div class="status-label">📦 版本:</div>
                    <div class="status-value">v1.0.0</div>
                </div>
                <div class="status-item">
                    <div class="status-label">⏰ 运行时间:</div>
                    <div class="status-value" id="runtime-counter">0秒</div>
                </div>
                <button id="logout-btn" style="width: 100%; padding: 10px; background: linear-gradient(135deg, #dc3545, #c82333); color: white; border: none; border-radius: 5px; cursor: pointer; margin-top: 15px; font-weight: bold; transition: all 0.3s ease;">🚪 退出登录</button>
            </div>
        `;
        document.body.appendChild(controlPanel);

        // 启动运行时间计时器
        startRuntimeCounter();

        document.getElementById('minimize-panel-btn').addEventListener('click', function() {
            minimizeControlPanel();
        });

        document.getElementById('logout-btn').addEventListener('click', function() {
            if (confirm('🤔 确定要退出登录吗？\n\n退出后需要重新输入卡密才能使用功能。')) {
                // 【新增】停止卡密状态监控
                stopKeyStatusMonitor();

                GM_deleteValue('saved_license_key_xiaomeihua');
                GM_deleteValue('panel_state_xiaomeihua');
                GM_deleteValue('function_type');
                GM_deleteValue('has_customer_service');
                GM_deleteValue('has_product_listing');
                GM_deleteValue('shop_name');

                if (controlPanel) controlPanel.remove();
                if (floatingIcon) floatingIcon.remove();
                location.reload();
            }
        });

        // 添加悬停效果
        const logoutBtn = document.getElementById('logout-btn');
        logoutBtn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 5px 15px rgba(220, 53, 69, 0.4)';
        });
        logoutBtn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = 'none';
        });

        // 更新功能类型显示
        updateFunctionTypeDisplay();
    }

    // 最小化控制面板
    function minimizeControlPanel() {
        console.log('最小化控制面板...');

        if (controlPanel) {
            controlPanel.classList.add('minimized');
            setTimeout(() => {
                controlPanel.style.display = 'none';
            }, 300);
        }

        // 确保浮动图标存在并显示
        if (!floatingIcon) {
            console.log('浮动图标不存在，重新创建...');
            createFloatingIcon();
        }

        if (floatingIcon) {
            floatingIcon.style.display = 'block';
            floatingIcon.style.opacity = '1';
            floatingIcon.style.transform = 'scale(1)';
            console.log('浮动图标已显示');
        }

        // 保存面板状态
        GM_setValue('panel_state_xiaomeihua', 'minimized');
    }

    // 显示控制面板
    function showControlPanel() {
        if (controlPanel) {
            controlPanel.style.display = 'block';
            controlPanel.classList.remove('minimized');
            controlPanel.classList.add('show');
        }

        // 隐藏浮动图标
        if (floatingIcon) {
            floatingIcon.style.opacity = '0';
            floatingIcon.style.transform = 'scale(0.8)';
            setTimeout(() => {
                floatingIcon.style.display = 'none';
            }, 300);
        }

        // 保存面板状态
        GM_setValue('panel_state_xiaomeihua', 'expanded');
    }

    // 切换控制面板显示/隐藏
    function toggleControlPanel() {
        if (controlPanel.style.display === 'none' || controlPanel.classList.contains('minimized')) {
            showControlPanel();
        } else {
            minimizeControlPanel();
        }
    }

    // 恢复面板状态
    function restorePanelState() {
        // 刷新后总是保持缩略图状态
        console.log('恢复面板状态: 自动设置为缩略图');

        // 确保控制面板和浮动图标都已创建
        if (!controlPanel) {
            console.log('控制面板未创建，重新创建...');
            createControlPanel();
        }
        if (!floatingIcon) {
            console.log('浮动图标未创建，重新创建...');
            createFloatingIcon();
        }

        setTimeout(() => {
            minimizeControlPanel();
        }, 500);
    }

    // 启动运行时间计时器
    function startRuntimeCounter() {
        const startTime = Date.now();

        setInterval(() => {
            const runtimeElement = document.getElementById('runtime-counter');
            if (runtimeElement) {
                const elapsed = Math.floor((Date.now() - startTime) / 1000);
                const minutes = Math.floor(elapsed / 60);
                const seconds = elapsed % 60;

                if (minutes > 0) {
                    runtimeElement.textContent = `${minutes}分${seconds}秒`;
                } else {
                    runtimeElement.textContent = `${seconds}秒`;
                }
            }
        }, 1000);
    }

    // 更新功能类型显示
    function updateFunctionTypeDisplay() {
        const functionTypeElem = document.getElementById('function-type-value');
        if (!functionTypeElem) return;

        const functionType = GM_getValue('function_type', '');
        const hasCustomerService = GM_getValue('has_customer_service', true);
        const hasProductListing = GM_getValue('has_product_listing', false);

        let displayText = '';
        let displayColor = '';

        if (hasCustomerService && hasProductListing) {
            displayText = '小梅花AI全功能版本';
            displayColor = '#ff6b9d';
        } else if (hasProductListing && !hasCustomerService) {
            displayText = '小梅花AI上架产品功能版本';
            displayColor = '#4ecdc4';
        } else if (hasCustomerService && !hasProductListing) {
            displayText = '小梅花AI客服功能版本';
            displayColor = '#667eea';
        } else {
            displayText = '❌ 无功能';
            displayColor = '#666';
        }

        functionTypeElem.textContent = displayText;
        functionTypeElem.style.color = displayColor;
        functionTypeElem.style.fontWeight = 'bold';
    }

    // 【优化】获取店铺信息 - 精准识别店铺名称
    function getShopInfo() {
        try {
            console.log('🏪 开始获取店铺信息...');

            // 方法1: 从页面标题获取店铺名称（最准确）
            const title = document.title;
            console.log('页面标题:', title);

            // 匹配各种标题格式
            const titlePatterns = [
                /^(.+?)\s*[-|·|—|丨]\s*微信小店/,           // "小梅花旗舰店 - 微信小店"
                /^(.+?)\s*[-|·|—|丨]\s*客服/,               // "小梅花旗舰店 - 客服"
                /^(.+?)\s*[-|·|—|丨]\s*小店/,               // "小梅花旗舰店 - 小店"
                /^(.+?)\s*微信小店/,                       // "小梅花旗舰店微信小店"
                /^(.+?)\s*客服$/,                          // "小梅花旗舰店客服"
                /^(.+?)\s*小店$/                           // "小梅花旗舰店小店"
            ];

            for (const pattern of titlePatterns) {
                const match = title.match(pattern);
                if (match && match[1]) {
                    const shopName = match[1].trim();
                    if (shopName && shopName.length > 0 && shopName.length < 50) {
                        console.log('✅ 从标题获取店铺名称:', shopName);
                        return shopName;
                    }
                }
            }

            // 方法2: 从页面元素获取店铺名称
            console.log('📋 尝试从页面元素获取店铺名称...');
            const shopNameSelectors = [
                // 微信小店常见的店铺名称选择器
                '.shop-info .shop-name',
                '.store-header .store-name',
                '.shop-title',
                '.store-title',
                '[data-testid="shop-name"]',
                '[class*="shop-name"]',
                '[class*="store-name"]',
                '.header-title',
                '.page-title',
                // 通用选择器
                'h1', 'h2', '.title'
            ];

            for (const selector of shopNameSelectors) {
                const elements = document.querySelectorAll(selector);
                for (const element of elements) {
                    if (element && element.textContent) {
                        const text = element.textContent.trim();
                        console.log(`检查元素 ${selector}:`, text);

                        // 过滤条件更精确
                        if (text &&
                            text.length > 1 &&
                            text.length < 50 &&
                            !text.includes('客服') &&
                            !text.includes('微信') &&
                            !text.includes('设置') &&
                            !text.includes('管理') &&
                            !text.includes('页面') &&
                            !text.match(/^[0-9]+$/) && // 不是纯数字
                            !text.includes('kf') &&
                            !text.includes('shop')) {
                            console.log('✅ 从页面元素获取店铺名称:', text);
                            return text;
                        }
                    }
                }
            }

            // 方法3: 从URL路径获取店铺信息
            console.log('🔗 尝试从URL获取店铺信息...');
            const url = window.location.href;
            console.log('当前URL:', url);

            // 匹配URL中的店铺ID或名称
            const urlPatterns = [
                /\/shop\/([^\/\?]+)/,                      // /shop/shopname
                /shopId=([^&]+)/,                          // ?shopId=xxx
                /shop_id=([^&]+)/,                         // ?shop_id=xxx
                /store\/([^\/\?]+)/,                       // /store/storename
                /storeId=([^&]+)/                          // ?storeId=xxx
            ];

            for (const pattern of urlPatterns) {
                const match = url.match(pattern);
                if (match && match[1]) {
                    const identifier = decodeURIComponent(match[1]);
                    console.log('从URL获取标识符:', identifier);

                    // 如果是有意义的名称而不是ID
                    if (identifier &&
                        !identifier.match(/^[0-9a-f]+$/i) && // 不是纯16进制ID
                        !identifier.match(/^[0-9]+$/) &&     // 不是纯数字ID
                        identifier.length > 2) {
                        console.log('✅ 从URL获取店铺名称:', identifier);
                        return identifier;
                    } else {
                        console.log('✅ 从URL获取店铺ID:', identifier);
                        return `店铺${identifier}`;
                    }
                }
            }

            // 方法4: 从localStorage或sessionStorage获取
            console.log('💾 尝试从本地存储获取店铺信息...');
            const storageKeys = ['shopName', 'storeName', 'shop_name', 'store_name'];
            for (const key of storageKeys) {
                const stored = localStorage.getItem(key) || sessionStorage.getItem(key);
                if (stored && stored.trim()) {
                    console.log(`✅ 从本地存储 ${key} 获取店铺名称:`, stored);
                    return stored.trim();
                }
            }

            // 方法5: 检查页面中的JSON数据
            console.log('📄 尝试从页面JSON数据获取店铺信息...');
            const scripts = document.querySelectorAll('script');
            for (const script of scripts) {
                if (script.textContent) {
                    try {
                        // 查找包含店铺信息的JSON
                        const jsonMatches = script.textContent.match(/"shop_?name"\s*:\s*"([^"]+)"/i) ||
                                          script.textContent.match(/"store_?name"\s*:\s*"([^"]+)"/i);
                        if (jsonMatches && jsonMatches[1]) {
                            console.log('✅ 从JSON数据获取店铺名称:', jsonMatches[1]);
                            return jsonMatches[1];
                        }
                    } catch (e) {
                        // 忽略JSON解析错误
                    }
                }
            }

            console.log('⚠️ 无法获取具体店铺名称，使用默认称呼');
            return '尊贵的商家';

        } catch (error) {
            console.error('❌ 获取店铺信息时发生错误:', error);
            return '尊贵的商家';
        }
    }

    // 显示成功弹窗
    function showSuccessPopup(functionType, hasCustomerService, hasProductListing) {
        // 移除可能存在的旧弹窗
        const existingPopup = document.getElementById('success-popup');
        if (existingPopup) {
            existingPopup.remove();
        }

        // 获取店铺信息
        const shopName = getShopInfo();

        let functionName = '';
        let functionColor = '';
        let icon = '';

        if (hasCustomerService && hasProductListing) {
            functionName = '小梅花AI全功能版本';
            functionColor = '#ff6b9d';
            icon = '🌟';
        } else if (hasProductListing && !hasCustomerService) {
            functionName = '小梅花AI上架产品功能版本';
            functionColor = '#4ecdc4';
            icon = '📦';
        } else if (hasCustomerService && !hasProductListing) {
            functionName = '小梅花AI客服功能版本';
            functionColor = '#667eea';
            icon = '🤖';
        } else {
            functionName = '无功能版本';
            functionColor = '#666';
            icon = '❌';
        }

        const popup = document.createElement('div');
        popup.id = 'success-popup';
        popup.innerHTML = `
            <div class="popup-header">
                ${icon} 卡密验证成功
            </div>
            <div class="popup-body">
                <div class="welcome-message" style="
                    font-size: 18px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin: 20px 0;
                    padding: 15px;
                    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
                    border-radius: 12px;
                    border-left: 4px solid #28a745;
                    text-align: center;
                    line-height: 1.6;
                ">
                    🎉 欢迎 "<span style="color: #e74c3c; font-weight: bold;">${shopName}</span>"<br>
                    <span style="font-size: 16px; color: #6c757d;">感谢您使用小梅花智能AI客服系统</span>
                </div>
                <div class="function-info" style="color: ${functionColor};">
                    ${functionName}
                </div>
                <div class="success-text">
                    恭喜您！卡密验证通过，已成功激活对应功能。
                    <br>系统将为您的店铺提供智能化客服服务！
                </div>
                <div class="countdown" id="popup-countdown">
                    此弹窗将在 <span id="countdown-timer">5</span> 秒后自动关闭
                </div>
            </div>
        `;

        document.body.appendChild(popup);

        // 倒计时
        let countdown = 5;
        const timer = setInterval(() => {
            countdown--;
            const timerElem = document.getElementById('countdown-timer');
            if (timerElem) {
                timerElem.textContent = countdown;
            }

            if (countdown <= 0) {
                clearInterval(timer);
                // 添加淡出动画
                popup.style.animation = 'popupFadeOut 0.5s ease-in';
                setTimeout(() => {
                    if (popup.parentNode) {
                        popup.parentNode.removeChild(popup);
                    }
                }, 500);
            }
        }, 1000);

        // 点击弹窗关闭
        popup.addEventListener('click', () => {
            clearInterval(timer);
            popup.style.animation = 'popupFadeOut 0.5s ease-in';
            setTimeout(() => {
                if (popup.parentNode) {
                    popup.parentNode.removeChild(popup);
                }
            }, 500);
        });
    }

    // 验证逻辑
    function verifyKey() {
        const keyInput = document.getElementById('license-key-input');
        const messageDiv = document.getElementById('auth-message');
        const verifyBtn = document.getElementById('verify-key-btn');

        if (!keyInput || !messageDiv || !verifyBtn) {
            console.error('验证界面元素未找到:', {
                keyInput: !!keyInput,
                messageDiv: !!messageDiv,
                verifyBtn: !!verifyBtn
            });
            alert('❌ 验证界面初始化失败，请刷新页面重试');
            return;
        }

        const key = keyInput.value.trim();
        if (!key) {
            showMessage(messageDiv, '请输入卡密', 'error');
            return;
        }

        console.log('🔑 开始验证卡密:', key.substring(0, 8) + '...');

        verifyBtn.disabled = true;
        verifyBtn.textContent = '验证中...';
        showMessage(messageDiv, '正在验证卡密...', 'info');

        // 【修复】增加详细的请求日志
        console.log('📡 发送验证请求到:', 'http://xiaomeihuakefu.cn/api/verify.php');

        GM_xmlhttpRequest({
            method: 'POST',
            url: 'http://xiaomeihuakefu.cn/api/verify.php',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
                'Accept': 'application/json, text/plain, */*'
            },
            data: `key=${encodeURIComponent(key)}`,
            timeout: 15000, // 增加超时时间到15秒
            onload: function(response) {
                console.log('📨 验证API响应:', {
                    status: response.status,
                    statusText: response.statusText,
                    responseText: response.responseText,
                    headers: response.responseHeaders
                });

                // 【修复】确保按钮状态恢复
                verifyBtn.disabled = false;
                verifyBtn.textContent = '验证';

                // 【修复】检查HTTP状态码
                if (response.status !== 200) {
                    console.error('❌ HTTP状态码错误:', response.status);
                    showMessage(messageDiv, `服务器错误 (${response.status})，请稍后重试`, 'error');
                    return;
                }

                // 【修复】检查响应内容
                if (!response.responseText || response.responseText.trim() === '') {
                    console.error('❌ 服务器返回空响应');
                    showMessage(messageDiv, '服务器返回空响应，请检查网络连接', 'error');
                    return;
                }

                try {
                    const data = JSON.parse(response.responseText);
                    console.log('📋 解析后的响应数据:', data);

                    if (data.success) {
                        console.log('验证成功');
                        isAuthenticated = true;
                        currentKey = key;
                        GM_setValue('saved_license_key_xiaomeihua', key);

                        // 保存功能类型信息和店铺信息
                        if (data.function_type) {
                            GM_setValue('function_type', data.function_type);
                            GM_setValue('has_customer_service', data.has_customer_service);
                            GM_setValue('has_product_listing', data.has_product_listing);
                            console.log('💡 功能类型已保存:', data.function_type);

                            // 保存店铺信息
                            const shopName = getShopInfo();
                            GM_setValue('shop_name', shopName);
                            console.log('🏪 店铺信息已保存:', shopName);

                            // 显示功能类型弹窗
                            showSuccessPopup(data.function_type, data.has_customer_service, data.has_product_listing);
                        }

                        showMessage(messageDiv, data.message || '验证成功！正在加载脚本...', 'success');

                        // 延长显示时间以便用户看到功能类型提示
                        setTimeout(() => {
                            try {
                                const panel = document.getElementById('auth-panel');
                                if (panel) panel.remove();

                                if (!data.script || data.script.trim() === '') {
                                    throw new Error('从服务器获取的脚本代码为空');
                                }

                                console.log('开始执行脚本...');

                                // 【修复】在脚本执行前设置完整的API访问权限
                                setupAPIAccess();

                                // 【修复】测试API可用性
                                testAPIAvailability();

                                // 执行脚本
                                eval(data.script);
                                console.log('脚本执行成功');

                                // 【修复】脚本执行后再次确保API可用
                                setTimeout(() => {
                                    setupAPIAccess();
                                    testAPIAvailability();
                                    console.log('🔄 脚本执行后API权限重新设置完成');
                                }, 1000);

                                isScriptLoaded = true;

                                // 创建控制面板和浮动图标
                                createControlPanel();
                                createFloatingIcon();

                                // 恢复上次的面板状态
                                restorePanelState();

                                // 【新增】启动卡密状态监控
                                startKeyStatusMonitor(key);

                                console.log('手动验证成功，控制面板已创建');

                            } catch (scriptError) {
                                console.error('脚本执行错误:', scriptError);
                                alert('❌ 脚本执行失败: ' + scriptError.message);
                            }
                        }, 3000);

                    } else {
                        showMessage(messageDiv, data.message || '验证失败', 'error');
                    }
                } catch (e) {
                    console.error('❌ 解析响应失败:', e);
                    console.error('原始响应内容:', response.responseText);
                    showMessage(messageDiv, '服务器响应格式错误，请稍后重试', 'error');
                }
            },
            onerror: function(error) {
                console.error('❌ 验证请求失败:', error);
                verifyBtn.disabled = false;
                verifyBtn.textContent = '验证';
                showMessage(messageDiv, '网络连接失败，请检查网络连接', 'error');
            },
            ontimeout: function() {
                console.error('❌ 验证请求超时 (15秒)');
                verifyBtn.disabled = false;
                verifyBtn.textContent = '验证';
                showMessage(messageDiv, '请求超时，请检查网络后重试', 'error');
            },
            onabort: function() {
                console.error('❌ 验证请求被中断');
                verifyBtn.disabled = false;
                verifyBtn.textContent = '验证';
                showMessage(messageDiv, '请求被中断，请重试', 'error');
            }
        });
    }

    // 自动验证保存的卡密
    function autoVerifyKey(savedKey) {
        console.log('🔄 尝试自动验证保存的卡密...', savedKey.substring(0, 8) + '...');

        GM_xmlhttpRequest({
            method: 'POST',
            url: 'http://xiaomeihuakefu.cn/api/verify.php',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
                'Accept': 'application/json, text/plain, */*'
            },
            data: `key=${encodeURIComponent(savedKey)}`,
            timeout: 15000,
            onload: function(response) {
                console.log('📨 自动验证API响应:', {
                    status: response.status,
                    statusText: response.statusText,
                    responseText: response.responseText
                });

                // 【修复】检查HTTP状态码
                if (response.status !== 200) {
                    console.error('❌ 自动验证HTTP状态码错误:', response.status);
                    createUI();
                    return;
                }

                // 【修复】检查响应内容
                if (!response.responseText || response.responseText.trim() === '') {
                    console.error('❌ 自动验证服务器返回空响应');
                    createUI();
                    return;
                }

                try {
                    const data = JSON.parse(response.responseText);
                    console.log('📋 自动验证解析后的响应数据:', data);

                    if (data.success) {
                        console.log('自动验证成功');
                        isAuthenticated = true;

                        // 检查脚本代码
                        if (data.script && data.script.trim() !== '') {
                            try {
                                // 保存功能类型信息和店铺信息
                                if (data.function_type) {
                                    GM_setValue('function_type', data.function_type);
                                    GM_setValue('has_customer_service', data.has_customer_service);
                                    GM_setValue('has_product_listing', data.has_product_listing);
                                    console.log('💡 自动验证 - 功能类型已保存:', data.function_type);

                                    // 保存店铺信息
                                    const shopName = getShopInfo();
                                    GM_setValue('shop_name', shopName);
                                    console.log('🏪 自动验证 - 店铺信息已保存:', shopName);

                                    // 自动验证成功也显示弹窗
                                    showSuccessPopup(data.function_type, data.has_customer_service, data.has_product_listing);
                                }

                                // 【修复】在脚本执行前设置完整的API访问权限
                                console.log('自动验证成功，开始执行脚本...');
                                setupAPIAccess();
                                testAPIAvailability();

                                // 执行脚本
                                eval(data.script);
                                console.log('自动验证脚本执行成功');

                                // 【修复】脚本执行后再次确保API可用
                                setTimeout(() => {
                                    setupAPIAccess();
                                    testAPIAvailability();
                                    console.log('🔄 自动验证脚本执行后API权限重新设置完成');
                                }, 1000);

                                isScriptLoaded = true;

                                // 创建控制面板和浮动图标
                                createControlPanel();
                                createFloatingIcon();

                                // 恢复上次的面板状态
                                restorePanelState();

                                // 【新增】启动卡密状态监控
                                startKeyStatusMonitor(savedKey);

                                console.log('自动验证成功，控制面板已创建');

                            } catch (scriptError) {
                                console.error('自动验证脚本执行错误:', scriptError);
                                // 如果脚本执行失败，显示验证界面
                                createUI();
                            }
                        } else {
                            console.error('自动验证：脚本代码为空');
                            createUI();
                        }

                    } else {
                        console.log('自动验证失败，显示验证界面');
                        createUI();
                    }
                } catch (e) {
                    console.error('❌ 自动验证解析响应失败:', e);
                    console.error('原始响应内容:', response.responseText);
                    createUI();
                }
            },
            onerror: function(error) {
                console.error('❌ 自动验证请求失败:', error);
                createUI();
            },
            ontimeout: function() {
                console.error('❌ 自动验证请求超时 (15秒)');
                createUI();
            },
            onabort: function() {
                console.error('❌ 自动验证请求被中断');
                createUI();
            }
        });
    }

    // 页面加载后执行
    function main() {
        console.log('调试版加载器启动...');

        const savedKey = GM_getValue('saved_license_key_xiaomeihua', null);
        if (savedKey) {
            console.log('发现保存的卡密，尝试自动验证...');
            autoVerifyKey(savedKey);
        } else {
            console.log('未发现保存的卡密，显示验证界面...');
            createUI();
        }
    }

    if (document.readyState === 'complete' || document.readyState === 'interactive') {
        main();
    } else {
        window.addEventListener('DOMContentLoaded', main);
    }

})();