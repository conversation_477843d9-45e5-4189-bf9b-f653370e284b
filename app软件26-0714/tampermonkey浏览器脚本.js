// ==UserScript==
// @name         小梅花AI客服助手
// @namespace    http://xiaomeihuakefu.cn/
// @version      1.0.4
// @description  小梅花智能AI客服助手
// <AUTHOR>
// @match        https://store.weixin.qq.com/shop/kf*
// @grant        GM_xmlhttpRequest
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_deleteValue
// @grant        GM_addStyle
// @grant        GM_openInTab
// @grant        window.open
// @grant        unsafeWindow
// @connect      xiaomeihuakefu.cn
// @connect      *
// @run-at       document-end
// ==/UserScript==

window["__f__md1ijceb.3di"] = function(){with (this) {(async (u, { p, r, s }) => {try {r(u, s, [undefined,undefined,undefined,p.GM_xmlhttpRequest,p.GM_setValue,p.GM_getValue,p.GM_deleteValue,p.GM_addStyle,p.GM_openInTab,p.unsafeWindow,p.GM_info,p.GM]);} catch (e) {if (e.message && e.stack) {console.error("ERROR: Execution of script '小梅花AI客服助手' failed! " + e.message);console.log(e.stack);} else {console.error(e);}}})(async function(define,module,exports,GM_xmlhttpRequest,GM_setValue,GM_getValue,GM_deleteValue,GM_addStyle,GM_openInTab,unsafeWindow,GM_info,GM) {
    'use strict';

    console.log('=== 小梅花AI客服助手调试版启动 (v1.0.4) ===');

    // 【新增】直接执行初始化代码，不等待DOMContentLoaded
    console.log('🚀 直接执行初始化代码');
    
    // 【新增】强制执行延迟函数，确保DOM已加载
    function ensureDOMLoaded(callback) {
        if (document.readyState === 'complete' || document.readyState === 'interactive') {
            setTimeout(callback, 500);
        } else {
            window.addEventListener('DOMContentLoaded', () => setTimeout(callback, 500));
        }
    }

    // 【新增】全局错误处理
    window.addEventListener('error', function(event) {
        console.error('全局错误捕获:', event.error);
    });
    
    // 【新增】强制在页面加载完成后执行初始化
    window.addEventListener('load', function() {
        console.log('🔄 页面完全加载，强制执行初始化...');
        setTimeout(initializeScript, 1000);
    });
    
    // 【新增】初始化函数
    function initializeScript() {
        console.log('🔧 执行脚本初始化...');
        
        // 检查是否已经初始化
        if (window._xiaomeihuaInitialized) {
            console.log('⚠️ 脚本已初始化，跳过');
            return;
        }
        
        // 标记已初始化
        window._xiaomeihuaInitialized = true;
        
        // 执行主函数
        main().catch(err => {
            console.error('主函数执行失败:', err);
            
            // 尝试恢复UI
            try {
                if (!floatingIcon) createFloatingIcon();
                if (!controlPanel) createControlPanel();
                restorePanelState();
            } catch (e) {
                console.error('UI恢复失败:', e);
            }
        });
    }
    
    let isScriptLoaded = false;
    let currentKey = null;
    let controlPanel = null;
    let floatingIcon = null;
    let isAuthenticated = false;
    let keyStatusMonitor = null; // 卡密状态监控定时器
    let configUpdateMonitor = null; // 配置更新监控定时器
    let apiKeyUpdateMonitor = null; // API密钥更新监控定时器

    // 【核心修复】模拟真实浏览器请求头，以绕过服务器新增的防御机制
    const getApiHeaders = () => ({
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Origin': 'https://store.weixin.qq.com',
        'Referer': window.location.href,
        'X-Requested-With': 'XMLHttpRequest'
    });

    // 【新增】动态API配置系统
    const API_CONFIG = {
        // 服务器地址（从后台API配置获取）
        servers: {
            primary: 'https://xiaomeihuakefu.cn',
            backup: 'https://api.xiaomeihuakefu.cn',
            secure: 'https://secure.xiaomeihuakefu.cn'
        },
        // 当前使用的服务器
        current: 'https://xiaomeihuakefu.cn',
        // API端点
        endpoints: {
            verify: '/api/verify.php',
            status: '/api/status.php',
            heartbeat: '/api/heartbeat.php',
            key_info: '/api/key_info.php',
            config: '/api/config.php'
        },
        // API密钥（动态获取）
        api_key: null,
        // 配置版本
        version: 'v2',
        // 最后更新时间
        last_update: 0,
        // 更新间隔（5分钟）
        update_interval: 5 * 60 * 1000,

        // 初始化配置
        async init() {
            console.log('🔧 初始化API配置系统...');

            // 从本地存储加载配置
            this.loadFromStorage();

            try {
                // 获取最新配置
                await this.updateConfig();
                console.log('✅ API配置系统初始化完成');
            } catch (error) {
                console.warn('⚠️ 无法从服务器获取配置，使用本地配置:', error);
                // 确保有默认服务器可用
                if (!this.current) {
                    this.current = this.servers.primary;
                }
            }

            this.logCurrentConfig();

            // 返回初始化状态
            return {
                success: true,
                servers: this.servers,
                current: this.current
            };
        },

        // 从本地存储加载配置
        loadFromStorage() {
            try {
                const stored = GM_getValue('api_config', null);
                if (stored) {
                    const config = JSON.parse(stored);
                    if (config.servers) {
                        this.servers = { ...this.servers, ...config.servers };
                    }
                    if (config.current) {
                        this.current = config.current;
                    }
                    if (config.api_key) {
                        this.api_key = config.api_key;
                    }
                    if (config.version) {
                        this.version = config.version;
                    }
                    if (config.last_update) {
                        this.last_update = config.last_update;
                    }
                    console.log('📦 从本地存储加载API配置');
                }
            } catch (e) {
                console.warn('⚠️ 加载本地API配置失败:', e);
            }
        },

        // 保存配置到本地存储
        saveToStorage() {
            try {
                const config = {
                    servers: this.servers,
                    current: this.current,
                    api_key: this.api_key,
                    version: this.version,
                    last_update: Date.now()
                };
                GM_setValue('api_config', JSON.stringify(config));
                console.log('💾 API配置已保存到本地');
            } catch (e) {
                console.error('❌ 保存API配置失败:', e);
            }
        },

        // 更新配置
        async updateConfig() {
            console.log('🔄 更新API配置...');

            // 尝试从各个服务器获取配置
            const servers = [
                this.servers.primary,
                this.servers.backup,
                this.servers.secure,
                'https://xiaomeihuakefu.cn',
                'https://api.xiaomeihuakefu.cn'
            ];

            let lastError = null;
            for (const server of servers) {
                try {
                    console.log(`🔄 尝试从服务器获取配置: ${server}`);
                    const config = await this.fetchConfigFromServer(server);
                    if (config) {
                        this.applyConfig(config);
                        this.current = server;
                        this.saveToStorage();
                        console.log(`✅ 从服务器 ${server} 获取配置成功`);
                        return true;
                    }
                } catch (e) {
                    lastError = e;
                    console.warn(`⚠️ 从服务器 ${server} 获取配置失败:`, e);
                }
            }

            // 所有服务器都失败
            console.warn('⚠️ 所有服务器配置获取失败，使用本地配置');
            if (lastError) {
                throw new Error('无法从任何服务器获取配置: ' + lastError.message);
            }
            return false;
        },

        // 【修复】从服务器获取配置 - 使用 GM_xmlhttpRequest 并添加完整请求头
        async fetchConfigFromServer(serverUrl) {
            console.log(`📡 从服务器获取配置: ${serverUrl}`);
            const url = `${serverUrl}${this.endpoints.key_info}`;
            const requestData = {
                key: currentKey || 'config_request',
                action: 'get_config',
                timestamp: Date.now()
            };

            return new Promise((resolve, reject) => {
                GM_xmlhttpRequest({
                    method: 'POST',
                    url: url,
                    headers: getApiHeaders(),
                    data: Object.keys(requestData).map(key =>
                        `${encodeURIComponent(key)}=${encodeURIComponent(requestData[key])}`
                    ).join('&'),
                    timeout: 8000,
                    onload: (response) => {
                        if (response.status === 200) {
                            try {
                                const responseText = response.responseText;
                                if (!responseText || responseText.trim() === '') {
                                    return reject(new Error('服务器返回空响应'));
                                }
                                const data = JSON.parse(responseText);
                                if (data.success && data.api_config) {
                                    resolve(data.api_config);
                                } else {
                                    reject(new Error('配置响应格式错误'));
                                }
                            } catch (e) {
                                reject(new Error('配置解析失败: ' + e.message));
                            }
                        } else {
                            reject(new Error(`HTTP ${response.status}: ${response.statusText || '未知错误'}`));
                        }
                    },
                    onerror: () => reject(new Error('网络请求失败')),
                    ontimeout: () => reject(new Error('请求超时'))
                });
            });
        },


        // 应用配置
        applyConfig(config) {
            console.log('🔧 应用新配置:', config);

            // 更新服务器地址
            if (config.servers) {
                this.servers = { ...this.servers, ...config.servers };
            }

            // 更新API密钥
            if (config.api_key && config.api_key !== this.api_key) {
                console.log('🔑 检测到API密钥更新');
                this.api_key = config.api_key;

                // 触发API密钥更新事件
                this.onApiKeyUpdated();
            }

            // 更新版本
            if (config.version) {
                this.version = config.version;
            }

            this.last_update = Date.now();
        },

        // API密钥更新事件处理
        onApiKeyUpdated() {
            console.log('🔄 API密钥已更新，重新初始化连接...');

            // 重新验证当前卡密
            if (currentKey && isAuthenticated) {
                console.log('🔄 重新验证卡密以同步新API密钥...');
                this.revalidateCurrentKey();
            }
        },

        // 重新验证当前卡密
        async revalidateCurrentKey() {
            if (!currentKey) return;

            try {
                const response = await this.makeRequest(this.endpoints.verify, {
                    key: currentKey,
                    action: 'revalidate'
                });

                if (response.success) {
                    console.log('✅ 卡密重新验证成功');
                } else {
                    console.warn('⚠️ 卡密重新验证失败:', response.message);
                }
            } catch (e) {
                console.error('❌ 卡密重新验证异常:', e);
            }
        },

        // 获取当前API URL
        getApiUrl(endpoint) {
            return `${this.current}${this.endpoints[endpoint]}`;
        },

        // 获取API密钥
        getApiKey() {
            return this.api_key;
        },

        // 切换服务器
        switchServer() {
            const servers = [this.servers.primary, this.servers.backup, this.servers.secure];
            const currentIndex = servers.indexOf(this.current);

            // 如果当前服务器不在列表中，默认使用主服务器
            const nextIndex = (currentIndex >= 0) ? (currentIndex + 1) % servers.length : 0;
            const oldServer = this.current;
            this.current = servers[nextIndex];

            console.log(`🔄 服务器切换: ${oldServer} -> ${this.current}`);

            // 保存当前使用的服务器
            try {
                this.saveToStorage();
                console.log('✅ 已保存新的服务器配置');
            } catch (e) {
                console.warn('⚠️ 保存服务器配置失败:', e);
            }

            return this.current;
        },

        // 【修复】创建API请求 - 使用完整的请求头
        async makeRequest(endpoint, data = {}) {
            const url = this.getApiUrl(endpoint);
            const apiKey = this.getApiKey();

            return new Promise((resolve, reject) => {
                const requestData = {
                    ...data,
                    timestamp: Date.now()
                };

                // 添加API密钥认证
                const headers = getApiHeaders();

                // 如果有API密钥，添加到请求头
                if (apiKey) {
                    headers['X-API-Key'] = apiKey;
                    headers['X-API-Version'] = this.version;
                }

                console.log(`🔄 发送请求到: ${url}`, requestData);

                GM_xmlhttpRequest({
                    method: 'POST',
                    url: url,
                    headers: headers,
                    data: Object.keys(requestData).map(key =>
                        `${encodeURIComponent(key)}=${encodeURIComponent(requestData[key])}`
                    ).join('&'),
                    timeout: 15000,
                    onload: (response) => {
                        console.log(`📥 收到响应 [${response.status}]`);

                        if (response.status === 200) {
                            try {
                                // 尝试解析JSON响应
                                const responseText = response.responseText;
                                console.log('📄 响应内容长度:', responseText.length);

                                // 检查响应是否为有效JSON
                                if (!responseText || responseText.trim() === '') {
                                    console.error('❌ 响应内容为空');
                                    reject(new Error('服务器返回空响应'));
                                    return;
                                }

                                try {
                                    const data = JSON.parse(responseText);
                                    console.log('✅ JSON解析成功');
                                    resolve(data);
                                } catch (jsonError) {
                                    console.error('❌ JSON解析失败:', jsonError);
                                    console.log('📄 原始响应内容:', responseText.substring(0, 200) + '...');
                                    reject(new Error('响应格式错误，无法解析JSON'));
                                }
                            } catch (e) {
                                console.error('❌ 响应处理错误:', e);
                                reject(new Error('响应处理失败: ' + e.message));
                            }
                        } else {
                            console.error(`❌ HTTP错误状态码: ${response.status}`);
                            reject(new Error(`HTTP ${response.status}: ${response.statusText || '未知错误'}`));
                        }
                    },
                    onerror: (error) => {
                        console.error('❌ 网络请求失败:', error);
                        reject(new Error('网络连接失败，请检查您的网络连接'));
                    },
                    ontimeout: () => {
                        console.error('❌ 请求超时');
                        reject(new Error('请求超时，服务器响应时间过长'));
                    }
                });
            });
        },

        // 启动配置监控
        startConfigMonitoring() {
            if (configUpdateMonitor) {
                clearInterval(configUpdateMonitor);
            }

            console.log('🔄 启动配置监控服务...');

            configUpdateMonitor = setInterval(async () => {
                try {
                    await this.updateConfig();
                    console.log('🔄 配置监控检查完成');
                } catch (e) {
                    console.warn('⚠️ 配置监控检查失败:', e);
                }
            }, this.update_interval);
        },

        // 停止配置监控
        stopConfigMonitoring() {
            if (configUpdateMonitor) {
                console.log('🛑 停止配置监控');
                clearInterval(configUpdateMonitor);
                configUpdateMonitor = null;
            }
        },

        // 记录当前配置
        logCurrentConfig() {
            console.log('📋 当前API配置:');
            console.log('  • 主服务器:', this.servers.primary);
            console.log('  • 备用服务器:', this.servers.backup);
            console.log('  • 安全服务器:', this.servers.secure);
            console.log('  • 当前服务器:', this.current);
            console.log('  • API密钥:', this.api_key ? this.api_key.substring(0, 8) + '...' : '未设置');
            console.log('  • 版本:', this.version);
            console.log('  • 最后更新:', new Date(this.last_update).toLocaleString());
        }
    };

    // 【新增】API密钥同步管理器
    const ApiKeyManager = {
        // 检查API密钥是否需要更新
        async checkForUpdates() {
            if (!currentKey || !isAuthenticated) return false;

            try {
                console.log('🔍 检查API密钥更新...');

                const response = await API_CONFIG.makeRequest('key_info', {
                    key: currentKey,
                    current_api_key: API_CONFIG.getApiKey(),
                    action: 'check_updates'
                });

                if (response.success) {
                    // 检查API密钥是否有更新
                    if (response.api_key_updated) {
                        console.log('🔑 检测到API密钥更新');
                        API_CONFIG.api_key = response.api_key;
                        API_CONFIG.saveToStorage();
                        return true;
                    }

                    // 检查脚本是否有更新
                    if (response.script_updated && response.script) {
                        console.log('📝 检测到脚本更新');
                        this.updateScript(response.script);
                    }

                    // 更新API配置
                    if (response.api_config) {
                        API_CONFIG.applyConfig(response.api_config);
                        API_CONFIG.saveToStorage();
                    }
                }

                return false;
            } catch (e) {
                console.error('❌ API密钥更新检查失败:', e);
                return false;
            }
        },

        // 更新脚本
        updateScript(newScript) {
            console.log('🔄 更新脚本代码...');

            try {
                // 备份当前脚本状态
                const currentState = {
                    isAuthenticated,
                    currentKey,
                    isScriptLoaded
                };

                // 执行新脚本
                eval(newScript);

                // 恢复状态
                isAuthenticated = currentState.isAuthenticated;
                currentKey = currentState.currentKey;
                isScriptLoaded = true;

                console.log('✅ 脚本更新成功');

                // 显示更新通知
                this.showUpdateNotification('脚本已更新到最新版本');

            } catch (e) {
                console.error('❌ 脚本更新失败:', e);
                this.showUpdateNotification('脚本更新失败: ' + e.message, 'error');
            }
        },

        // 显示更新通知
        showUpdateNotification(message, type = 'success') {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? 'linear-gradient(135deg, #28a745, #20c997)' : 'linear-gradient(135deg, #dc3545, #c82333)'};
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                box-shadow: 0 5px 15px rgba(0,0,0,0.3);
                z-index: 100000;
                font-family: sans-serif;
                font-size: 14px;
                font-weight: bold;
                max-width: 300px;
                animation: slideInRight 0.3s ease-out;
            `;

            notification.innerHTML = `
                <div style="display: flex; align-items: center;">
                    <span style="margin-right: 10px;">${type === 'success' ? '✅' : '❌'}</span>
                    <span>${message}</span>
                </div>
            `;

            // 添加动画样式
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideInRight {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }
                @keyframes slideOutRight {
                    from {
                        transform: translateX(0);
                        opacity: 1;
                    }
                    to {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);

            document.body.appendChild(notification);

            // 3秒后自动消失
            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease-in';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                    if (style.parentNode) {
                        style.parentNode.removeChild(style);
                    }
                }, 300);
            }, 3000);
        },

        // 启动API密钥监控
        startMonitoring() {
            if (apiKeyUpdateMonitor) {
                clearInterval(apiKeyUpdateMonitor);
            }

            console.log('🔄 启动API密钥监控服务...');

            apiKeyUpdateMonitor = setInterval(async () => {
                try {
                    const updated = await this.checkForUpdates();
                    if (updated) {
                        console.log('🔑 API密钥已同步更新');
                    }
                } catch (e) {
                    console.warn('⚠️ API密钥监控检查失败:', e);
                }
            }, API_CONFIG.update_interval);
        },

        // 停止API密钥监控
        stopMonitoring() {
            if (apiKeyUpdateMonitor) {
                console.log('🛑 停止API密钥监控');
                clearInterval(apiKeyUpdateMonitor);
                apiKeyUpdateMonitor = null;
            }
        }
    };

    // 【修复】全局API保存，确保动态脚本可以访问
    window._tampermonkeyAPIs = {
        GM_openInTab: GM_openInTab,
        GM_setValue: GM_setValue,
        GM_getValue: GM_getValue,
        GM_xmlhttpRequest: GM_xmlhttpRequest,
        GM_addStyle: GM_addStyle,
        windowOpen: window.open
    };

    // 【修复】增强的权限传递函数
    function setupAPIAccess() {
        console.log('🔧 设置API访问权限...');

        // 方法1: 通过unsafeWindow传递
        if (typeof unsafeWindow !== 'undefined') {
            unsafeWindow.GM_openInTab = GM_openInTab;
            unsafeWindow.GM_setValue = GM_setValue;
            unsafeWindow.GM_getValue = GM_getValue;
            unsafeWindow.GM_xmlhttpRequest = GM_xmlhttpRequest;
            unsafeWindow.GM_addStyle = GM_addStyle;
            console.log('✅ unsafeWindow API传递完成');
        }

        // 方法2: 直接在window对象上设置
        window.GM_openInTab = GM_openInTab;
        window.GM_setValue = GM_setValue;
        window.GM_getValue = GM_getValue;
        window.GM_xmlhttpRequest = GM_xmlhttpRequest;
        window.GM_addStyle = GM_addStyle;
        console.log('✅ window API设置完成');

        // 方法3: 确保window.open可用
        if (typeof window.open === 'undefined' || !window.open) {
            window.open = function(url, name, specs) {
                console.log('🔗 使用GM_openInTab打开链接:', url);
                if (typeof GM_openInTab !== 'undefined') {
                    return GM_openInTab(url, { active: true });
                }
                console.error('❌ GM_openInTab不可用');
                return null;
            };
            console.log('✅ window.open备用实现已设置');
        }

        // 方法4: 创建全局辅助函数
        window.openNewTab = function(url) {
            console.log('🚀 尝试打开新标签页:', url);

            // 优先使用GM_openInTab
            if (typeof GM_openInTab !== 'undefined') {
                console.log('使用GM_openInTab');
                return GM_openInTab(url, { active: true });
            }

            // 备用使用window.open
            if (typeof window.open !== 'undefined') {
                console.log('使用window.open');
                return window.open(url, '_blank');
            }

            // 最后备用：通过location.href跳转
            console.log('使用location.href跳转');
            window.location.href = url;
            return null;
        };

        console.log('🎯 API访问权限设置完成');
    }

    // 【修复】API可用性测试函数
    function testAPIAvailability() {
        console.log('🧪 测试API可用性...');

        const results = {
            GM_openInTab: typeof GM_openInTab !== 'undefined',
            window_open: typeof window.open !== 'undefined',
            unsafeWindow: typeof unsafeWindow !== 'undefined',
            openNewTab: typeof window.openNewTab !== 'undefined'
        };

        console.log('API可用性测试结果:', results);
        return results;
    }

    // 【修改】卡密状态检查函数 - 使用API配置系统
    async function checkKeyStatus(key, silent = false) {
        if (!key || !isAuthenticated) {
            return;
        }

        if (!silent) {
            console.log('🔍 检查卡密状态:', key.substring(0, 8) + '...');
        }

        try {
            const response = await API_CONFIG.makeRequest('verify', {
                key: key,
                check_status: 1
            });

            if (!silent) {
                console.log('📨 卡密状态检查响应:', response);
            }

            handleKeyStatusResponse(response);
        } catch (error) {
            if (!silent) {
                console.error('❌ 卡密状态检查失败:', error);

                // 尝试切换服务器重试
                API_CONFIG.switchServer();
                console.log('🔄 切换服务器后重试卡密状态检查...');

                try {
                    const retryResponse = await API_CONFIG.makeRequest('verify', {
                        key: key,
                        check_status: 1
                    });
                    handleKeyStatusResponse(retryResponse);
                } catch (retryError) {
                    console.error('❌ 重试后仍然失败:', retryError);
                }
            }
        }
    }

    // 【新增】处理卡密状态响应
    function handleKeyStatusResponse(data) {
        console.log('📋 卡密状态数据:', data);

        if (!data.success) {
            // 卡密状态异常，需要处理
            let message = '';
            let title = '⚠️ 卡密状态异常';

            if (data.error_code) {
                switch (data.error_code) {
                    case 'CONCURRENT_LOGIN_LIMIT':
                        message = '老板您好！\n\n该卡密已经在其他电脑登录，不能同时登录';
                        title = '🚫 同时登录限制';
                        break;
                    case 'MULTI_STORE_LOGIN_LIMIT':
                        const maxDevices = data.max_devices || '未知';
                        message = `老板您好！\n\n该多店卡密已达到最大同时登录数量限制（${maxDevices}台设备），请先退出其他设备后再登录`;
                        title = '🚫 多店登录限制';
                        break;
                    case 'KEY_DISABLED':
                        message = '您的卡密已经被禁用，如有疑问请联系代理商';
                        title = '🚫 卡密已禁用';
                        break;
                    case 'KEY_DELETED':
                        message = '您的卡密不存在已被删除，如有疑问请联系代理商';
                        title = '❌ 卡密已删除';
                        break;
                    case 'KEY_EXPIRED':
                        message = '您的卡密已经过期，如需继续使用请联系代理商';
                        title = '⏰ 卡密已过期';
                        break;
                    default:
                        message = data.message || '卡密验证失败，请重新验证';
                        break;
                }
            } else {
                message = data.message || '卡密验证失败，请重新验证';
            }

            console.warn('⚠️ 卡密状态异常:', message);

            // 显示状态异常弹窗
            showKeyStatusAlert(title, message);

            // 清除本地数据并退出
            handleKeyStatusExit();
        } else {
            // 卡密状态正常，更新功能信息
            if (data.function_type) {
                GM_setValue('function_type', data.function_type);
                GM_setValue('has_customer_service', data.has_customer_service);
                GM_setValue('has_product_listing', data.has_product_listing);

                // 更新控制面板显示
                updateFunctionTypeDisplay();
            }
        }
    }

    // 【新增】显示卡密状态异常弹窗
    function showKeyStatusAlert(title, message) {
        // 移除可能存在的旧弹窗
        const existingAlert = document.getElementById('key-status-alert');
        if (existingAlert) {
            existingAlert.remove();
        }

        const alert = document.createElement('div');
        alert.id = 'key-status-alert';
        alert.innerHTML = `
            <div class="alert-overlay"></div>
            <div class="alert-content">
                <div class="alert-header">
                    ${title}
                </div>
                <div class="alert-body">
                    <div class="alert-message">
                        ${message}
                    </div>
                    <div class="alert-buttons">
                        <button id="alert-confirm-btn" class="alert-btn alert-btn-primary">确定</button>
                    </div>
                </div>
            </div>
        `;

        // 添加样式
        GM_addStyle(`
            #key-status-alert {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 999999;
                font-family: sans-serif;
            }

            #key-status-alert .alert-overlay {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.6);
                backdrop-filter: blur(5px);
            }

            #key-status-alert .alert-content {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                border-radius: 15px;
                box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
                overflow: hidden;
                min-width: 400px;
                max-width: 90vw;
                animation: alertFadeIn 0.3s ease-out;
            }

            #key-status-alert .alert-header {
                background: linear-gradient(135deg, #dc3545, #c82333);
                color: white;
                padding: 20px;
                font-size: 18px;
                font-weight: bold;
                text-align: center;
            }

            #key-status-alert .alert-body {
                padding: 30px;
            }

            #key-status-alert .alert-message {
                font-size: 16px;
                line-height: 1.6;
                color: #333;
                text-align: center;
                margin-bottom: 25px;
            }

            #key-status-alert .alert-buttons {
                text-align: center;
            }

            #key-status-alert .alert-btn {
                padding: 12px 30px;
                border: none;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s ease;
            }

            #key-status-alert .alert-btn-primary {
                background: linear-gradient(135deg, #dc3545, #c82333);
                color: white;
            }

            #key-status-alert .alert-btn-primary:hover {
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
            }

            @keyframes alertFadeIn {
                from {
                    opacity: 0;
                    transform: translate(-50%, -50%) scale(0.9);
                }
                to {
                    opacity: 1;
                    transform: translate(-50%, -50%) scale(1);
                }
            }
        `);

        document.body.appendChild(alert);

        // 绑定确定按钮事件
        document.getElementById('alert-confirm-btn').addEventListener('click', () => {
            alert.remove();
        });

        // 点击遮罩层关闭
        alert.querySelector('.alert-overlay').addEventListener('click', () => {
            alert.remove();
        });
    }

    // 【新增】处理卡密状态异常退出
    function handleKeyStatusExit() {
        console.log('🚪 卡密状态异常，执行退出流程...');

        // 停止状态监控
        if (keyStatusMonitor) {
            clearInterval(keyStatusMonitor);
            keyStatusMonitor = null;
        }

        // 清除本地存储的数据
        GM_deleteValue('saved_license_key_xiaomeihua');
        GM_deleteValue('function_type');
        GM_deleteValue('has_customer_service');
        GM_deleteValue('has_product_listing');
        GM_deleteValue('shop_name');
        GM_deleteValue('panel_state_xiaomeihua');

        // 重置状态变量
        isAuthenticated = false;
        currentKey = null;
        isScriptLoaded = false;

        // 移除控制面板和浮动图标
        if (controlPanel) {
            controlPanel.remove();
            controlPanel = null;
        }
        if (floatingIcon) {
            floatingIcon.remove();
            floatingIcon = null;
        }

        // 延迟后重新加载页面或显示验证界面
        setTimeout(() => {
            location.reload();
        }, 3000);
    }

    // 【新增】启动卡密状态监控
    function startKeyStatusMonitor(key) {
        if (!key) return;

        console.log('🔄 启动卡密状态监控...');

        // 停止之前的监控
        if (keyStatusMonitor) {
            clearInterval(keyStatusMonitor);
        }

        // 每5分钟检查一次卡密状态
        keyStatusMonitor = setInterval(() => {
            checkKeyStatus(key, true); // 静默检查
        }, 5 * 60 * 1000); // 5分钟

        // 立即进行一次检查（非静默）
        setTimeout(() => {
            checkKeyStatus(key, false);
        }, 10000); // 10秒后进行首次检查
    }

    // 【新增】停止卡密状态监控
    function stopKeyStatusMonitor() {
        if (keyStatusMonitor) {
            console.log('🛑 停止卡密状态监控');
            clearInterval(keyStatusMonitor);
            keyStatusMonitor = null;
        }
    }

    // 【修复】添加缺失的showMessage函数
    function showMessage(element, message, type) {
        if (!element) {
            console.error('showMessage: 元素不存在');
            return;
        }

        // 清除之前的类名
        element.className = '';

        // 根据类型添加相应的样式类
        switch(type) {
            case 'success':
                element.className = 'success';
                break;
            case 'error':
                element.className = 'error';
                break;
            case 'info':
                element.className = 'info';
                break;
            default:
                element.className = '';
        }

        // 设置消息内容
        element.innerHTML = message;
        console.log(`showMessage [${type}]: ${message}`);
    }

    // 创建UI
    function createUI() {
        if (document.getElementById('auth-panel')) return;

        const panel = document.createElement('div');
        panel.id = 'auth-panel';
        panel.innerHTML = `
            <div class="auth-header">
                小梅花AI客服助手 v1.0.4 (调试版)
                <button id="close-panel-btn" style="float: right; background: none; border: none; color: white; font-size: 18px; cursor: pointer; padding: 0; width: 20px; height: 20px;">×</button>
            </div>
            <div class="auth-body">
                <p>请输入卡密以解锁完整功能:</p>
                <input type="text" id="license-key-input" placeholder="在此输入卡密" autocomplete="off">
                <button id="verify-key-btn">验证</button>
                <div id="auth-message"></div>
            </div>
        `;
        document.body.appendChild(panel);

        GM_addStyle(`
            #auth-panel { position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 99999;
                         background-image: url('https://img.zcool.cn/community/01e3d85e71636ca801216518168b81.jpg@1280w_1l_2o_100sh.jpg');
                         background-size: cover; background-position: center;
                         border: 1px solid #ddd; border-radius: 15px;
                         box-shadow: 0 10px 30px rgba(0,0,0,0.5); width: 380px; font-family: sans-serif; }
            #auth-panel::before { content: ""; position: absolute; top: 0; left: 0; right: 0; bottom: 0;
                                background-color: rgba(255,255,255,0.85); border-radius: 15px; z-index: -1; }
            #auth-panel .auth-header { background: linear-gradient(135deg, #667eea, #764ba2); color: white;
                                      padding: 20px; border-top-left-radius: 15px; border-top-right-radius: 15px;
                                      font-size: 18px; text-align: center; font-weight: bold; position: relative; }
            #auth-panel .auth-body { padding: 25px; position: relative; z-index: 1; }
            #auth-panel p { margin-bottom: 15px; color: #333; font-size: 14px; font-weight: bold; }
            #auth-panel input { width: 100%; padding: 12px; box-sizing: border-box; margin-bottom: 15px;
                               border-radius: 8px; border: 2px solid #e1e5e9; font-size: 14px; }
            #auth-panel input:focus { outline: none; border-color: #667eea; }
            #auth-panel button { width: 100%; padding: 12px; background: linear-gradient(135deg, #667eea, #764ba2);
                                color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 16px;
                                font-weight: bold; transition: all 0.3s ease; }
            #auth-panel button:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(102,126,234,0.4); }
            #auth-message { margin-top: 15px; text-align: center; font-weight: bold; padding: 8px; border-radius: 5px; }
            .success { color: #28a745; background-color: rgba(40, 167, 69, 0.1); }
            .error { color: #dc3545; background-color: rgba(220, 53, 69, 0.1); }
            .info { color: #007bff; background-color: rgba(0, 123, 255, 0.1); }

            /* 成功提示中的特殊样式 */
            .success span[style*="color: #ff0000"] {
                color: #ff0000 !important;
                font-weight: bold !important;
            }

            /* 控制面板样式 */
            #control-panel { position: fixed; top: 20px; left: 20px; z-index: 99998; background: rgba(255,255,255,0.95);
                           border: 1px solid #ddd; border-radius: 10px; box-shadow: 0 5px 20px rgba(0,0,0,0.2);
                           width: 320px; font-family: sans-serif; backdrop-filter: blur(10px);
                           transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1); }
            #control-panel .panel-header { background: linear-gradient(135deg, #28a745, #20c997); color: white;
                                         padding: 15px; border-top-left-radius: 10px; border-top-right-radius: 10px;
                                         font-size: 16px; font-weight: bold; position: relative; }
            #control-panel .panel-body { padding: 20px; }
            #control-panel .status-item { margin-bottom: 10px; padding: 8px; background: #f8f9fa; border-radius: 5px; }
            #control-panel .status-label { font-weight: bold; color: #495057; margin-bottom: 4px; }
            #control-panel .status-value { color: #28a745; word-wrap: break-word; line-height: 1.4; }

            /* 浮动图标样式 */
            #floating-icon {
                position: fixed;
                top: 20px;
                left: 20px;
                width: 60px;
                height: 60px;
                border-radius: 50%;
                cursor: pointer;
                z-index: 99999;
                display: none;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                background: linear-gradient(135deg, #ff6b9d, #c44569, #f8b500, #e55039, #3c6382);
                background-size: 300% 300%;
                animation: gradientShift 3s ease infinite, float 2s ease-in-out infinite;
                box-shadow: 0 4px 15px rgba(255, 107, 157, 0.4);
            }

            #floating-icon:hover {
                transform: scale(1.1);
                box-shadow: 0 8px 25px rgba(255, 107, 157, 0.6);
            }

            #floating-icon::before {
                content: '';
                position: absolute;
                top: -10px;
                left: -10px;
                right: -10px;
                bottom: -10px;
                border-radius: 50%;
                background: radial-gradient(circle, rgba(255, 107, 157, 0.3) 0%, transparent 70%);
                animation: pulse 2s ease-in-out infinite;
                z-index: -1;
            }

            #floating-icon::after {
                content: '';
                position: absolute;
                top: -20px;
                left: -20px;
                right: -20px;
                bottom: -20px;
                border-radius: 50%;
                background: radial-gradient(circle, rgba(255, 107, 157, 0.1) 0%, transparent 70%);
                animation: pulse 2s ease-in-out infinite 0.5s;
                z-index: -2;
            }

            /* 梅花图标 */
            .plum-flower {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 30px;
                height: 30px;
                color: white;
                font-size: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                animation: rotate 4s linear infinite;
            }

            /* 动画定义 */
            @keyframes gradientShift {
                0% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
                100% { background-position: 0% 50%; }
            }

            @keyframes float {
                0%, 100% { transform: translateY(0px); }
                50% { transform: translateY(-5px); }
            }

            @keyframes pulse {
                0%, 100% {
                    opacity: 0.8;
                    transform: scale(1);
                }
                50% {
                    opacity: 0.4;
                    transform: scale(1.2);
                }
            }

            @keyframes rotate {
                0% { transform: translate(-50%, -50%) rotate(0deg); }
                100% { transform: translate(-50%, -50%) rotate(360deg); }
            }

            /* 面板隐藏时的样式 */
            #control-panel.minimized {
                opacity: 0;
                transform: scale(0.8) translateX(100px);
                pointer-events: none;
            }

            /* 面板显示动画 */
            #control-panel.show {
                opacity: 1;
                transform: scale(1) translateX(0);
                pointer-events: all;
            }

            /* 成功弹窗样式 */
            #success-popup {
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                z-index: 100000;
                background: rgba(255, 255, 255, 0.98);
                border: 2px solid #28a745;
                border-radius: 20px;
                box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
                width: 550px;
                max-width: 90vw;
                font-family: sans-serif;
                backdrop-filter: blur(15px);
                animation: popupFadeIn 0.5s ease-out;
            }

            #success-popup .popup-header {
                background: linear-gradient(135deg, #28a745, #20c997);
                color: white;
                padding: 20px;
                border-top-left-radius: 18px;
                border-top-right-radius: 18px;
                text-align: center;
                font-size: 20px;
                font-weight: bold;
            }

            #success-popup .popup-body {
                padding: 30px;
                text-align: center;
            }

            #success-popup .function-info {
                font-size: 24px;
                font-weight: bold;
                margin: 20px 0;
                padding: 15px;
                border-radius: 12px;
                background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
                border: 2px solid #e1bee7;
            }

            #success-popup .success-text {
                font-size: 16px;
                color: #155724;
                line-height: 1.6;
                margin: 15px 0;
            }

            #success-popup .countdown {
                font-size: 14px;
                color: #6c757d;
                margin-top: 20px;
            }

            @keyframes popupFadeIn {
                from {
                    opacity: 0;
                    transform: translate(-50%, -50%) scale(0.8);
                }
                to {
                    opacity: 1;
                    transform: translate(-50%, -50%) scale(1);
                }
            }

            @keyframes popupFadeOut {
                from {
                    opacity: 1;
                    transform: translate(-50%, -50%) scale(1);
                }
                to {
                    opacity: 0;
                    transform: translate(-50%, -50%) scale(0.8);
                }
            }
        `);

        document.getElementById('verify-key-btn').addEventListener('click', verifyKey);
        document.getElementById('license-key-input').addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                verifyKey();
            }
        });
        document.getElementById('close-panel-btn').addEventListener('click', function() {
            document.getElementById('auth-panel').remove();
        });

        // 自动聚焦到输入框
        setTimeout(() => {
            const inputElement = document.getElementById('license-key-input');
            if (inputElement) {
                inputElement.focus();
            }
        }, 300);
    }

    // 创建浮动图标
    function createFloatingIcon() {
        // 如果已存在，先移除
        if (floatingIcon) {
            if (floatingIcon.parentNode) {
                floatingIcon.parentNode.removeChild(floatingIcon);
            }
        }

        // 检查是否已有同ID元素
        const existingIcon = document.getElementById('floating-icon');
        if (existingIcon) {
            existingIcon.remove();
        }

        floatingIcon = document.createElement('div');
        floatingIcon.id = 'floating-icon';
        
        // 设置更高的z-index确保在最上层
        floatingIcon.style.cssText = `
            position: fixed !important;
            top: 20px;
            left: 20px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            cursor: pointer;
            z-index: 2147483647 !important; /* 最高可能的z-index值 */
            display: block !important;
            opacity: 1 !important;
            transform: scale(1);
            background: linear-gradient(135deg, #ff6b9d, #c44569, #f8b500, #e55039, #3c6382);
            background-size: 300% 300%;
            animation: gradientShift 3s ease infinite, float 2s ease-in-out infinite;
            box-shadow: 0 4px 15px rgba(255, 107, 157, 0.4);
            visibility: visible !important;
            pointer-events: auto !important;
        `;

        floatingIcon.innerHTML = `
            <div class="plum-flower" style="
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 30px;
                height: 30px;
                color: white;
                font-size: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
            ">🌸</div>
        `;

        floatingIcon.addEventListener('click', function() {
            toggleControlPanel();
        });

        // 添加到body
        document.body.appendChild(floatingIcon);
        console.log('浮动图标已创建并添加到页面，显示状态已设置为可见');
        
        // 确保图标在最上层，延迟后再次添加
        setTimeout(() => {
            if (floatingIcon && document.body.contains(floatingIcon)) {
                document.body.appendChild(floatingIcon); // 重新添加到body末尾确保最上层
                console.log('浮动图标已重新添加到DOM以确保在最上层');
            }
        }, 1000);
        
        // 添加微信小店特定的样式修复
        GM_addStyle(`
            #floating-icon {
                position: fixed !important;
                z-index: 2147483647 !important;
                display: block !important;
                opacity: 1 !important;
                visibility: visible !important;
                pointer-events: auto !important;
            }
            
            /* 确保我们的样式不被微信覆盖 */
            body > #floating-icon {
                display: block !important;
                opacity: 1 !important;
            }
            
            /* 修复可能的微信样式冲突 */
            .wx-root #floating-icon,
            .shop-container #floating-icon,
            .kf-container #floating-icon {
                display: block !important;
                opacity: 1 !important;
                z-index: 2147483647 !important;
            }
            
            @keyframes gradientShift {
                0% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
                100% { background-position: 0% 50%; }
            }

            @keyframes float {
                0%, 100% { transform: translateY(0px); }
                50% { transform: translateY(-5px); }
            }
        `);
        
        return floatingIcon;
    }

    // 创建控制面板
    function createControlPanel() {
        if (document.getElementById('control-panel')) return;

        controlPanel = document.createElement('div');
        controlPanel.id = 'control-panel';
        controlPanel.innerHTML = `
            <div class="panel-header">
                🌸 脚本控制面板
                <button id="minimize-panel-btn" style="float: right; background: none; border: none; color: white; font-size: 16px; cursor: pointer; padding: 0; width: 18px; height: 18px;">−</button>
            </div>
            <div class="panel-body">
                <div class="status-item">
                    <div class="status-label">🟢 状态:</div>
                    <div class="status-value">已激活</div>
                </div>
                <div class="status-item">
                    <div class="status-label">🏪 店铺:</div>
                    <div class="status-value" id="shop-name-value" style="color: #e74c3c; font-weight: bold;">${getShopInfo()}</div>
                </div>
                <div class="status-item">
                    <div class="status-label">🔑 卡密:</div>
                    <div class="status-value" style="font-family: monospace; font-size: 11px; word-break: break-all;">${currentKey || '未知'}</div>
                </div>
                <div class="status-item">
                    <div class="status-label">⭐ 功能类型:</div>
                    <div class="status-value" id="function-type-value">检测中...</div>
                </div>
                <div class="status-item">
                    <div class="status-label">📦 版本:</div>
                    <div class="status-value">v1.0.4</div>
                </div>
                <div class="status-item">
                    <div class="status-label">⏰ 运行时间:</div>
                    <div class="status-value" id="runtime-counter">0秒</div>
                </div>
                <button id="logout-btn" style="width: 100%; padding: 10px; background: linear-gradient(135deg, #dc3545, #c82333); color: white; border: none; border-radius: 5px; cursor: pointer; margin-top: 15px; font-weight: bold; transition: all 0.3s ease;">🚪 退出登录</button>
            </div>
        `;
        document.body.appendChild(controlPanel);

        // 启动运行时间计时器
        startRuntimeCounter();

        document.getElementById('minimize-panel-btn').addEventListener('click', function() {
            minimizeControlPanel();
        });

        document.getElementById('logout-btn').addEventListener('click', function() {
            if (confirm('🤔 确定要退出登录吗？\n\n退出后需要重新输入卡密才能使用功能。')) {
                // 【新增】停止卡密状态监控
                stopKeyStatusMonitor();

                GM_deleteValue('saved_license_key_xiaomeihua');
                GM_deleteValue('panel_state_xiaomeihua');
                GM_deleteValue('function_type');
                GM_deleteValue('has_customer_service');
                GM_deleteValue('has_product_listing');
                GM_deleteValue('shop_name');

                if (controlPanel) controlPanel.remove();
                if (floatingIcon) floatingIcon.remove();
                location.reload();
            }
        });

        // 添加悬停效果
        const logoutBtn = document.getElementById('logout-btn');
        logoutBtn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 5px 15px rgba(220, 53, 69, 0.4)';
        });
        logoutBtn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = 'none';
        });

        // 更新功能类型显示
        updateFunctionTypeDisplay();
    }

    // 最小化控制面板
    function minimizeControlPanel() {
        console.log('最小化控制面板...');

        if (controlPanel) {
            controlPanel.classList.add('minimized');
            setTimeout(() => {
                controlPanel.style.display = 'none';
            }, 300);
        }

        // 确保浮动图标存在并显示
        if (!floatingIcon) {
            console.log('浮动图标不存在，重新创建...');
            createFloatingIcon();
        } else {
            // 强制设置浮动图标可见
            floatingIcon.style.display = 'block';
            floatingIcon.style.opacity = '1';
            floatingIcon.style.transform = 'scale(1)';
            console.log('浮动图标显示状态已设置为可见');
        }

        // 保存面板状态
        GM_setValue('panel_state_xiaomeihua', 'minimized');
    }

    // 显示控制面板
    function showControlPanel() {
        if (controlPanel) {
            controlPanel.style.display = 'block';
            controlPanel.classList.remove('minimized');
            controlPanel.classList.add('show');
        }

        // 隐藏浮动图标
        if (floatingIcon) {
            floatingIcon.style.opacity = '0';
            floatingIcon.style.transform = 'scale(0.8)';
            setTimeout(() => {
                floatingIcon.style.display = 'none';
            }, 300);
        }

        // 保存面板状态
        GM_setValue('panel_state_xiaomeihua', 'expanded');
    }

    // 切换控制面板显示/隐藏
    function toggleControlPanel() {
        if (controlPanel.style.display === 'none' || controlPanel.classList.contains('minimized')) {
            showControlPanel();
        } else {
            minimizeControlPanel();
        }
    }

    // =================================================================================
    // 【优化】恢复面板状态 - 强制显示缩略图
    // 根据用户要求，在页面刷新后，无论之前的状态如何，都默认显示缩略图图标。
    // 这解决了"刷新页面后图标消失"的问题。
    function restorePanelState() {
        console.log('🔄 初始化UI状态：强制显示缩略图...');

        // 1. 确保核心UI元素已创建
        // 如果在自动验证流程中尚未创建，则在这里创建它们。
        if (!controlPanel) {
            console.log('面板不存在，创建控制面板...');
            createControlPanel();
        }
        if (!floatingIcon) {
            console.log('图标不存在，创建浮动图标...');
            createFloatingIcon();
        }

        // 2. 延迟执行以确保页面渲染稳定
        // 使用一个短延迟来避免与其他页面加载脚本的冲突。
        setTimeout(() => {
            console.log('执行UI状态设置 -> 最小化面板');
            // 3. 调用 minimizeControlPanel 函数
            // 这个函数会隐藏主面板并【显示】浮动图标，同时保存状态。
            minimizeControlPanel();
            console.log('✅ UI状态已成功设置为缩略图模式。');
            
            // 4. 确保浮动图标可见
            if (floatingIcon) {
                floatingIcon.style.display = 'block';
                floatingIcon.style.opacity = '1';
                floatingIcon.style.transform = 'scale(1)';
                console.log('✅ 浮动图标显示状态已强制设置为可见。');
            }
        }, 500); // 增加延迟时间，确保DOM完全加载
    }
    // =================================================================================

    // 启动运行时间计时器
    function startRuntimeCounter() {
        const startTime = Date.now();

        setInterval(() => {
            const runtimeElement = document.getElementById('runtime-counter');
            if (runtimeElement) {
                const elapsed = Math.floor((Date.now() - startTime) / 1000);
                const minutes = Math.floor(elapsed / 60);
                const seconds = elapsed % 60;

                if (minutes > 0) {
                    runtimeElement.textContent = `${minutes}分${seconds}秒`;
                } else {
                    runtimeElement.textContent = `${seconds}秒`;
                }
            }
        }, 1000);
    }

    // 更新功能类型显示
    function updateFunctionTypeDisplay() {
        const functionTypeElem = document.getElementById('function-type-value');
        if (!functionTypeElem) return;

        const functionType = GM_getValue('function_type', '');
        const hasCustomerService = GM_getValue('has_customer_service', true);
        const hasProductListing = GM_getValue('has_product_listing', false);

        let displayText = '';
        let displayColor = '';

        if (hasCustomerService && hasProductListing) {
            displayText = '小梅花AI全功能版本';
            displayColor = '#ff6b9d';
        } else if (hasProductListing && !hasCustomerService) {
            displayText = '小梅花AI上架产品功能版本';
            displayColor = '#4ecdc4';
        } else if (hasCustomerService && !hasProductListing) {
            displayText = '小梅花AI客服功能版本';
            displayColor = '#667eea';
        } else {
            displayText = '❌ 无功能';
            displayColor = '#666';
        }

        functionTypeElem.textContent = displayText;
        functionTypeElem.style.color = displayColor;
        functionTypeElem.style.fontWeight = 'bold';
    }

    // 【优化】获取店铺信息 - 精准识别店铺名称
    function getShopInfo() {
        try {
            console.log('🏪 开始获取店铺信息...');

            // 方法1: 从页面标题获取店铺名称（最准确）
            const title = document.title;
            console.log('页面标题:', title);

            // 匹配各种标题格式
            const titlePatterns = [
                /^(.+?)\s*[-|·|—|丨]\s*微信小店/,           // "小梅花旗舰店 - 微信小店"
                /^(.+?)\s*[-|·|—|丨]\s*客服/,               // "小梅花旗舰店 - 客服"
                /^(.+?)\s*[-|·|—|丨]\s*小店/,               // "小梅花旗舰店 - 小店"
                /^(.+?)\s*微信小店/,                       // "小梅花旗舰店微信小店"
                /^(.+?)\s*客服$/,                          // "小梅花旗舰店客服"
                /^(.+?)\s*小店$/                           // "小梅花旗舰店小店"
            ];

            for (const pattern of titlePatterns) {
                const match = title.match(pattern);
                if (match && match[1]) {
                    const shopName = match[1].trim();
                    if (shopName && shopName.length > 0 && shopName.length < 50) {
                        console.log('✅ 从标题获取店铺名称:', shopName);
                        return shopName;
                    }
                }
            }

            // 方法2: 从页面元素获取店铺名称
            console.log('📋 尝试从页面元素获取店铺名称...');
            const shopNameSelectors = [
                // 微信小店常见的店铺名称选择器
                '.shop-info .shop-name',
                '.store-header .store-name',
                '.shop-title',
                '.store-title',
                '[data-testid="shop-name"]',
                '[class*="shop-name"]',
                '[class*="store-name"]',
                '.header-title',
                '.page-title',
                // 通用选择器
                'h1', 'h2', '.title'
            ];

            for (const selector of shopNameSelectors) {
                const elements = document.querySelectorAll(selector);
                for (const element of elements) {
                    if (element && element.textContent) {
                        const text = element.textContent.trim();
                        console.log(`检查元素 ${selector}:`, text);

                        // 过滤条件更精确
                        if (text &&
                            text.length > 1 &&
                            text.length < 50 &&
                            !text.includes('客服') &&
                            !text.includes('微信') &&
                            !text.includes('设置') &&
                            !text.includes('管理') &&
                            !text.includes('页面') &&
                            !text.match(/^[0-9]+$/) && // 不是纯数字
                            !text.includes('kf') &&
                            !text.includes('shop')) {
                            console.log('✅ 从页面元素获取店铺名称:', text);
                            return text;
                        }
                    }
                }
            }

            // 方法3: 从URL路径获取店铺信息
            console.log('🔗 尝试从URL获取店铺信息...');
            const url = window.location.href;
            console.log('当前URL:', url);

            // 匹配URL中的店铺ID或名称
            const urlPatterns = [
                /\/shop\/([^\/\?]+)/,                      // /shop/shopname
                /shopId=([^&]+)/,                          // ?shopId=xxx
                /shop_id=([^&]+)/,                         // ?shop_id=xxx
                /store\/([^\/\?]+)/,                       // /store/storename
                /storeId=([^&]+)/                          // ?storeId=xxx
            ];

            for (const pattern of urlPatterns) {
                const match = url.match(pattern);
                if (match && match[1]) {
                    const identifier = decodeURIComponent(match[1]);
                    console.log('从URL获取标识符:', identifier);

                    // 如果是有意义的名称而不是ID
                    if (identifier &&
                        !identifier.match(/^[0-9a-f]+$/i) && // 不是纯16进制ID
                        !identifier.match(/^[0-9]+$/) &&     // 不是纯数字ID
                        identifier.length > 2) {
                        console.log('✅ 从URL获取店铺名称:', identifier);
                        return identifier;
                    } else {
                        console.log('✅ 从URL获取店铺ID:', identifier);
                        return `店铺${identifier}`;
                    }
                }
            }

            // 方法4: 从localStorage或sessionStorage获取
            console.log('💾 尝试从本地存储获取店铺信息...');
            const storageKeys = ['shopName', 'storeName', 'shop_name', 'store_name'];
            for (const key of storageKeys) {
                const stored = localStorage.getItem(key) || sessionStorage.getItem(key);
                if (stored && stored.trim()) {
                    console.log(`✅ 从本地存储 ${key} 获取店铺名称:`, stored);
                    return stored.trim();
                }
            }

            // 方法5: 检查页面中的JSON数据
            console.log('📄 尝试从页面JSON数据获取店铺信息...');
            const scripts = document.querySelectorAll('script');
            for (const script of scripts) {
                if (script.textContent) {
                    try {
                        // 查找包含店铺信息的JSON
                        const jsonMatches = script.textContent.match(/"shop_?name"\s*:\s*"([^"]+)"/i) ||
                                          script.textContent.match(/"store_?name"\s*:\s*"([^"]+)"/i);
                        if (jsonMatches && jsonMatches[1]) {
                            console.log('✅ 从JSON数据获取店铺名称:', jsonMatches[1]);
                            return jsonMatches[1];
                        }
                    } catch (e) {
                        // 忽略JSON解析错误
                    }
                }
            }

            console.log('⚠️ 无法获取具体店铺名称，使用默认称呼');
            return '尊贵的商家';

        } catch (error) {
            console.error('❌ 获取店铺信息时发生错误:', error);
            return '尊贵的商家';
        }
    }

    // 显示成功弹窗
    function showSuccessPopup(functionType, hasCustomerService, hasProductListing) {
        // 移除可能存在的旧弹窗
        const existingPopup = document.getElementById('success-popup');
        if (existingPopup) {
            existingPopup.remove();
        }

        // 获取店铺信息
        const shopName = getShopInfo();

        let functionName = '';
        let functionColor = '';
        let icon = '';

        if (hasCustomerService && hasProductListing) {
            functionName = '小梅花AI全功能版本';
            functionColor = '#ff6b9d';
            icon = '🌟';
        } else if (hasProductListing && !hasCustomerService) {
            functionName = '小梅花AI上架产品功能版本';
            functionColor = '#4ecdc4';
            icon = '📦';
        } else if (hasCustomerService && !hasProductListing) {
            functionName = '小梅花AI客服功能版本';
            functionColor = '#667eea';
            icon = '🤖';
        } else {
            functionName = '无功能版本';
            functionColor = '#666';
            icon = '❌';
        }

        const popup = document.createElement('div');
        popup.id = 'success-popup';
        popup.innerHTML = `
            <div class="popup-header">
                ${icon} 卡密验证成功
            </div>
            <div class="popup-body">
                <div class="welcome-message" style="
                    font-size: 18px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin: 20px 0;
                    padding: 15px;
                    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
                    border-radius: 12px;
                    border-left: 4px solid #28a745;
                    text-align: center;
                    line-height: 1.6;
                ">
                    🎉 欢迎 "<span style="color: #e74c3c; font-weight: bold;">${shopName}</span>"<br>
                    <span style="font-size: 16px; color: #6c757d;">感谢您使用小梅花智能AI客服系统</span>
                </div>
                <div class="function-info" style="color: ${functionColor};">
                    ${functionName}
                </div>
                <div class="success-text">
                    恭喜您！卡密验证通过，已成功激活对应功能。
                    <br>系统将为您的店铺提供智能化客服服务！
                </div>
                <div class="countdown" id="popup-countdown">
                    此弹窗将在 <span id="countdown-timer">5</span> 秒后自动关闭
                </div>
            </div>
        `;

        document.body.appendChild(popup);

        // 倒计时
        let countdown = 5;
        const timer = setInterval(() => {
            countdown--;
            const timerElem = document.getElementById('countdown-timer');
            if (timerElem) {
                timerElem.textContent = countdown;
            }

            if (countdown <= 0) {
                clearInterval(timer);
                // 添加淡出动画
                popup.style.animation = 'popupFadeOut 0.5s ease-in';
                setTimeout(() => {
                    if (popup.parentNode) {
                        popup.parentNode.removeChild(popup);
                    }
                }, 500);
            }
        }, 1000);

        // 点击弹窗关闭
        popup.addEventListener('click', () => {
            clearInterval(timer);
            popup.style.animation = 'popupFadeOut 0.5s ease-in';
            setTimeout(() => {
                if (popup.parentNode) {
                    popup.parentNode.removeChild(popup);
                }
            }, 500);
        });
    }

    // 【优化】验证逻辑 - 移除店铺验证相关参数
    async function verifyKey() {
        const keyInput = document.getElementById('license-key-input');
        const messageDiv = document.getElementById('auth-message');
        const verifyBtn = document.getElementById('verify-key-btn');

        if (!keyInput || !messageDiv || !verifyBtn) {
            console.error('验证界面元素未找到');
            alert('❌ 验证界面初始化失败，请刷新页面重试');
            return;
        }

        const key = keyInput.value ? keyInput.value.trim() : '';
        if (!key) {
            showMessage(messageDiv, '请输入卡密后再验证', 'error');
            keyInput.focus();
            return;
        }

        if (key.length < 6) {
            showMessage(messageDiv, '卡密格式不正确，请检查后重试', 'error');
            return;
        }

        console.log('🔑 开始验证卡密:', key.substring(0, 8) + '...');
        verifyBtn.disabled = true;
        verifyBtn.textContent = '验证中...';
        showMessage(messageDiv, '正在连接服务器...', 'info');

        const serversToTry = [
            API_CONFIG.servers.primary,
            API_CONFIG.servers.backup,
            API_CONFIG.servers.secure,
            'https://xiaomeihuakefu.cn',
            'https://api.xiaomeihuakefu.cn'
        ];
        const uniqueServers = [...new Set(serversToTry)].filter(Boolean);
        let responseData = null;
        let lastNetworkError = null;

        for (const serverUrl of uniqueServers) {
            const url = `${serverUrl}/api/verify.php`;
            console.log(`🔄 尝试验证服务器: ${url}`);
            try {
                const data = await new Promise((resolve, reject) => {
                    GM_xmlhttpRequest({
                        method: 'POST',
                        url: url,
                        headers: getApiHeaders(),
                        // 【核心优化】移除 shop_name 参数，因为后台不再需要验证
                        data: `key=${encodeURIComponent(key)}&timestamp=${Date.now()}`,
                        timeout: 10000,
                        onload: (response) => {
                            if (response.status === 200) {
                                try {
                                    const parsed = JSON.parse(response.responseText);
                                    if (parsed) {
                                        resolve(parsed);
                                    } else {
                                        reject(new Error('服务器返回空JSON'));
                                    }
                                } catch (e) {
                                    reject(new Error(`JSON解析失败: ${e.message}`));
                                }
                            } else {
                                reject(new Error(`HTTP状态 ${response.status}`));
                            }
                        },
                        onerror: (err) => reject(new Error('网络错误')),
                        ontimeout: () => reject(new Error('请求超时'))
                    });
                });

                // 收到响应，停止尝试其他服务器
                responseData = data;
                API_CONFIG.current = serverUrl;
                API_CONFIG.saveToStorage();
                console.log(`✅ 服务器 ${serverUrl} 已响应`);
                break;
            } catch (error) {
                console.warn(`⚠️ 服务器 ${serverUrl} 连接失败:`, error.message);
                lastNetworkError = error;
            }
        }

        verifyBtn.disabled = false;
        verifyBtn.textContent = '验证';

        if (responseData) {
            // 服务器已响应，处理数据
            console.log('📨 验证API响应:', responseData);
            if (responseData.success) {
                handleSuccessfulVerification(responseData, key, messageDiv);
            } else {
                handleFailedVerification(responseData, messageDiv);
            }
        } else {
            // 所有服务器都未响应
            console.error('❌ 所有服务器验证失败:', lastNetworkError);
            showMessage(messageDiv, '所有服务器都无法连接，请检查网络或稍后重试', 'error');
            performNetworkDiagnosis();
        }
    }

    // 处理成功验证
    function handleSuccessfulVerification(data, key, messageDiv) {
        console.log('验证成功');
        isAuthenticated = true;
        currentKey = key;
        GM_setValue('saved_license_key_xiaomeihua', key);

        // 保存功能类型信息和店铺信息
        if (data.function_type) {
            GM_setValue('function_type', data.function_type);
            GM_setValue('has_customer_service', data.has_customer_service);
            GM_setValue('has_product_listing', data.has_product_listing);
            console.log('💡 功能类型已保存:', data.function_type);

            // 保存店铺信息（仅用于本地显示）
            const shopName = getShopInfo();
            GM_setValue('shop_name', shopName);
            console.log('🏪 店铺信息已保存 (仅用于显示):', shopName);

            // 显示功能类型弹窗
            showSuccessPopup(data.function_type, data.has_customer_service, data.has_product_listing);
        }

        showMessage(messageDiv, data.message || '验证成功！正在加载脚本...', 'success');

        // 延长显示时间以便用户看到功能类型提示
        setTimeout(() => {
            try {
                const panel = document.getElementById('auth-panel');
                if (panel) panel.remove();

                if (!data.script || data.script.trim() === '') {
                    throw new Error('从服务器获取的脚本代码为空');
                }

                console.log('开始执行脚本...');

                // 在脚本执行前设置完整的API访问权限
                setupAPIAccess();
                testAPIAvailability();

                // 安全执行脚本
                safeExecuteScript(data.script);

                // 脚本执行后再次确保API可用
                setTimeout(() => {
                    setupAPIAccess();
                    testAPIAvailability();
                    console.log('🔄 脚本执行后API权限重新设置完成');
                }, 1000);

                isScriptLoaded = true;

                // 创建控制面板和浮动图标
                createControlPanel();
                createFloatingIcon();

                // 恢复上次的面板状态
                restorePanelState();
                
                // 确保浮动图标可见
                setTimeout(() => {
                    if (floatingIcon) {
                        console.log('🔄 验证成功后确认浮动图标可见性...');
                        floatingIcon.style.display = 'block';
                        floatingIcon.style.opacity = '1';
                        floatingIcon.style.transform = 'scale(1)';
                    }
                }, 1500);

                // 启动卡密状态监控
                startKeyStatusMonitor(key);

                // 启动API配置监控和API密钥监控
                API_CONFIG.startConfigMonitoring();
                ApiKeyManager.startMonitoring();

                console.log('手动验证成功，控制面板已创建');

            } catch (scriptError) {
                console.error('脚本执行错误:', scriptError);
                alert('❌ 脚本执行失败: ' + scriptError.message);
            }
        }, 3000);
    }

    // 处理验证失败
    function handleFailedVerification(data, messageDiv) {
        // 增强错误信息处理
        let errorMessage = data.message || '验证失败';

        // 【优化】根据错误代码提供更具体的错误信息，包括同时登录限制
        if (data.error_code) {
            switch (data.error_code) {
                case 'CONCURRENT_LOGIN_LIMIT':
                    errorMessage = '老板您好！\n\n该卡密已经在其他电脑登录，不能同时登录';
                    break;
                case 'MULTI_STORE_LOGIN_LIMIT':
                    const maxDevices = data.max_devices || '未知';
                    errorMessage = `老板您好！\n\n该多店卡密已达到最大同时登录数量限制（${maxDevices}台设备），请先退出其他设备后再登录`;
                    break;
                case 'KEY_DELETED':
                    errorMessage = '卡密不存在或已被删除，请检查卡密是否正确';
                    break;
                case 'KEY_DISABLED':
                    errorMessage = '卡密已被禁用，请联系代理商';
                    break;
                case 'KEY_EXPIRED':
                    errorMessage = '卡密已过期，请联系代理商续费';
                    break;
                case 'INVALID_KEY':
                    errorMessage = '无效的卡密格式，请检查后重试';
                    break;
                default:
                    errorMessage = data.message || '验证失败';
            }
        }

        // 检查是否为系统错误
        if (errorMessage.includes('系统错误') || errorMessage.includes('系统异常')) {
            errorMessage += '\n\n💡 可能的解决方案：\n1. 检查网络连接\n2. 稍后重试\n3. 联系技术支持';

            // 显示网络诊断信息
            console.log('🔍 开始网络诊断...');
            performNetworkDiagnosis();
        }

        showMessage(messageDiv, errorMessage, 'error');
    }

    // 安全执行脚本
    function safeExecuteScript(script) {
        try {
            // 检查是否为加密脚本（以特定标记开头）
            if (script && typeof script === 'string') {
                if (script.startsWith('(function()') && script.includes('XMHENC_')) {
                    console.log('检测到加密脚本包装器，直接执行...');
                    // 这是一个自执行的包装函数，包含解密逻辑
                    eval(script);
                    console.log('加密脚本执行成功');
                } else {
                    // 普通脚本直接执行
                    console.log('执行普通脚本...');
                    eval(script);
                    console.log('脚本执行成功');
                }
            } else {
                throw new Error('无效的脚本格式');
            }
        } catch (evalError) {
            console.error('❌ 脚本执行错误:', evalError);

            // 尝试备用执行方法
            try {
                console.log('尝试备用执行方法...');
                const scriptFunction = new Function(script);
                scriptFunction();
                console.log('备用执行方法成功');
            } catch (backupError) {
                console.error('❌ 备用执行方法也失败:', backupError);
                throw new Error('脚本执行失败: ' + evalError.message);
            }
        }
    }

    // 【新增】网络诊断函数
    function performNetworkDiagnosis() {
        console.log('🔍 执行网络诊断...');

        // 检查基本网络连接
        const testUrls = [
            'https://xiaomeihuakefu.cn',
            'http://xiaomeihuakefu.cn',
            'https://api.xiaomeihuakefu.cn',
            'https://secure.xiaomeihuakefu.cn',
            'https://www.baidu.com'
        ];

        let successCount = 0;
        let totalTests = testUrls.length;

        testUrls.forEach((url, index) => {
            // 使用fetch API进行更可靠的连接测试
            fetch(url + '/favicon.ico?' + Date.now(), {
                method: 'HEAD',
                mode: 'no-cors',  // 允许跨域请求
                cache: 'no-store'
            })
            .then(response => {
                console.log(`✅ 网络诊断 ${index + 1}: ${url} - 可访问`);
                successCount++;
                checkDiagnosisComplete();
            })
            .catch(error => {
                console.error(`❌ 网络诊断 ${index + 1}: ${url} - 不可访问`, error);
                checkDiagnosisComplete();
            });
        });

        // 【修复】检查DNS解析 - 使用 GM_xmlhttpRequest 和完整请求头
        console.log('🔍 检查DNS解析...');
        GM_xmlhttpRequest({
            method: 'GET',
            url: 'https://xiaomeihuakefu.cn/api/heartbeat.php?' + Date.now(),
            headers: getApiHeaders(),
            timeout: 5000,
            onload: function(response) {
                if (response.status === 200) {
                    console.log('✅ DNS解析正常，服务器心跳检测成功');
                    try {
                        const data = JSON.parse(response.responseText);
                        console.log('📡 服务器状态:', data);
                    } catch (e) {
                        console.log('📡 服务器返回非JSON响应');
                    }
                } else {
                    console.error('❌ DNS解析或心跳检测失败, HTTP Status: ' + response.status);
                }
            },
            onerror: function() {
                console.error('❌ DNS解析失败或服务器无响应 (onerror)');
            },
            ontimeout: function() {
                console.error('❌ DNS解析或心跳检测超时 (ontimeout)');
            }
        });


        // 检查诊断是否完成
        function checkDiagnosisComplete() {
            if (successCount + (totalTests - successCount) === totalTests) {
                console.log(`🔍 网络诊断完成: ${successCount}/${totalTests} 个测试成功`);

                if (successCount === 0) {
                    console.error('❌ 严重网络问题: 所有服务器都无法连接');
                    alert('网络诊断结果: 无法连接到任何服务器，请检查您的网络连接或防火墙设置。');
                } else if (successCount < totalTests / 2) {
                    console.warn('⚠️ 网络问题: 部分服务器无法连接');
                } else {
                    console.log('✅ 网络连接基本正常');
                }
            }
        }
    }

    // 【优化】自动验证保存的卡密 - 移除店铺验证相关参数
    async function autoVerifyKey(savedKey) {
        console.log('🔄 尝试自动验证保存的卡密...', savedKey.substring(0, 8) + '...');

        const serversToTry = [
            API_CONFIG.current,
            API_CONFIG.servers.primary,
            API_CONFIG.servers.backup,
            API_CONFIG.servers.secure,
            'https://xiaomeihuakefu.cn',
            'https://api.xiaomeihuakefu.cn'
        ];
        const uniqueServers = [...new Set(serversToTry)].filter(Boolean);
        let responseData = null;
        let lastNetworkError = null;

        for (const serverUrl of uniqueServers) {
            const url = `${serverUrl}/api/verify.php`;
            console.log(`🔄 尝试自动验证服务器: ${url}`);
            try {
                const data = await new Promise((resolve, reject) => {
                    GM_xmlhttpRequest({
                        method: 'POST',
                        url: url,
                        headers: getApiHeaders(),
                        // 【核心优化】移除 shop_name 参数，因为后台不再需要验证
                        data: `key=${encodeURIComponent(savedKey)}&timestamp=${Date.now()}`,
                        timeout: 10000,
                        onload: (response) => {
                            if (response.status === 200) {
                                try {
                                    const parsed = JSON.parse(response.responseText);
                                    if (parsed) {
                                        resolve(parsed);
                                    } else {
                                        reject(new Error('服务器返回空JSON'));
                                    }
                                } catch (e) {
                                    reject(new Error(`JSON解析失败: ${e.message}`));
                                }
                            } else {
                                reject(new Error(`HTTP状态 ${response.status}`));
                            }
                        },
                        onerror: (err) => reject(new Error('网络错误')),
                        ontimeout: () => reject(new Error('请求超时'))
                    });
                });

                responseData = data;
                API_CONFIG.current = serverUrl;
                API_CONFIG.saveToStorage();
                console.log(`✅ 服务器 ${serverUrl} 已响应 (自动验证)`);
                break;
            } catch (error) {
                console.warn(`⚠️ 服务器 ${serverUrl} 连接失败 (自动验证):`, error.message);
                lastNetworkError = error;
            }
        }

        if (responseData) {
            if (responseData.success) {
                console.log('自动验证成功');
                isAuthenticated = true;
                currentKey = savedKey;

                if (responseData.script && responseData.script.trim() !== '') {
                    try {
                        if (responseData.function_type) {
                            GM_setValue('function_type', responseData.function_type);
                            GM_setValue('has_customer_service', responseData.has_customer_service);
                            GM_setValue('has_product_listing', responseData.has_product_listing);
                            const shopName = getShopInfo(); // 获取店铺名称
                            GM_setValue('shop_name', shopName); // 仅保存用于本地显示
                            showSuccessPopup(responseData.function_type, responseData.has_customer_service, responseData.has_product_listing);
                        }
                        console.log('自动验证成功，开始执行脚本...');
                        setupAPIAccess();
                        testAPIAvailability();
                        safeExecuteScript(responseData.script);
                        setTimeout(() => {
                            setupAPIAccess();
                            testAPIAvailability();
                            console.log('�� 自动验证脚本执行后API权限重新设置完成');
                        }, 1000);
                        isScriptLoaded = true;
                        createControlPanel();
                        createFloatingIcon();
                        
                        // 确保UI元素显示正确
                        console.log('🔄 强制执行UI状态恢复...');
                        restorePanelState();
                        
                        // 再次确保浮动图标可见
                        setTimeout(() => {
                            if (floatingIcon) {
                                console.log(' 二次确认浮动图标可见性...');
                                floatingIcon.style.display = 'block';
                                floatingIcon.style.opacity = '1';
                                floatingIcon.style.transform = 'scale(1)';
                            }
                        }, 1000);
                        
                        startKeyStatusMonitor(savedKey);
                        API_CONFIG.startConfigMonitoring();
                        ApiKeyManager.startMonitoring();
                        console.log('自动验证成功，控制面板已创建');
                    } catch (scriptError) {
                        console.error('自动验证脚本执行错误:', scriptError);
                        createUI();
                    }
                } else {
                    console.error('自动验证：脚本代码为空');
                    createUI();
                }
            } else {
                console.error('自动验证失败 (服务器响应):', responseData.message);
                GM_deleteValue('saved_license_key_xiaomeihua');
                createUI();
            }
        } else {
            console.error('❌ 自动验证网络失败:', lastNetworkError);
            GM_deleteValue('saved_license_key_xiaomeihua');
            createUI();
            performNetworkDiagnosis();
        }
    }


    // 【新增】添加DOM监视器，确保在页面变化时重新显示UI
    function setupDOMObserver() {
        console.log('🔍 设置DOM变化监视器...');
        
        // 创建一个观察器实例
        const observer = new MutationObserver((mutations) => {
            // 检查是否有重要的DOM变化
            const significantChange = mutations.some(mutation => {
                // 检查是否添加了新节点
                if (mutation.addedNodes && mutation.addedNodes.length) {
                    // 检查是否是重要的节点（如微信商店的主要容器）
                    return Array.from(mutation.addedNodes).some(node => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            // 检查是否是重要的元素
                            return (
                                node.classList && 
                                (node.classList.contains('shop-container') || 
                                 node.classList.contains('kf-container') ||
                                 node.classList.contains('main-container') ||
                                 node.id === 'app' ||
                                 node.id === 'root')
                            );
                        }
                        return false;
                    });
                }
                return false;
            });
            
            // 如果有重要变化，检查并恢复UI
            if (significantChange) {
                console.log('🔄 检测到重要DOM变化，检查UI状态...');
                checkAndRestoreUI();
            }
        });
        
        // 配置观察选项
        const config = { 
            childList: true,     // 观察目标子节点的变化
            subtree: true,       // 观察所有后代节点
            attributes: false,   // 不观察属性变化
            characterData: false // 不观察文本变化
        };
        
        // 开始观察文档
        observer.observe(document.body, config);
        console.log('✅ DOM变化监视器已设置');
        
        // 返回观察器以便可以停止
        return observer;
    }

    // 【新增】检查并恢复UI元素
    function checkAndRestoreUI() {
        console.log('🔍 检查UI元素状态...');
        
        // 检查浮动图标是否存在且可见
        const iconExists = document.getElementById('floating-icon');
        const panelExists = document.getElementById('control-panel');
        
        console.log('UI元素检查:', {
            iconExists: !!iconExists,
            iconVisible: iconExists ? (iconExists.style.display !== 'none' && iconExists.style.opacity !== '0') : false,
            panelExists: !!panelExists
        });
        
        // 如果浮动图标不存在或不可见，尝试恢复
        if (!iconExists || (iconExists.style.display === 'none' || iconExists.style.opacity === '0')) {
            console.log('🔄 浮动图标不存在或不可见，尝试恢复...');
            
            // 如果图标不存在，创建新的
            if (!iconExists) {
                console.log('创建新的浮动图标');
                createFloatingIcon();
            } else {
                // 如果图标存在但不可见，强制显示
                console.log('强制显示现有浮动图标');
                iconExists.style.display = 'block';
                iconExists.style.opacity = '1';
                iconExists.style.transform = 'scale(1)';
                iconExists.style.zIndex = '100000'; // 确保最高层级
            }
        }
        
        // 如果控制面板不存在，创建新的
        if (!panelExists) {
            console.log('创建新的控制面板');
            createControlPanel();
            // 创建后立即最小化，确保显示浮动图标
            setTimeout(() => {
                minimizeControlPanel();
            }, 500);
        }
    }

    // 【修改】main函数，添加DOM监视器
    async function main() {
        console.log('调试版加载器启动...');

        try {
            // 【新增】初始化API配置系统
            console.log('开始初始化API配置系统...');
            await API_CONFIG.init();
            console.log('API配置系统初始化完成');

            // 设置DOM监视器
            const observer = setupDOMObserver();
            
            // 保存到window对象以防止被垃圾回收
            window._xiaomeihuaDOMObserver = observer;

            const savedKey = GM_getValue('saved_license_key_xiaomeihua', null);
            if (savedKey) {
                console.log('发现保存的卡密，尝试自动验证...');
                // 设置当前卡密以便配置系统使用
                currentKey = savedKey;
                await autoVerifyKey(savedKey);
                
                // 确保UI元素正确显示
                setTimeout(() => {
                    console.log('🔄 验证后检查UI元素显示状态...');
                    checkAndRestoreUI();
                    
                    // 再次延迟检查，确保在页面完全加载后UI元素可见
                    setTimeout(checkAndRestoreUI, 5000);
                    setTimeout(checkAndRestoreUI, 10000);
                }, 2000);
            } else {
                console.log('未发现保存的卡密，显示验证界面...');
                createUI();
            }
        } catch (initError) {
            console.error('初始化过程中出现错误:', initError);

            // 尝试使用默认配置
            console.log('尝试使用默认配置...');
            API_CONFIG.current = API_CONFIG.servers.primary;

            // 显示验证界面
            createUI();

            // 显示错误提示
            setTimeout(() => {
                alert('初始化过程中遇到问题，部分功能可能不可用。请刷新页面重试。');
            }, 1000);
        }
    }

    // 【修改】立即执行初始化
    ensureDOMLoaded(initializeScript);
    
    // 【修改】导出关键函数到全局作用域，便于调试和手动调用
    window.xiaomeihuaDebug = {
        createFloatingIcon,
        createControlPanel,
        restorePanelState,
        main,
        initializeScript
    };
})();
}, seed)}
//# sourceURL=chrome-extension://dhdgffkkebhmkfjojejmpbldmpobfkfo/userscript.html?name=%25E5%25B0%258F%25E6%25A2%2585%25E8%258A%25B1AI%25E5%25AE%25A2%25E6%259C%258D%25E5%258A%25A9%25E6%2589%258B.user.js&id=dffcffcf-d48a-41b9-abd0-3c0bae8fa211